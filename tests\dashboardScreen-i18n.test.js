/**
 * DashboardScreen i18n Implementation Test
 * 
 * Tests to verify that DashboardScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing DashboardScreen.js i18n Implementation\n');

// Read the DashboardScreen.js file
const dashboardScreenPath = path.join(__dirname, '../screens/DashboardScreen.js');
const dashboardScreenContent = fs.readFileSync(dashboardScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = dashboardScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = dashboardScreenContent.includes('const { t }') || dashboardScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded strings that should have been replaced
console.log('\n✅ Test 3: Hardcoded Alert.alert strings replacement');
const hardcodedAlertStrings = [
  'Investments',
  'Investment features are now available!',
  'Financial Planning', 
  'Financial planning tools are now available!',
  'Test Features',
  'Test screen navigation failed'
];

let hardcodedAlertsFound = [];
hardcodedAlertStrings.forEach(str => {
  // Check if the string appears in Alert.alert calls (not in translation keys)
  const alertPattern = new RegExp(`Alert\\.alert\\([^)]*['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (alertPattern.test(dashboardScreenContent)) {
    hardcodedAlertsFound.push(str);
  }
});

if (hardcodedAlertsFound.length === 0) {
  console.log('   All hardcoded Alert.alert strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Alert.alert strings still found: ❌ FAIL');
  hardcodedAlertsFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 4: Check for hardcoded Text component strings
console.log('\n✅ Test 4: Hardcoded Text component strings');
const hardcodedTextStrings = [
  'Enhanced'
];

let hardcodedTextFound = [];
hardcodedTextStrings.forEach(str => {
  // Check if the string appears in Text components (not in translation keys)
  const textPattern = new RegExp(`<Text[^>]*>\\s*${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*</Text>`, 'g');
  if (textPattern.test(dashboardScreenContent)) {
    hardcodedTextFound.push(str);
  }
});

if (hardcodedTextFound.length === 0) {
  console.log('   All hardcoded Text strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Text strings still found: ❌ FAIL');
  hardcodedTextFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 5: Check for proper translation key usage
console.log('\n✅ Test 5: Translation key usage');
const translationKeys = [
  't(\'dashboard.alerts.investments\')',
  't(\'dashboard.alerts.investmentFeaturesAvailable\')',
  't(\'dashboard.alerts.financialPlanning\')',
  't(\'dashboard.alerts.financialPlanningToolsAvailable\')',
  't(\'dashboard.alerts.testFeatures\')',
  't(\'dashboard.alerts.testScreenNavigationFailed\')',
  't(\'dashboard.enhanced\')'
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (dashboardScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check that required translation keys exist in en.js
console.log('\n✅ Test 6: Translation keys in en.js');
const requiredKeys = [
  'investments:',
  'investmentFeaturesAvailable:',
  'financialPlanning:',
  'financialPlanningToolsAvailable:',
  'testFeatures:',
  'testScreenNavigationFailed:',
  'enhanced:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for Alert.alert with proper translation keys
console.log('\n✅ Test 7: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = dashboardScreenContent.match(alertPattern);
const totalAlerts = (dashboardScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 8: Check for dashboard-specific translation usage
console.log('\n✅ Test 8: Dashboard-specific translations');
const dashboardTranslations = [
  't(\'dashboard.welcome\')',
  't(\'dashboard.quickActionsTitle\')',
  't(\'dashboard.financialInsights\')',
  't(\'dashboard.recentTransactions\')',
  't(\'dashboard.viewAll\')'
];

let dashboardKeysFound = 0;
dashboardTranslations.forEach(key => {
  if (dashboardScreenContent.includes(key)) {
    dashboardKeysFound++;
  }
});

console.log(`   Dashboard translation keys found: ${dashboardKeysFound}/${dashboardTranslations.length} ${dashboardKeysFound === dashboardTranslations.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 8;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (hardcodedAlertsFound.length === 0) passedTests++;
if (hardcodedTextFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (dashboardKeysFound === dashboardTranslations.length) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 DashboardScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
} else {
  console.log('\n⚠️  DashboardScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test dashboard with different languages');
console.log('2. Verify alert messages display correctly in multiple languages');
console.log('3. Test quick actions and financial insights sections');
console.log('4. Proceed to WalletScreen.js implementation');
