import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import privacyManagementService from '../services/privacyManagementService';
import consentEnforcementService from '../services/consentEnforcementService';
import authService from '../services/authService';

const PrivacyControlsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [privacySettings, setPrivacySettings] = useState(null);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [exportFormat, setExportFormat] = useState('json');

  useEffect(() => {
    loadPrivacyData();
  }, []);

  const loadPrivacyData = async () => {
    try {
      setLoading(true);
      
      const currentUser = authService.getCurrentUser();
      if (currentUser) {
        setUser(currentUser);
        
        const settingsResult = await privacyManagementService.getPrivacySettings(currentUser.id);
        if (settingsResult.success) {
          setPrivacySettings(settingsResult.data);
        }
      }
    } catch (error) {
      console.error('❌ Error loading privacy data:', error);
      Alert.alert(t('common.error'), t('settings.failedToLoadPrivacySettings'));
    } finally {
      setLoading(false);
    }
  };

  const handleConsentToggle = async (consentType, enabled) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Show confirmation for consent revocation
      if (!enabled && consentType !== 'essential') {
        Alert.alert(
          t('settings.revokeConsent'),
          t('settings.revokeConsentConfirmation', { consentType: getConsentDisplayName(consentType) }),
          [
            { text: t('common.cancel'), style: 'cancel' },
            {
              text: t('common.confirm'),
              style: 'destructive',
              onPress: () => updateConsentSetting(consentType, enabled)
            }
          ]
        );
      } else {
        await updateConsentSetting(consentType, enabled);
      }
    } catch (error) {
      console.error('❌ Error updating consent:', error);
      Alert.alert('Error', 'Failed to update consent settings');
    }
  };

  const updateConsentSetting = async (consentType, enabled) => {
    try {
      // Use consent enforcement service for proper handling
      const result = await consentEnforcementService.updateConsent(user.id, consentType, enabled);

      if (result.success) {
        setPrivacySettings(prev => ({
          ...prev,
          consent_given: {
            ...prev.consent_given,
            [consentType]: enabled
          }
        }));

        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        // Show confirmation message
        const action = enabled ? 'enabled' : 'disabled';
        Alert.alert(
          'Consent Updated',
          `${getConsentDisplayName(consentType)} has been ${action}.`,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('❌ Error updating consent:', error);
      Alert.alert('Error', 'Failed to update consent settings');
    }
  };

  const getConsentDisplayName = (consentType) => {
    const names = {
      essential: 'Essential Services',
      analytics: 'Analytics & Performance',
      marketing: 'Marketing Communications',
      dataSharing: 'Data Sharing',
      locationTracking: 'Location Services'
    };
    return names[consentType] || consentType;
  };

  const handlePrivacySettingToggle = async (setting, enabled) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      const result = await privacyManagementService.updatePrivacySettings(user.id, {
        [setting]: enabled
      });
      
      if (result.success) {
        setPrivacySettings(prev => ({
          ...prev,
          [setting]: enabled
        }));
        
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('❌ Error updating privacy setting:', error);
      Alert.alert('Error', 'Failed to update privacy settings');
    }
  };

  const handleDataExport = async () => {
    try {
      setExporting(true);
      
      const result = await privacyManagementService.exportUserData(user.id, exportFormat);
      
      if (result.success) {
        setShowExportModal(false);
        
        Alert.alert(
          'Export Complete',
          `Your data has been exported successfully. File size: ${(result.data.fileSize / 1024).toFixed(1)} KB`,
          [
            { text: 'OK' },
            {
              text: 'Share',
              onPress: () => privacyManagementService.shareExportedData(result.data.filePath)
            }
          ]
        );
        
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        Alert.alert('Export Failed', result.error);
      }
    } catch (error) {
      console.error('❌ Error exporting data:', error);
      Alert.alert('Error', 'Failed to export data');
    } finally {
      setExporting(false);
    }
  };

  const handleAccountDeletion = () => {
    Alert.alert(
      t('settings.deleteAccount'),
      t('settings.deleteAccountConfirmation'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('settings.delete'),
          style: 'destructive',
          onPress: () => setShowDeleteModal(true)
        }
      ]
    );
  };

  const confirmAccountDeletion = async () => {
    try {
      const result = await privacyManagementService.requestAccountDeletion(user.id, 'User requested deletion');
      
      if (result.success) {
        setShowDeleteModal(false);
        
        Alert.alert(
          'Account Deletion Requested',
          result.data.message,
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
        
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('❌ Error requesting account deletion:', error);
      Alert.alert('Error', 'Failed to request account deletion');
    }
  };

  const renderConsentOption = (consentType, title, description, required = false) => (
    <View style={[styles.consentOption, required && styles.requiredOption]}>
      <View style={styles.consentLeft}>
        <View style={styles.consentText}>
          <View style={styles.consentTitleRow}>
            <Text style={styles.consentTitle}>{title}</Text>
            {required && <Text style={styles.requiredBadge}>Required</Text>}
          </View>
          <Text style={styles.consentDescription}>{description}</Text>
        </View>
      </View>
      <Switch
        value={privacySettings?.consent_given?.[consentType] || (required ? true : false)}
        onValueChange={(enabled) => !required && handleConsentToggle(consentType, enabled)}
        trackColor={{ false: Colors.neutral.lightGray, true: Colors.primary.main + '40' }}
        thumbColor={privacySettings?.consent_given?.[consentType] || (required ? true : false) ? Colors.primary.main : Colors.neutral.warmGray}
        disabled={required}
      />
    </View>
  );

  const renderPrivacyOption = (icon, title, subtitle, setting, iconColor) => (
    <View style={styles.privacyOption}>
      <View style={styles.optionLeft}>
        <View style={[styles.optionIcon, { backgroundColor: iconColor + '20' }]}>
          <Ionicons name={icon} size={24} color={iconColor} />
        </View>
        <View style={styles.optionText}>
          <Text style={styles.optionTitle}>{title}</Text>
          <Text style={styles.optionSubtitle}>{subtitle}</Text>
        </View>
      </View>
      <Switch
        value={privacySettings?.[setting] || false}
        onValueChange={(enabled) => handlePrivacySettingToggle(setting, enabled)}
        trackColor={{ false: Colors.neutral.lightGray, true: Colors.primary.main + '40' }}
        thumbColor={privacySettings?.[setting] ? Colors.primary.main : Colors.neutral.warmGray}
      />
    </View>
  );

  const renderActionOption = (icon, title, subtitle, onPress, iconColor, destructive = false) => (
    <TouchableOpacity 
      style={[styles.actionOption, destructive && styles.destructiveOption]} 
      onPress={onPress} 
      activeOpacity={0.7}
    >
      <View style={styles.optionLeft}>
        <View style={[styles.optionIcon, { backgroundColor: iconColor + '20' }]}>
          <Ionicons name={icon} size={24} color={iconColor} />
        </View>
        <View style={styles.optionText}>
          <Text style={[styles.optionTitle, destructive && styles.destructiveText]}>{title}</Text>
          <Text style={styles.optionSubtitle}>{subtitle}</Text>
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary.main} />
          <Text style={styles.loadingText}>Loading privacy settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Modern Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>Privacy & Data</Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={styles.headerSubtitle}>
          Control how your data is used and shared
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Data Consent */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Consent</Text>
          <Text style={styles.sectionDescription}>
            Choose what data you're comfortable sharing with us
          </Text>
          
          {renderConsentOption(
            'essential',
            'Essential Services',
            'Required for core app functionality and security',
            true
          )}
          
          {renderConsentOption(
            'analytics',
            'Analytics & Performance',
            'Help us improve the app by sharing usage data'
          )}
          
          {renderConsentOption(
            'marketing',
            'Marketing Communications',
            'Receive personalized offers and financial tips'
          )}
          
          {renderConsentOption(
            'dataSharing',
            'Data Sharing',
            'Share anonymized data with trusted partners'
          )}

          {renderConsentOption(
            'locationTracking',
            'Location Services',
            'Use location data for enhanced security and services'
          )}
        </View>

        {/* Communication Preferences */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Communication Preferences</Text>
          
          {renderPrivacyOption(
            'mail',
            'Email Marketing',
            'Receive promotional emails and newsletters',
            'marketing_emails',
            Colors.primary.main
          )}
          
          {renderPrivacyOption(
            'chatbubble',
            'SMS Marketing',
            'Receive promotional SMS messages',
            'marketing_sms',
            Colors.secondary.lake
          )}
        </View>

        {/* Data Rights */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Your Data Rights</Text>
          
          {renderActionOption(
            'download',
            'Export My Data',
            'Download a copy of all your data',
            () => setShowExportModal(true),
            Colors.accent.gold
          )}
          
          {renderActionOption(
            'document-text',
            'Privacy Policy',
            'Read our comprehensive privacy policy',
            () => navigation.navigate('PrivacyPolicy'),
            Colors.secondary.forest
          )}

          {renderActionOption(
            'shield-checkmark',
            'Data Protection',
            'Learn about your data rights and protection',
            () => navigation.navigate('DataProtection'),
            Colors.primary.main
          )}
        </View>

        {/* Danger Zone */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, styles.dangerTitle]}>Danger Zone</Text>
          
          {renderActionOption(
            'trash',
            'Delete Account',
            'Permanently delete your account and all data',
            handleAccountDeletion,
            Colors.status.error,
            true
          )}
        </View>
      </ScrollView>

      {/* Export Modal */}
      <Modal
        visible={showExportModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowExportModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowExportModal(false)}>
              <Text style={styles.modalCancel}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Export Data</Text>
            <TouchableOpacity onPress={handleDataExport} disabled={exporting}>
              <Text style={[styles.modalSave, exporting && styles.disabledText]}>
                {exporting ? 'Exporting...' : 'Export'}
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.modalContent}>
            <Text style={styles.exportDescription}>
              Choose the format for your data export. This will include your profile, 
              transaction history, and privacy settings.
            </Text>
            
            <View style={styles.formatOptions}>
              <TouchableOpacity 
                style={[styles.formatOption, exportFormat === 'json' && styles.selectedFormat]}
                onPress={() => setExportFormat('json')}
              >
                <Text style={styles.formatTitle}>JSON Format</Text>
                <Text style={styles.formatSubtitle}>Machine-readable format</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.formatOption, exportFormat === 'csv' && styles.selectedFormat]}
                onPress={() => setExportFormat('csv')}
              >
                <Text style={styles.formatTitle}>CSV Format</Text>
                <Text style={styles.formatSubtitle}>Spreadsheet-friendly format</Text>
              </TouchableOpacity>
            </View>
            
            {exporting && (
              <View style={styles.exportingContainer}>
                <ActivityIndicator size="large" color={Colors.primary.main} />
                <Text style={styles.exportingText}>Preparing your data export...</Text>
              </View>
            )}
          </View>
        </SafeAreaView>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={showDeleteModal}
        animationType="fade"
        transparent={true}
        onRequestClose={() => setShowDeleteModal(false)}
      >
        <View style={styles.deleteModalOverlay}>
          <View style={styles.deleteModalContent}>
            <Ionicons name="warning" size={48} color={Colors.status.error} />
            <Text style={styles.deleteModalTitle}>Delete Account?</Text>
            <Text style={styles.deleteModalText}>
              This will permanently delete your account and all associated data. 
              This action cannot be undone.
            </Text>
            
            <View style={styles.deleteModalButtons}>
              <TouchableOpacity 
                style={styles.deleteModalCancel}
                onPress={() => setShowDeleteModal(false)}
              >
                <Text style={styles.deleteModalCancelText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.deleteModalConfirm}
                onPress={confirmAccountDeletion}
              >
                <Text style={styles.deleteModalConfirmText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  placeholder: {
    width: 40,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.background,
    marginTop: -15,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 15,
  },
  dangerTitle: {
    color: Colors.status.error,
  },
  consentOption: {
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  requiredOption: {
    borderLeftWidth: 4,
    borderLeftColor: Colors.primary.main,
  },
  consentLeft: {
    flex: 1,
  },
  consentText: {
    flex: 1,
  },
  consentTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  consentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginRight: 8,
  },
  requiredBadge: {
    fontSize: 10,
    fontWeight: 'bold',
    color: Colors.primary.main,
    backgroundColor: Colors.primary.main + '20',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    textTransform: 'uppercase',
  },
  consentDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  privacyOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  destructiveOption: {
    borderColor: Colors.status.error,
    borderWidth: 1,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionText: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  destructiveText: {
    color: Colors.status.error,
  },
  optionSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalCancel: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  modalSave: {
    fontSize: 16,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  disabledText: {
    opacity: 0.5,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  exportDescription: {
    fontSize: 16,
    color: theme.colors.text,
    marginBottom: 20,
    lineHeight: 24,
  },
  formatOptions: {
    marginBottom: 20,
  },
  formatOption: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.colors.border,
    marginBottom: 12,
  },
  selectedFormat: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary.main + '10',
  },
  formatTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  formatSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  exportingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  exportingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  deleteModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  deleteModalContent: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    maxWidth: 320,
    width: '100%',
  },
  deleteModalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  deleteModalText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  deleteModalButtons: {
    flexDirection: 'row',
    width: '100%',
  },
  deleteModalCancel: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    backgroundColor: theme.colors.border,
    marginRight: 8,
  },
  deleteModalCancelText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    textAlign: 'center',
  },
  deleteModalConfirm: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    backgroundColor: Colors.status.error,
    marginLeft: 8,
  },
  deleteModalConfirmText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.white,
    textAlign: 'center',
  },
});

export default PrivacyControlsScreen;
