import authService from './authService';
import walletService from './walletService';
import sendMoneyService from './sendMoneyService';
import qrCodeService from './qrCodeService';
import productionTransactionService from './productionTransactionService';

/**
 * QR Payment Processing Service
 * Handles QR-based payment processing with security, validation, and integration
 */
class QRPaymentService {
  constructor() {
    this.processingQueue = new Map();
    this.recentTransactions = new Map();
    
    // Security settings
    this.securitySettings = {
      maxConcurrentPayments: 3,
      duplicateTransactionWindow: 30000, // 30 seconds
      requireBiometricAbove: 100000, // 100k UGX
      requirePINAbove: 50000, // 50k UGX
      maxDailyQRPayments: 20,
    };

    // Payment limits
    this.paymentLimits = {
      minAmount: 100, // 100 UGX
      maxAmount: 5000000, // 5M UGX
      dailyLimit: 10000000, // 10M UGX
    };
  }

  /**
   * Process QR payment with comprehensive validation
   */
  async processQRPayment(qrData, paymentDetails = {}) {
    try {
      console.log('💳 Processing QR payment:', {
        type: qrData.type,
        amount: qrData.amount,
        recipient: qrData.user?.name
      });

      // Get current user
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        return { success: false, error: 'User not authenticated' };
      }

      // Prevent duplicate processing
      const duplicateCheck = this.checkDuplicateTransaction(qrData, currentUser.id);
      if (!duplicateCheck.success) {
        return duplicateCheck;
      }

      // Validate payment amount
      const amount = qrData.amount || paymentDetails.amount;
      if (!amount || amount <= 0) {
        return { success: false, error: 'Invalid payment amount' };
      }

      // Security validations
      const securityCheck = await this.validatePaymentSecurity(qrData, amount, currentUser);
      if (!securityCheck.success) {
        return securityCheck;
      }

      // Check wallet balance
      const balanceCheck = await this.validateWalletBalance(currentUser.id, amount);
      if (!balanceCheck.success) {
        return balanceCheck;
      }

      // Check payment limits
      const limitsCheck = await this.checkPaymentLimits(currentUser.id, amount);
      if (!limitsCheck.success) {
        return limitsCheck;
      }

      // Create recipient object for payment processing
      const recipient = {
        id: qrData.user.id,
        name: qrData.user.name,
        phoneNumber: qrData.user.phone,
        source: 'qr_payment',
        qrData: qrData,
      };

      // Calculate fees
      const feeCalculation = sendMoneyService.calculateTransferFee(amount);

      // Process payment through existing send money service
      const transferData = {
        userId: currentUser.id,
        recipient: recipient,
        amount: amount,
        purpose: qrData.purpose || paymentDetails.purpose || 'QR Payment',
        feeCalculation: feeCalculation,
        metadata: {
          qrReference: qrData.reference,
          qrType: qrData.type,
          paymentMethod: 'qr_scan',
          timestamp: new Date().toISOString(),
        }
      };

      // Add to processing queue
      const processingId = this.addToProcessingQueue(qrData, currentUser.id);

      try {
        const result = await sendMoneyService.executeTransfer(transferData);
        
        if (result.success) {
          // Record successful QR payment
          await this.recordQRPayment(qrData, result.transaction, currentUser.id);
          
          console.log('✅ QR payment processed successfully:', {
            reference: result.reference,
            amount: amount,
            recipient: recipient.name
          });

          return {
            success: true,
            transaction: result.transaction,
            reference: result.reference,
            amount: amount,
            recipient: recipient,
            qrReference: qrData.reference,
            message: `Payment of UGX ${amount.toLocaleString()} sent successfully to ${recipient.name}`,
            paymentMethod: 'qr_scan'
          };
        } else {
          return { success: false, error: result.error || 'Payment processing failed' };
        }
      } finally {
        // Remove from processing queue
        this.removeFromProcessingQueue(processingId);
      }

    } catch (error) {
      console.error('❌ QR payment processing error:', error);
      return { success: false, error: 'Payment processing failed. Please try again.' };
    }
  }

  /**
   * Validate payment security requirements
   */
  async validatePaymentSecurity(qrData, amount, currentUser) {
    try {
      // Check if user is trying to pay themselves
      if (qrData.user.id === currentUser.id) {
        return { success: false, error: 'You cannot pay yourself' };
      }

      // Check QR expiration
      if (qrData.expires) {
        const expirationDate = new Date(qrData.expires);
        if (expirationDate < new Date()) {
          return { success: false, error: 'QR code has expired. Please request a new QR code.' };
        }
      }

      // Check concurrent payments
      const concurrentPayments = Array.from(this.processingQueue.values())
        .filter(payment => payment.userId === currentUser.id).length;

      if (concurrentPayments >= this.securitySettings.maxConcurrentPayments) {
        return { success: false, error: 'Too many concurrent payments. Please wait for current payments to complete.' };
      }

      // Check if additional security is required
      const securityRequirements = this.getSecurityRequirements(amount);
      
      return { 
        success: true, 
        securityRequirements: securityRequirements 
      };
    } catch (error) {
      console.error('Payment security validation error:', error);
      return { success: false, error: 'Security validation failed' };
    }
  }

  /**
   * Get security requirements based on amount
   */
  getSecurityRequirements(amount) {
    return {
      requiresPIN: amount >= this.securitySettings.requirePINAbove,
      requiresBiometric: amount >= this.securitySettings.requireBiometricAbove,
      requiresConfirmation: true,
      amount: amount
    };
  }

  /**
   * Validate wallet balance for payment
   */
  async validateWalletBalance(userId, amount) {
    try {
      const wallet = await walletService.getWalletBalance(userId);
      if (!wallet.success) {
        return { success: false, error: 'Unable to verify wallet balance' };
      }

      const feeCalculation = sendMoneyService.calculateTransferFee(amount);
      const totalRequired = feeCalculation.total;

      if (wallet.data.balance < totalRequired) {
        return { 
          success: false, 
          error: `Insufficient balance. You need UGX ${totalRequired.toLocaleString()} (including fees)`,
          requiredAmount: totalRequired,
          currentBalance: wallet.data.balance,
          shortfall: totalRequired - wallet.data.balance
        };
      }

      return { 
        success: true, 
        balance: wallet.data.balance,
        feeCalculation: feeCalculation 
      };
    } catch (error) {
      console.error('Wallet balance validation error:', error);
      return { success: false, error: 'Unable to verify wallet balance' };
    }
  }

  /**
   * Check payment limits
   */
  async checkPaymentLimits(userId, amount) {
    try {
      // Check minimum amount
      if (amount < this.paymentLimits.minAmount) {
        return { 
          success: false, 
          error: `Minimum payment amount is UGX ${this.paymentLimits.minAmount.toLocaleString()}` 
        };
      }

      // Check maximum amount
      if (amount > this.paymentLimits.maxAmount) {
        return { 
          success: false, 
          error: `Maximum payment amount is UGX ${this.paymentLimits.maxAmount.toLocaleString()}` 
        };
      }

      // Check daily limits (simplified implementation)
      const today = new Date().toDateString();
      const dailyKey = `${userId}_${today}`;
      const dailySpent = this.getDailySpent(dailyKey);

      if (dailySpent + amount > this.paymentLimits.dailyLimit) {
        return { 
          success: false, 
          error: `Daily payment limit exceeded. Remaining: UGX ${(this.paymentLimits.dailyLimit - dailySpent).toLocaleString()}` 
        };
      }

      return { success: true };
    } catch (error) {
      console.error('Payment limits check error:', error);
      return { success: false, error: 'Unable to verify payment limits' };
    }
  }

  /**
   * Check for duplicate transactions
   */
  checkDuplicateTransaction(qrData, userId) {
    try {
      const transactionKey = `${userId}_${qrData.reference || qrData.user.id}_${qrData.amount}`;
      const now = Date.now();
      
      const recentTransaction = this.recentTransactions.get(transactionKey);
      if (recentTransaction && (now - recentTransaction.timestamp) < this.securitySettings.duplicateTransactionWindow) {
        return { 
          success: false, 
          error: 'Duplicate transaction detected. Please wait before trying again.' 
        };
      }

      // Record this transaction attempt
      this.recentTransactions.set(transactionKey, { timestamp: now });

      // Cleanup old entries
      this.cleanupRecentTransactions();

      return { success: true };
    } catch (error) {
      console.error('Duplicate transaction check error:', error);
      return { success: true }; // Don't block on this check
    }
  }

  /**
   * Add payment to processing queue
   */
  addToProcessingQueue(qrData, userId) {
    const processingId = `${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    
    this.processingQueue.set(processingId, {
      qrData: qrData,
      userId: userId,
      timestamp: Date.now(),
    });

    return processingId;
  }

  /**
   * Remove payment from processing queue
   */
  removeFromProcessingQueue(processingId) {
    this.processingQueue.delete(processingId);
  }

  /**
   * Record successful QR payment
   */
  async recordQRPayment(qrData, transaction, userId) {
    try {
      // In production, save to database
      console.log('📝 Recording QR payment:', {
        qrReference: qrData.reference,
        transactionId: transaction.id,
        userId: userId,
        amount: qrData.amount
      });

      // Update daily spending
      const today = new Date().toDateString();
      const dailyKey = `${userId}_${today}`;
      const currentSpent = this.getDailySpent(dailyKey);
      this.setDailySpent(dailyKey, currentSpent + (qrData.amount || 0));

    } catch (error) {
      console.error('Record QR payment error:', error);
    }
  }

  /**
   * Get daily spent amount (simplified implementation)
   */
  getDailySpent(dailyKey) {
    // In production, get from database
    return 0;
  }

  /**
   * Set daily spent amount (simplified implementation)
   */
  setDailySpent(dailyKey, amount) {
    // In production, save to database
  }

  /**
   * Cleanup old transaction records
   */
  cleanupRecentTransactions() {
    const now = Date.now();
    const maxAge = this.securitySettings.duplicateTransactionWindow * 2;

    for (const [key, transaction] of this.recentTransactions.entries()) {
      if (now - transaction.timestamp > maxAge) {
        this.recentTransactions.delete(key);
      }
    }
  }

  /**
   * Get payment status
   */
  getPaymentStatus(processingId) {
    return this.processingQueue.has(processingId) ? 'processing' : 'completed';
  }
}

export default new QRPaymentService();
