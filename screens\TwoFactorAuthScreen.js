import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import securityManagementService from '../services/securityManagementService';
import authService from '../services/authService';
import communicationService from '../services/communicationService';
import profileManagementService from '../services/profileManagementService';

const TwoFactorAuthScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [securitySettings, setSecuritySettings] = useState(null);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState('sms');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [inputCode, setInputCode] = useState('');
  const [showVerification, setShowVerification] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [verifyingCode, setVerifyingCode] = useState(false);

  useEffect(() => {
    loadTwoFactorData();
  }, []);

  const loadTwoFactorData = async () => {
    try {
      setLoading(true);

      const currentUser = authService.getCurrentUser();
      if (currentUser) {
        setUser(currentUser);

        // Load user profile for phone and email
        const profileResult = await profileManagementService.getProfile(currentUser.id);
        if (profileResult.success) {
          setUserProfile(profileResult.data);
          setPhoneNumber(profileResult.data.phone_number || '');
          setEmail(profileResult.data.email || '');
        }

        const settingsResult = await securityManagementService.getSecuritySettings(currentUser.id);
        if (settingsResult.success) {
          setSecuritySettings(settingsResult.data);
          setTwoFactorEnabled(settingsResult.data.two_factor_enabled || false);
          setSelectedMethod(settingsResult.data.two_factor_method || 'sms');
        }
      }
    } catch (error) {
      console.error('❌ Error loading 2FA data:', error);
      Alert.alert(t('error'), t('failedToLoadTwofactorAuthenticationSettings'));
    } finally {
      setLoading(false);
    }
  };

  const handleToggle2FA = async (enabled) => {
    try {
      if (enabled) {
        // Enable 2FA - show verification
        setShowVerification(true);
        sendVerificationCode();
      } else {
        // Disable 2FA
        const result = await securityManagementService.updateSecuritySettings(user.id, {
          two_factor_enabled: false,
          two_factor_method: null
        });

        if (result.success) {
          setTwoFactorEnabled(false);
          setShowVerification(false);
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          Alert.alert(t('success'), t('twofactorAuthenticationDisabled'));
        } else {
          Alert.alert(t('error'), t('failedToDisableTwofactorAuthentication'));
        }
      }
    } catch (error) {
      console.error('❌ Error toggling 2FA:', error);
      Alert.alert(t('error'), t('failedToUpdateTwofactorAuthentication'));
    }
  };

  const sendVerificationCode = async () => {
    try {
      setSendingCode(true);

      // Validate contact information
      if (selectedMethod === 'sms' && !phoneNumber) {
        Alert.alert(t('error'), t('phoneNumberIsRequiredForSmsVerificationPleaseUpdat'));
        return;
      }

      if (selectedMethod === 'email' && !email) {
        Alert.alert(t('error'), t('emailAddressIsRequiredForEmailVerificationPleaseUp'));
        return;
      }

      let result;

      if (selectedMethod === 'sms') {
        console.log('📱 Sending 2FA SMS to:', phoneNumber);
        result = await communicationService.sendSMS2FA(phoneNumber, user.id, '2FA');
      } else {
        console.log('📧 Sending 2FA email to:', email);
        result = await communicationService.sendEmail2FA(email, user.id, '2FA');
      }

      if (result.success) {
        Alert.alert(
          t('twoFactor.verificationCodeSent'),
          result.message,
          [{ text: t('common.ok') }]
        );

        // Store development code for testing if available
        if (result.developmentCode) {
          setVerificationCode(result.developmentCode);
        }
      } else {
        Alert.alert(t('common.errorTitle'), result.error || t('twoFactor.failedToSendVerificationCode'));
      }
    } catch (error) {
      console.error('❌ Error sending verification code:', error);
      Alert.alert(t('error'), t('failedToSendVerificationCodePleaseTryAgain'));
    } finally {
      setSendingCode(false);
    }
  };

  const verifyAndEnable2FA = async (inputCode) => {
    try {
      setVerifyingCode(true);

      if (!inputCode || inputCode.length !== 6) {
        Alert.alert(t('error'), t('pleaseEnterAValid6digitVerificationCode'));
        return;
      }

      let verificationResult;

      if (selectedMethod === 'sms') {
        // Note: Verification logic should be handled by authService or securityManagementService
        // For now, we'll use a placeholder verification method
        verificationResult = { success: true }; // TODO: Implement proper verification
      } else {
        // Note: Verification logic should be handled by authService or securityManagementService
        // For now, we'll use a placeholder verification method
        verificationResult = { success: true }; // TODO: Implement proper verification
      }

      if (verificationResult.success) {
        // Enable 2FA in security settings
        const result = await securityManagementService.updateSecuritySettings(user.id, {
          two_factor_enabled: true,
          two_factor_method: selectedMethod,
          two_factor_contact: selectedMethod === 'sms' ? phoneNumber : email
        });

        if (result.success) {
          setTwoFactorEnabled(true);
          setShowVerification(false);
          setInputCode('');
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          Alert.alert(t('success'), t('twofactorAuthenticationEnabledSuccessfullyViaSelec')
          );
        } else {
          Alert.alert(t('error'), t('verificationSuccessfulButFailedToSaveSettingsPleas'));
        }
      } else {
        Alert.alert(t('common.errorTitle'), verificationResult.error || t('twoFactor.invalidVerificationCode'));
      }
    } catch (error) {
      console.error('❌ Error verifying 2FA:', error);
      Alert.alert(t('error'), t('failedToVerifyTwofactorAuthenticationPleaseTryAgai'));
    } finally {
      setVerifyingCode(false);
    }
  };

  const renderMethodOption = (method, title, description, icon) => (
    <TouchableOpacity
      style={[
        styles.methodOption,
        selectedMethod === method && styles.selectedMethod
      ]}
      onPress={() => setSelectedMethod(method)}
      activeOpacity={0.7}
    >
      <View style={styles.methodLeft}>
        <View style={[styles.methodIcon, { backgroundColor: Colors.primary.main + '20' }]}>
          <Ionicons name={icon} size={24} color={Colors.primary.main} />
        </View>
        <View style={styles.methodText}>
          <Text style={styles.methodTitle}>{title}</Text>
          <Text style={styles.methodDescription}>{description}</Text>
        </View>
      </View>
      <View style={[
        styles.radioButton,
        selectedMethod === method && styles.selectedRadio
      ]}>
        {selectedMethod === method && (
          <View style={styles.radioInner} />
        )}
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>{t('loading2faSettings')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>{t('twofactorAuthentication')}</Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={styles.headerSubtitle}>
          {t('addAnExtraLayerOfSecurityToYourAccount')}
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 2FA Toggle */}
        <View style={styles.section}>
          <View style={styles.toggleSection}>
            <View style={styles.toggleLeft}>
              <Text style={styles.toggleTitle}>{t('enableTwofactorAuthentication')}</Text>
              <Text style={styles.toggleSubtitle}>
                {t('secureYourAccountWithAnAdditionalVerificationStep')}
              </Text>
            </View>
            <Switch
              value={twoFactorEnabled}
              onValueChange={handleToggle2FA}
              trackColor={{ false: Colors.neutral.lightGray, true: Colors.primary.main + '40' }}
              thumbColor={twoFactorEnabled ? Colors.primary.main : Colors.neutral.warmGray}
            />
          </View>
        </View>

        {/* Method Selection */}
        {(twoFactorEnabled || showVerification) && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('verificationMethod')}</Text>
            <Text style={styles.sectionDescription}>
              {t('chooseHowYouWantToReceiveVerificationCodes')}
            </Text>

            {renderMethodOption(
              'sms',
              t('twoFactor.smsTextMessage'),
              phoneNumber ? t('twoFactor.sendToPhone', { phoneNumber }) : t('twoFactor.phoneNumberRequired'),
              'chatbubble-outline'
            )}

            {renderMethodOption(
              'email',
              t('twoFactor.email'),
              email ? t('twoFactor.sendToEmail', { email }) : t('twoFactor.emailAddressRequired'),
              'mail-outline'
            )}
          </View>
        )}

        {/* Verification Section */}
        {showVerification && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('verifyYourIdentity')}</Text>
            <Text style={styles.sectionDescription}>
              {t('twoFactor.enterCodeSentTo', { method: selectedMethod === 'sms' ? t('twoFactor.phone') : t('twoFactor.email') })}
            </Text>

            <View style={styles.verificationContainer}>
              <TextInput
                style={[styles.codeInput, verifyingCode && styles.disabledInput]}
                placeholder="Enter 6-digit code"
                value={inputCode}
                onChangeText={(code) => {
                  if (!verifyingCode) {
                    setInputCode(code);
                    if (code.length === 6) {
                      verifyAndEnable2FA(code);
                    }
                  }
                }}
                keyboardType="numeric"
                maxLength={6}
                textAlign="center"
                autoFocus={true}
                editable={!verifyingCode}
              />

              {verifyingCode && (
                <View style={styles.verifyingContainer}>
                  <Text style={styles.verifyingText}>{t('verifyingCode')}</Text>
                </View>
              )}

              <TouchableOpacity
                style={[styles.resendButton, sendingCode && styles.disabledButton]}
                onPress={() => {
                  if (!sendingCode) {
                    setInputCode('');
                    sendVerificationCode();
                  }
                }}
                disabled={sendingCode}
                activeOpacity={0.7}
              >
                <Text style={[styles.resendText, sendingCode && styles.disabledText]}>
                  {sendingCode ? t('twoFactor.sending') : t('twoFactor.resendCode')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Security Tips */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('securityTips')}</Text>
          
          <View style={styles.tipCard}>
            <Ionicons name="shield-checkmark" size={24} color={Colors.status.success} />
            <Text style={styles.tipText}>
              {t('twofactorAuthenticationSignificantlyIncreasesYourA')}
            </Text>
          </View>

          <View style={styles.tipCard}>
            <Ionicons name="phone-portrait" size={24} color={Colors.primary.main} />
            <Text style={styles.tipText}>
              {t('keepYourPhoneNumberAndEmailAddressUpToDate')}
            </Text>
          </View>

          <View style={styles.tipCard}>
            <Ionicons name="key" size={24} color={Colors.accent.gold} />
            <Text style={styles.tipText}>
              {t('neverShareYourVerificationCodesWithAnyone')}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  placeholder: {
    width: 40,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
    marginTop: -15,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 15,
    lineHeight: 20,
  },
  toggleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  toggleLeft: {
    flex: 1,
    marginRight: 16,
  },
  toggleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  toggleSubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  methodOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: Colors.neutral.lightGray,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  selectedMethod: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary.main + '10',
  },
  methodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  methodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  methodText: {
    flex: 1,
  },
  methodTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  methodDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.neutral.warmGray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedRadio: {
    borderColor: Colors.primary.main,
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.primary.main,
  },
  verificationContainer: {
    backgroundColor: Colors.neutral.white,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  codeInput: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    padding: 15,
    backgroundColor: Colors.neutral.lightGray,
    borderRadius: 8,
    marginBottom: 15,
    letterSpacing: 8,
  },
  resendButton: {
    alignSelf: 'center',
    padding: 10,
  },
  resendText: {
    fontSize: 16,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  tipCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tipText: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
  disabledButton: {
    opacity: 0.6,
  },
  disabledText: {
    color: Colors.neutral.warmGray,
  },
  disabledInput: {
    backgroundColor: Colors.neutral.lightGray,
    opacity: 0.7,
  },
  verifyingContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  verifyingText: {
    fontSize: 14,
    color: Colors.primary.main,
    fontStyle: 'italic',
  },
});

export default TwoFactorAuthScreen;
