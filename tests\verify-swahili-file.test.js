/**
 * Verify Swahili Translation File Content
 * 
 * Checks the actual content of sw.js to identify any issues
 * that might cause English text or raw keys to appear in the app
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Swahili Translation File Content\n');

// Load Swahili translation file
const swPath = path.join(__dirname, '../locales/sw.js');
const swContent = fs.readFileSync(swPath, 'utf8');

// Extract all translation keys and values
const extractTranslations = (content) => {
  const translations = {};
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    const match = line.match(/^\s*([a-zA-Z][a-zA-Z0-9]*)\s*:\s*'([^']*)'.*$/);
    if (match) {
      const [, key, value] = match;
      translations[key] = value;
    }
  }
  
  return translations;
};

const swTranslations = extractTranslations(swContent);

console.log('📊 File Analysis:');
console.log(`   Total lines: ${swContent.split('\n').length}`);
console.log(`   Translation keys found: ${Object.keys(swTranslations).length}`);

// Check for specific problematic patterns
console.log('\n🔍 Checking for Issues:');

// 1. Check for camelCase values (raw keys)
const camelCasePattern = /^[a-z]+[A-Z][a-zA-Z]*$/;
const camelCaseIssues = [];

Object.entries(swTranslations).forEach(([key, value]) => {
  if (camelCasePattern.test(value)) {
    camelCaseIssues.push({ key, value });
  }
});

console.log(`\n1. CamelCase Values (${camelCaseIssues.length}):`);
if (camelCaseIssues.length > 0) {
  camelCaseIssues.forEach(({ key, value }) => {
    console.log(`   ❌ ${key}: "${value}" (appears to be raw key)`);
  });
} else {
  console.log('   ✅ No camelCase values found');
}

// 2. Check for English words that should be translated
const englishWords = ['Account', 'Verification', 'Profile', 'Settings', 'Privacy', 'Login', 'Password', 'Edit'];
const englishWordIssues = [];

Object.entries(swTranslations).forEach(([key, value]) => {
  englishWords.forEach(word => {
    if (value.includes(word)) {
      englishWordIssues.push({ key, value, word });
    }
  });
});

console.log(`\n2. English Words (${englishWordIssues.length}):`);
if (englishWordIssues.length > 0) {
  englishWordIssues.slice(0, 10).forEach(({ key, value, word }) => {
    console.log(`   ❌ ${key}: "${value}" (contains "${word}")`);
  });
  if (englishWordIssues.length > 10) {
    console.log(`   ... and ${englishWordIssues.length - 10} more`);
  }
} else {
  console.log('   ✅ No problematic English words found');
}

// 3. Check for empty or very short values
const shortValues = [];
Object.entries(swTranslations).forEach(([key, value]) => {
  if (value.length < 2) {
    shortValues.push({ key, value });
  }
});

console.log(`\n3. Very Short Values (${shortValues.length}):`);
if (shortValues.length > 0) {
  shortValues.forEach(({ key, value }) => {
    console.log(`   ❌ ${key}: "${value}" (too short)`);
  });
} else {
  console.log('   ✅ No suspiciously short values found');
}

// 4. Check specific edit profile keys
console.log('\n4. Edit Profile Keys Check:');
const editProfileKeys = [
  'editProfile',
  'updateProfile', 
  'uploadProfilePicture',
  'removePhoto',
  'fullName',
  'emailAddress',
  'phoneNumber',
  'personalInformation',
  'save',
  'cancel'
];

editProfileKeys.forEach(key => {
  if (swTranslations[key]) {
    const value = swTranslations[key];
    const isProblematic = camelCasePattern.test(value) || englishWords.some(word => value.includes(word));
    console.log(`   ${isProblematic ? '❌' : '✅'} ${key}: "${value}"`);
  } else {
    console.log(`   ❌ ${key}: MISSING`);
  }
});

// 5. Check verification keys
console.log('\n5. Verification Keys Check:');
const verificationKeys = [
  'accountVerification',
  'verificationSteps',
  'phoneVerification',
  'emailVerification',
  'identityVerification',
  'uploadDocument',
  'nationalId',
  'passport'
];

verificationKeys.forEach(key => {
  if (swTranslations[key]) {
    const value = swTranslations[key];
    const isProblematic = camelCasePattern.test(value) || englishWords.some(word => value.includes(word));
    console.log(`   ${isProblematic ? '❌' : '✅'} ${key}: "${value}"`);
  } else {
    console.log(`   ❌ ${key}: MISSING`);
  }
});

// 6. Check privacy keys
console.log('\n6. Privacy & Data Keys Check:');
const privacyKeys = [
  'privacyAndData',
  'dataConsent',
  'privacyPolicy',
  'exportData',
  'deleteAccount'
];

privacyKeys.forEach(key => {
  if (swTranslations[key]) {
    const value = swTranslations[key];
    const isProblematic = camelCasePattern.test(value) || englishWords.some(word => value.includes(word));
    console.log(`   ${isProblematic ? '❌' : '✅'} ${key}: "${value}"`);
  } else {
    console.log(`   ❌ ${key}: MISSING`);
  }
});

// 7. Check for duplicate keys
console.log('\n7. Duplicate Keys Check:');
const keyCount = {};
const lines = swContent.split('\n');

lines.forEach((line, index) => {
  const match = line.match(/^\s*([a-zA-Z][a-zA-Z0-9]*)\s*:/);
  if (match) {
    const key = match[1];
    if (!keyCount[key]) {
      keyCount[key] = [];
    }
    keyCount[key].push(index + 1);
  }
});

const duplicates = Object.entries(keyCount).filter(([key, lines]) => lines.length > 1);

if (duplicates.length > 0) {
  console.log(`   Found ${duplicates.length} duplicate keys:`);
  duplicates.slice(0, 10).forEach(([key, lines]) => {
    console.log(`   ❌ ${key}: appears on lines ${lines.join(', ')}`);
  });
} else {
  console.log('   ✅ No duplicate keys found');
}

// Summary
const totalIssues = camelCaseIssues.length + englishWordIssues.length + shortValues.length + duplicates.length;

console.log('\n📋 SUMMARY:');
console.log('===========');
console.log(`CamelCase Issues: ${camelCaseIssues.length}`);
console.log(`English Word Issues: ${englishWordIssues.length}`);
console.log(`Short Value Issues: ${shortValues.length}`);
console.log(`Duplicate Key Issues: ${duplicates.length}`);
console.log(`Total Issues: ${totalIssues}`);

if (totalIssues === 0) {
  console.log('\n🎉 FILE STATUS: ✅ EXCELLENT');
  console.log('   No obvious issues found in the translation file');
} else if (totalIssues <= 5) {
  console.log('\n✅ FILE STATUS: 🔄 GOOD');
  console.log('   Minor issues that may need attention');
} else {
  console.log('\n⚠️  FILE STATUS: 🔄 NEEDS ATTENTION');
  console.log('   Several issues that could cause display problems');
}

console.log('\n🎯 RECOMMENDATIONS:');
console.log('===================');
if (camelCaseIssues.length > 0) {
  console.log('• Fix camelCase values - these will show as raw keys in the app');
}
if (englishWordIssues.length > 0) {
  console.log('• Replace English words with proper Swahili translations');
}
if (duplicates.length > 0) {
  console.log('• Remove duplicate keys to avoid conflicts');
}
if (totalIssues === 0) {
  console.log('• File appears ready for use! 🚀');
}
