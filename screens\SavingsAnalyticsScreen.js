/**
 * Savings Analytics Screen
 * Screen for detailed savings analytics and insights
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import savingsAccountService from '../services/savingsAccountService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SavingsAnalyticsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('6M');

  const periods = ['1M', '3M', '6M', '1Y', 'ALL'];

  useEffect(() => {
    loadAnalytics();
  }, [selectedPeriod]);

  const loadAnalytics = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert(t('common.authenticationRequired'), t('savingsAnalytics.pleaseLogInToViewAnalytics'));
        navigation.goBack();
        return;
      }

      // Load real data from services
      const [summaryResult, accountsResult, transactionsResult] = await Promise.all([
        savingsAccountService.getSavingsSummary(userId),
        savingsAccountService.getUserSavingsAccounts(userId, { isActive: true }),
        savingsAccountService.getSavingsTransactions(userId, {
          limit: 100,
          startDate: getStartDateForPeriod(selectedPeriod)
        })
      ]);

      if (accountsResult.success && accountsResult.accounts.length > 0) {
        const accounts = accountsResult.accounts;
        const transactions = transactionsResult.success ? transactionsResult.transactions : [];

        // Calculate real analytics from actual data
        const calculatedAnalytics = calculateAnalyticsFromData(accounts, transactions);
        setAnalytics(calculatedAnalytics);
      } else {
        // Show empty state if no accounts
        setAnalytics(null);
      }

    } catch (error) {
      console.error('❌ Error loading analytics:', error);
      Alert.alert(t('common.error'), t('savingsAnalytics.failedToLoadAnalytics'));
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const getStartDateForPeriod = (period) => {
    const now = new Date();
    switch (period) {
      case '1M':
        return new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
      case '3M':
        return new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
      case '6M':
        return new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
      case '1Y':
        return new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
      case 'ALL':
        return new Date(2020, 0, 1); // Start from 2020
      default:
        return new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
    }
  };

  const calculateAnalyticsFromData = (accounts, transactions) => {
    // Calculate totals
    const totalSavings = accounts.reduce((sum, account) => sum + account.currentBalance, 0);
    const totalInterest = accounts.reduce((sum, account) => sum + (account.totalInterestEarned || 0), 0);

    // Calculate growth rate
    const oldestAccount = accounts.reduce((oldest, account) =>
      new Date(account.createdAt) < new Date(oldest.createdAt) ? account : oldest
    );
    const daysSinceStart = Math.max(1, Math.floor((new Date() - new Date(oldestAccount.createdAt)) / (1000 * 60 * 60 * 24)));
    const savingsGrowth = totalSavings > 0 ? ((totalInterest / totalSavings) * 100) : 0;

    // Calculate monthly deposits
    const deposits = transactions.filter(t => t.transactionType === 'deposit');
    const averageMonthlyDeposit = deposits.length > 0
      ? deposits.reduce((sum, t) => sum + t.amount, 0) / Math.max(1, Math.ceil(daysSinceStart / 30))
      : 0;

    // Calculate goal completion rate
    const goalAccounts = accounts.filter(account => account.targetAmount > 0);
    const goalCompletionRate = goalAccounts.length > 0
      ? goalAccounts.reduce((sum, account) => sum + (account.progressPercentage || 0), 0) / goalAccounts.length
      : 0;

    // Find best performing account
    const bestPerformingAccount = accounts.reduce((best, account) => {
      const growth = account.totalInterestEarned || 0;
      return growth > (best.growth || 0) ? { name: account.accountName, growth } : best;
    }, { name: 'None', growth: 0 });

    // Calculate account breakdown
    const accountBreakdown = calculateAccountBreakdown(accounts);

    // Calculate monthly trends
    const monthlyTrends = calculateMonthlyTrends(transactions);

    // Generate insights
    const insights = generateInsights(accounts, transactions, goalCompletionRate);

    return {
      totalSavings,
      totalInterest,
      savingsGrowth,
      averageMonthlyDeposit,
      goalCompletionRate,
      bestPerformingAccount,
      savingsVelocity: savingsGrowth,
      interestRate: totalSavings > 0 ? (totalInterest / totalSavings) * 100 : 0,
      accountBreakdown,
      monthlyTrends,
      insights
    };
  };

  const calculateAccountBreakdown = (accounts) => {
    const totalAmount = accounts.reduce((sum, account) => sum + account.currentBalance, 0);
    const colors = ['#FF6B35', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#6C5CE7'];

    const breakdown = {};
    accounts.forEach(account => {
      const type = account.accountType || 'general';
      if (!breakdown[type]) {
        breakdown[type] = { amount: 0, accounts: [] };
      }
      breakdown[type].amount += account.currentBalance;
      breakdown[type].accounts.push(account);
    });

    return Object.entries(breakdown).map(([type, data], index) => ({
      type: type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' '),
      amount: data.amount,
      percentage: totalAmount > 0 ? (data.amount / totalAmount) * 100 : 0,
      color: colors[index % colors.length]
    }));
  };

  const calculateMonthlyTrends = (transactions) => {
    const monthlyData = {};
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    transactions.forEach(transaction => {
      const date = new Date(transaction.createdAt);
      const monthKey = months[date.getMonth()];

      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { deposits: 0, withdrawals: 0 };
      }

      if (transaction.transactionType === 'deposit') {
        monthlyData[monthKey].deposits += transaction.amount;
      } else if (transaction.transactionType === 'withdrawal') {
        monthlyData[monthKey].withdrawals += transaction.amount;
      }
    });

    return Object.entries(monthlyData).map(([month, data]) => ({
      month,
      deposits: data.deposits,
      withdrawals: data.withdrawals,
      net: data.deposits - data.withdrawals
    })).slice(-6); // Last 6 months
  };

  const generateInsights = (accounts, transactions, goalCompletionRate) => {
    const insights = [];

    // Goal completion insight
    if (goalCompletionRate > 80) {
      insights.push({
        type: 'positive',
        title: t('savingsAnalytics.excellentProgress'),
        description: t('savingsAnalytics.goalProgressMessage', { percentage: goalCompletionRate.toFixed(1) })
      });
    } else if (goalCompletionRate < 50) {
      insights.push({
        type: 'warning',
        title: t('savingsAnalytics.goalAlert'),
        description: t('savingsAnalytics.increaseContributionsMessage')
      });
    }

    // Emergency fund insight
    const emergencyFund = accounts.find(account =>
      account.accountType === 'emergency' || account.accountName.toLowerCase().includes('emergency')
    );

    if (!emergencyFund) {
      insights.push({
        type: 'tip',
        title: t('savingsAnalytics.emergencyFundRecommendation'),
        description: t('savingsAnalytics.createEmergencyFundMessage')
      });
    } else if (emergencyFund.currentBalance < 10000) {
      insights.push({
        type: 'tip',
        title: t('savingsAnalytics.buildEmergencyFund'),
        description: t('savingsAnalytics.emergencyFundGoalMessage')
      });
    }

    // Interest earning insight
    const totalInterest = accounts.reduce((sum, account) => sum + (account.totalInterestEarned || 0), 0);
    if (totalInterest > 0) {
      insights.push({
        type: 'positive',
        title: t('savingsAnalytics.earningInterest'),
        description: t('savingsAnalytics.interestEarnedMessage', { amount: formatCurrency(totalInterest, 'UGX') })
      });
    }

    return insights.slice(0, 3); // Limit to 3 insights
  };

  const onRefresh = () => {
    loadAnalytics(true);
  };

  const getInsightIcon = (type) => {
    const icons = {
      positive: 'checkmark-circle',
      warning: 'warning',
      tip: 'bulb'
    };
    return icons[type] || 'information-circle';
  };

  const getInsightColor = (type) => {
    const colors = {
      positive: theme.colors.success,
      warning: theme.colors.warning,
      tip: theme.colors.primary
    };
    return colors[type] || theme.colors.info;
  };

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {periods.map((period) => (
          <TouchableOpacity
            key={period}
            style={[
              styles.periodButton,
              selectedPeriod === period && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod(period)}
          >
            <Text style={[
              styles.periodButtonText,
              selectedPeriod === period && styles.periodButtonTextActive
            ]}>
              {period}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderKeyMetrics = () => (
    <View style={styles.metricsCard}>
      <Text style={styles.cardTitle}>{t('savingsAnalytics.keyMetrics')}</Text>
      
      <View style={styles.metricsGrid}>
        <View style={styles.metricItem}>
          <Text style={styles.metricValue}>{formatCurrency(analytics.totalSavings, 'UGX')}</Text>
          <Text style={styles.metricLabel}>{t('savingsAnalytics.totalSavings')}</Text>
          <Text style={[styles.metricChange, { color: theme.colors.success }]}>
            +{analytics.savingsGrowth.toFixed(1)}%
          </Text>
        </View>
        
        <View style={styles.metricItem}>
          <Text style={styles.metricValue}>{formatCurrency(analytics.totalInterest, 'UGX')}</Text>
          <Text style={styles.metricLabel}>{t('savingsAnalytics.interestEarned')}</Text>
          <Text style={[styles.metricChange, { color: theme.colors.success }]}>
            +{analytics.interestRate.toFixed(1)}% APY
          </Text>
        </View>
        
        <View style={styles.metricItem}>
          <Text style={styles.metricValue}>{formatCurrency(analytics.averageMonthlyDeposit, 'UGX')}</Text>
          <Text style={styles.metricLabel}>{t('savingsAnalytics.avgMonthlyDeposit')}</Text>
          <Text style={[styles.metricChange, { color: theme.colors.success }]}>
            +{analytics.savingsVelocity.toFixed(1)}%
          </Text>
        </View>
        
        <View style={styles.metricItem}>
          <Text style={styles.metricValue}>{analytics.goalCompletionRate.toFixed(0)}%</Text>
          <Text style={styles.metricLabel}>{t('savingsAnalytics.goalCompletion')}</Text>
          <Text style={styles.metricChange}>
            {analytics.bestPerformingAccount.name}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderAccountBreakdown = () => (
    <View style={styles.breakdownCard}>
      <Text style={styles.cardTitle}>{t('savingsAnalytics.accountBreakdown')}</Text>
      
      {analytics.accountBreakdown.map((account, index) => (
        <View key={index} style={styles.breakdownItem}>
          <View style={styles.breakdownHeader}>
            <View style={styles.breakdownInfo}>
              <View style={[styles.breakdownDot, { backgroundColor: account.color }]} />
              <Text style={styles.breakdownLabel}>{account.type}</Text>
            </View>
            <Text style={styles.breakdownPercentage}>{account.percentage.toFixed(1)}%</Text>
          </View>
          
          <View style={styles.breakdownBar}>
            <View 
              style={[
                styles.breakdownFill,
                { 
                  width: `${account.percentage}%`,
                  backgroundColor: account.color
                }
              ]}
            />
          </View>
          
          <Text style={styles.breakdownAmount}>
            {formatCurrency(account.amount, 'UGX')}
          </Text>
        </View>
      ))}
    </View>
  );

  const renderMonthlyTrends = () => (
    <View style={styles.trendsCard}>
      <Text style={styles.cardTitle}>{t('savingsAnalytics.monthlyTrends')}</Text>
      
      <View style={styles.trendsChart}>
        {analytics.monthlyTrends.map((month, index) => (
          <View key={index} style={styles.trendMonth}>
            <View style={styles.trendBars}>
              <View 
                style={[
                  styles.trendBar,
                  styles.depositBar,
                  { height: (month.deposits / 1400) * 60 }
                ]}
              />
              <View 
                style={[
                  styles.trendBar,
                  styles.withdrawalBar,
                  { height: (month.withdrawals / 1400) * 60 }
                ]}
              />
            </View>
            <Text style={styles.trendLabel}>{month.month}</Text>
            <Text style={styles.trendNet}>
              {formatCurrency(month.net, 'UGX')}
            </Text>
          </View>
        ))}
      </View>
      
      <View style={styles.trendsLegend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: theme.colors.success }]} />
          <Text style={styles.legendText}>{t('savingsAnalytics.deposits')}</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: theme.colors.error }]} />
          <Text style={styles.legendText}>{t('savingsAnalytics.withdrawals')}</Text>
        </View>
      </View>
    </View>
  );

  const renderInsights = () => (
    <View style={styles.insightsCard}>
      <Text style={styles.cardTitle}>{t('savingsAnalytics.smartInsights')}</Text>
      
      {analytics.insights.map((insight, index) => (
        <View key={index} style={styles.insightItem}>
          <View style={[styles.insightIcon, { backgroundColor: getInsightColor(insight.type) + '20' }]}>
            <Ionicons 
              name={getInsightIcon(insight.type)} 
              size={16} 
              color={getInsightColor(insight.type)} 
            />
          </View>
          <View style={styles.insightContent}>
            <Text style={styles.insightTitle}>{insight.title}</Text>
            <Text style={styles.insightDescription}>{insight.description}</Text>
          </View>
        </View>
      ))}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>{t('savingsAnalytics.loadingAnalytics')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!analytics) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />

        {/* Header */}
        <View style={styles.header}>
          <UnifiedBackButton navigation={navigation} />
          <Text style={styles.headerTitle}>{t('savingsAnalytics.title')}</Text>
          <View style={styles.headerButton} />
        </View>

        <View style={styles.emptyContainer}>
          <Ionicons name="analytics-outline" size={64} color={theme.colors.textSecondary} />
          <Text style={styles.emptyTitle}>{t('savingsAnalytics.noAnalyticsAvailable')}</Text>
          <Text style={styles.emptyDescription}>
            {t('savingsAnalytics.createAccountsToSeeAnalytics')}
          </Text>
          <TouchableOpacity
            style={styles.createAccountButton}
            onPress={() => navigation.navigate('SavingsAccountCreation')}
          >
            <Text style={styles.createAccountButtonText}>{t('savingsAnalytics.createSavingsAccount')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>{t('savingsAnalytics.title')}</Text>
        <TouchableOpacity onPress={() => Alert.alert(t('common.comingSoon'), t('savingsAnalytics.exportAnalyticsComingSoon'))}>
          <Ionicons name="download-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderPeriodSelector()}
        {analytics && (
          <>
            {renderKeyMetrics()}
            {renderAccountBreakdown()}
            {renderMonthlyTrends()}
            {renderInsights()}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  periodSelector: {
    marginBottom: 20,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    marginRight: 8,
  },
  periodButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  periodButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  periodButtonTextActive: {
    color: theme.colors.white,
  },
  metricsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricItem: {
    width: '48%',
    marginBottom: 16,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  metricChange: {
    fontSize: 10,
    fontWeight: '500',
  },
  breakdownCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  breakdownItem: {
    marginBottom: 16,
  },
  breakdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  breakdownInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  breakdownDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  breakdownLabel: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  breakdownPercentage: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '600',
  },
  breakdownBar: {
    height: 6,
    backgroundColor: theme.colors.border,
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 4,
  },
  breakdownFill: {
    height: '100%',
    borderRadius: 3,
  },
  breakdownAmount: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  trendsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  trendsChart: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 100,
    marginBottom: 16,
  },
  trendMonth: {
    alignItems: 'center',
    flex: 1,
  },
  trendBars: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 60,
    marginBottom: 8,
  },
  trendBar: {
    width: 8,
    marginHorizontal: 1,
    borderRadius: 2,
  },
  depositBar: {
    backgroundColor: theme.colors.success,
  },
  withdrawalBar: {
    backgroundColor: theme.colors.error,
  },
  trendLabel: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  trendNet: {
    fontSize: 8,
    color: theme.colors.text,
    fontWeight: '500',
  },
  trendsLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 12,
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  legendText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  insightsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  insightIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  insightDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    lineHeight: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  createAccountButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  createAccountButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  headerButton: {
    width: 24,
  },
});

export default SavingsAnalyticsScreen;
