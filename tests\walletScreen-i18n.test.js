/**
 * WalletScreen i18n Verification Test
 * 
 * Tests to verify that WalletScreen.js is properly internationalized
 * and all user-facing strings use translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Verifying WalletScreen.js i18n Implementation\n');

// Read the WalletScreen.js file
const walletScreenPath = path.join(__dirname, '../screens/WalletScreen.js');
const walletScreenContent = fs.readFileSync(walletScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = walletScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = walletScreenContent.includes('const { t }') || walletScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded user-facing strings (should not exist)
console.log('\n✅ Test 3: No hardcoded user-facing strings');
const potentialHardcodedStrings = [
  'Wallet Balance',
  'Available Balance', 
  'Top Up',
  'Send',
  'History',
  'Settings',
  'Recent Transactions',
  'View All',
  'No transactions yet',
  'My Wallet',
  'Loading wallet...'
];

let hardcodedFound = [];
potentialHardcodedStrings.forEach(str => {
  // Check if the string appears as hardcoded (not in translation key)
  const lines = walletScreenContent.split('\n');
  let foundAsHardcoded = false;
  lines.forEach(line => {
    if ((line.includes(`'${str}'`) || line.includes(`"${str}"`)) && 
        !line.includes('t(') && 
        !line.trim().startsWith('//') && 
        !line.trim().startsWith('*') &&
        !line.includes('console.')) {
      foundAsHardcoded = true;
    }
  });
  if (foundAsHardcoded) {
    hardcodedFound.push(str);
  }
});

if (hardcodedFound.length === 0) {
  console.log('   No hardcoded user-facing strings found: ✅ PASS');
} else {
  console.log('   Hardcoded user-facing strings found: ❌ FAIL');
  hardcodedFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 4: Check for proper wallet translation key usage
console.log('\n✅ Test 4: Wallet translation keys usage');
const walletTranslationKeys = [
  't(\'wallet.walletBalance\')',
  't(\'common.availableBalance\')',
  't(\'common.topUp\')',
  't(\'common.send\')',
  't(\'common.history\')',
  't(\'common.settings\')',
  't(\'wallet.recentTransactions\')',
  't(\'wallet.viewAll\')',
  't(\'common.noTransactionsYet\')',
  't(\'common.myWallet\')'
];

let walletKeysFound = 0;
walletTranslationKeys.forEach(key => {
  if (walletScreenContent.includes(key)) {
    walletKeysFound++;
  }
});

console.log(`   Wallet translation keys found: ${walletKeysFound}/${walletTranslationKeys.length} ${walletKeysFound === walletTranslationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 5: Check that required wallet keys exist in en.js
console.log('\n✅ Test 5: Wallet keys in en.js');
const requiredWalletKeys = [
  'walletBalance:',
  'availableBalance:',
  'topUp:',
  'send:',
  'history:',
  'settings:',
  'recentTransactions:',
  'viewAll:',
  'noTransactionsYet:',
  'myWallet:'
];

let walletKeysInTranslations = 0;
requiredWalletKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    walletKeysInTranslations++;
  }
});

console.log(`   Required wallet keys in en.js: ${walletKeysInTranslations}/${requiredWalletKeys.length} ${walletKeysInTranslations === requiredWalletKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check for Alert.alert with proper translation keys
console.log('\n✅ Test 6: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = walletScreenContent.match(alertPattern);
const totalAlerts = (walletScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for currency context usage
console.log('\n✅ Test 7: Currency context integration');
const hasCurrencyContext = walletScreenContent.includes('useCurrencyContext') && 
                           walletScreenContent.includes('convertAndFormat');
console.log(`   Currency context properly integrated: ${hasCurrencyContext ? '✅ PASS' : '❌ FAIL'}`);

// Test 8: Check for spending limits section
console.log('\n✅ Test 8: Spending limits section');
const hasSpendingLimits = walletScreenContent.includes('t(\'common.spendingLimits\')') &&
                          walletScreenContent.includes('t(\'common.daily\')') &&
                          walletScreenContent.includes('t(\'common.monthly\')');
console.log(`   Spending limits section internationalized: ${hasSpendingLimits ? '✅ PASS' : '❌ FAIL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 8;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (hardcodedFound.length === 0) passedTests++;
if (walletKeysFound === walletTranslationKeys.length) passedTests++;
if (walletKeysInTranslations === requiredWalletKeys.length) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (hasCurrencyContext) passedTests++;
if (hasSpendingLimits) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 WalletScreen.js i18n implementation: ✅ VERIFIED COMPLETE');
  console.log('   All user-facing strings use proper translation keys and the screen is fully internationalized!');
} else {
  console.log('\n⚠️  WalletScreen.js i18n implementation: 🔄 NEEDS ATTENTION');
  console.log('   Some issues need to be addressed.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test wallet screen with different languages');
console.log('2. Verify balance display and currency formatting');
console.log('3. Test transaction history and spending limits');
console.log('4. Proceed to ProfileScreen.js implementation');
