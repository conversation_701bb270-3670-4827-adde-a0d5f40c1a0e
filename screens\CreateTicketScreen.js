import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import authService from '../services/authService';
import supportTicketService from '../services/supportTicketService';

const CreateTicketScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);
  const [subject, setSubject] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [priority, setPriority] = useState('medium');
  const [attachments, setAttachments] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const categories = [
    { id: 'account', label: t('createTicket.accountIssues'), icon: 'person' },
    { id: 'transactions', label: t('createTicket.transactionProblems'), icon: 'card' },
    { id: 'wallet', label: t('createTicket.walletBalance'), icon: 'wallet' },
    { id: 'security', label: t('createTicket.securityConcerns'), icon: 'shield' },
    { id: 'bills', label: t('createTicket.billPayments'), icon: 'receipt' },
    { id: 'technical', label: t('createTicket.technicalSupport'), icon: 'settings' }
  ];

  const priorities = [
    { id: 'low', label: t('createTicket.low'), description: t('createTicket.generalQuestions'), color: Colors.status.success },
    { id: 'medium', label: t('createTicket.medium'), description: t('createTicket.standardIssues'), color: Colors.accent.gold },
    { id: 'high', label: t('createTicket.high'), description: t('createTicket.urgentProblems'), color: Colors.status.error },
    { id: 'urgent', label: t('createTicket.urgent'), description: t('createTicket.criticalIssues'), color: '#DC2626' }
  ];

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('createTicket.permissionRequired'), t('createTicket.grantCameraRollPermissions'));
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const attachment = {
          id: `img_${Date.now()}`,
          uri: asset.uri,
          name: `image_${Date.now()}.jpg`,
          type: 'image',
          size: asset.fileSize || 0
        };
        setAttachments(prev => [...prev, attachment]);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      console.error('❌ Error picking image:', error);
      Alert.alert(t('common.error'), t('createTicket.failedToPickImage'));
    }
  };

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf', 'text/plain', 'application/msword'],
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const attachment = {
          id: `doc_${Date.now()}`,
          uri: asset.uri,
          name: asset.name,
          type: 'document',
          size: asset.size || 0
        };
        setAttachments(prev => [...prev, attachment]);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      console.error('❌ Error picking document:', error);
      Alert.alert(t('common.error'), t('createTicket.failedToPickDocument'));
    }
  };

  const removeAttachment = (attachmentId) => {
    setAttachments(prev => prev.filter(att => att.id !== attachmentId));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const submitTicket = async () => {
    if (!subject.trim()) {
      Alert.alert(t('createTicket.missingInformation'), t('createTicket.pleaseEnterSubject'));
      return;
    }

    if (!description.trim()) {
      Alert.alert(t('createTicket.missingInformation'), t('createTicket.pleaseDescribeIssue'));
      return;
    }

    if (!category) {
      Alert.alert(t('createTicket.missingInformation'), t('createTicket.pleaseSelectCategory'));
      return;
    }

    try {
      setIsSubmitting(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const user = authService.getCurrentUser();
      if (!user) {
        Alert.alert(t('common.error'), t('createTicket.pleaseLogInToCreateTicket'));
        return;
      }

      const ticketData = {
        subject: subject.trim(),
        description: description.trim(),
        category,
        priority,
        attachments
      };

      const result = await supportTicketService.createTicket(user.id, ticketData);

      if (result.success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        
        const ticketId = supportTicketService.formatTicketId(result.ticket.id);
        const responseTime = supportTicketService.getEstimatedResponseTime(priority);
        
        Alert.alert(
          t('createTicket.ticketCreatedSuccessfully'),
          t('createTicket.ticketCreatedMessage', { ticketId, responseTime }),
          [
            {
              text: t('createTicket.viewTickets'),
              onPress: () => {
                navigation.replace('SupportTickets');
              }
            },
            {
              text: t('common.ok'),
              onPress: () => {
                navigation.goBack();
              }
            }
          ]
        );
      } else {
        Alert.alert(t('common.error'), t('createTicket.failedToCreateTicket'));
      }
    } catch (error) {
      console.error('❌ Error submitting ticket:', error);
      Alert.alert(t('common.error'), t('createTicket.failedToCreateTicket'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCategoryOption = (cat) => (
    <TouchableOpacity
      key={cat.id}
      style={[
        styles.categoryOption,
        category === cat.id && styles.selectedCategory
      ]}
      onPress={() => {
        setCategory(cat.id);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }}
      activeOpacity={0.7}
    >
      <Ionicons 
        name={cat.icon} 
        size={20} 
        color={category === cat.id ? Colors.primary.main : Colors.neutral.warmGray} 
      />
      <Text style={[
        styles.categoryText,
        category === cat.id && styles.selectedCategoryText
      ]}>
        {cat.label}
      </Text>
    </TouchableOpacity>
  );

  const renderPriorityOption = (pri) => (
    <TouchableOpacity
      key={pri.id}
      style={[
        styles.priorityOption,
        priority === pri.id && styles.selectedPriority
      ]}
      onPress={() => {
        setPriority(pri.id);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }}
      activeOpacity={0.7}
    >
      <View style={[styles.priorityDot, { backgroundColor: pri.color }]} />
      <View style={styles.priorityText}>
        <Text style={[
          styles.priorityLabel,
          priority === pri.id && styles.selectedPriorityText
        ]}>
          {pri.label}
        </Text>
        <Text style={styles.priorityDescription}>{pri.description}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderAttachment = (attachment) => (
    <View key={attachment.id} style={styles.attachmentItem}>
      <View style={styles.attachmentInfo}>
        <Ionicons 
          name={attachment.type === 'image' ? 'image' : 'document'} 
          size={20} 
          color={Colors.primary.main} 
        />
        <Text style={styles.attachmentName} numberOfLines={1}>
          {attachment.name}
        </Text>
      </View>
      <TouchableOpacity
        style={styles.removeAttachment}
        onPress={() => removeAttachment(attachment.id)}
        activeOpacity={0.7}
      >
        <Ionicons name="close-circle" size={20} color={Colors.status.error} />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>Create Support Ticket</Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={styles.headerSubtitle}>
          Describe your issue and we'll help you resolve it
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Subject */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Subject *</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Brief description of your issue"
            placeholderTextColor={Colors.neutral.warmGray}
            value={subject}
            onChangeText={setSubject}
            maxLength={100}
          />
        </View>

        {/* Category */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Category *</Text>
          <Text style={styles.sectionDescription}>
            Select the category that best describes your issue
          </Text>
          <View style={styles.categoryGrid}>
            {categories.map(renderCategoryOption)}
          </View>
        </View>

        {/* Priority */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Priority</Text>
          <Text style={styles.sectionDescription}>
            How urgent is this issue?
          </Text>
          {priorities.map(renderPriorityOption)}
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Description *</Text>
          <Text style={styles.sectionDescription}>
            Provide detailed information about your issue
          </Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            placeholder="Please describe your issue in detail. Include any error messages, steps you took, and what you expected to happen."
            placeholderTextColor={Colors.neutral.warmGray}
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={6}
            textAlignVertical="top"
            maxLength={1000}
          />
        </View>

        {/* Attachments */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Attachments (Optional)</Text>
          <Text style={styles.sectionDescription}>
            Add screenshots or documents to help us understand your issue
          </Text>
          
          <View style={styles.attachmentButtons}>
            <TouchableOpacity
              style={styles.attachmentButton}
              onPress={pickImage}
              activeOpacity={0.7}
            >
              <Ionicons name="camera" size={20} color={Colors.primary.main} />
              <Text style={styles.attachmentButtonText}>Add Image</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.attachmentButton}
              onPress={pickDocument}
              activeOpacity={0.7}
            >
              <Ionicons name="document" size={20} color={Colors.primary.main} />
              <Text style={styles.attachmentButtonText}>Add Document</Text>
            </TouchableOpacity>
          </View>

          {attachments.length > 0 && (
            <View style={styles.attachmentsList}>
              {attachments.map(renderAttachment)}
            </View>
          )}
        </View>

        {/* Submit Button */}
        <View style={styles.section}>
          <TouchableOpacity
            style={[
              styles.submitButton,
              (!subject.trim() || !description.trim() || !category || isSubmitting) && styles.submitButtonDisabled
            ]}
            onPress={submitTicket}
            disabled={!subject.trim() || !description.trim() || !category || isSubmitting}
            activeOpacity={0.7}
          >
            <Text style={styles.submitButtonText}>
              {isSubmitting ? 'Creating Ticket...' : 'Create Support Ticket'}
            </Text>
            {!isSubmitting && (
              <Ionicons name="send" size={20} color={Colors.neutral.white} />
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  placeholder: {
    width: 40,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.background,
    marginTop: -15,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 12,
    lineHeight: 20,
  },
  textInput: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.neutral.charcoal,
    borderWidth: 1,
    borderColor: Colors.neutral.lightGray,
  },
  textArea: {
    height: 120,
    paddingTop: 12,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.neutral.lightGray,
    marginBottom: 8,
  },
  selectedCategory: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary.main + '10',
  },
  categoryText: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginLeft: 8,
  },
  selectedCategoryText: {
    color: Colors.primary.main,
    fontWeight: '600',
  },
  priorityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: Colors.neutral.lightGray,
  },
  selectedPriority: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary.main + '10',
  },
  priorityDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  priorityText: {
    flex: 1,
  },
  priorityLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  selectedPriorityText: {
    color: Colors.primary.main,
  },
  priorityDescription: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  attachmentButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  attachmentButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.neutral.white,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.primary.main,
    gap: 8,
  },
  attachmentButtonText: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  attachmentsList: {
    gap: 8,
  },
  attachmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.neutral.lightGray,
  },
  attachmentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8,
  },
  attachmentName: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    flex: 1,
  },
  removeAttachment: {
    padding: 4,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary.main,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  submitButtonDisabled: {
    backgroundColor: Colors.neutral.warmGray,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
});

export default CreateTicketScreen;
