/**
 * RegisterScreen i18n Implementation Test
 * 
 * Tests to verify that RegisterScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing RegisterScreen.js i18n Implementation\n');

// Read the RegisterScreen.js file
const registerScreenPath = path.join(__dirname, '../screens/RegisterScreen.js');
const registerScreenContent = fs.readFileSync(registerScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = registerScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = registerScreenContent.includes('const { t }') || registerScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded strings that should have been replaced
console.log('\n✅ Test 3: Hardcoded strings replacement');
const hardcodedStrings = [
  'Create Account',
  'Enter your details to get started',
  'Full Name',
  'Create Password',
  'Confirm Password',
  'Continue',
  'Already have an account?',
  'Sign In',
  'Verify Phone Number',
  'Send OTP',
  'Verify & Continue',
  'Resend OTP'
];

let hardcodedFound = [];
hardcodedStrings.forEach(str => {
  // Check if the string appears in user-facing code (not in comments or console logs)
  const lines = registerScreenContent.split('\n');
  let foundInUserCode = false;
  lines.forEach(line => {
    if (line.includes(`"${str}"`) || line.includes(`'${str}'`) || line.includes(`>${str}<`)) {
      if (!line.trim().startsWith('//') && !line.trim().startsWith('*') && !line.includes('console.')) {
        foundInUserCode = true;
      }
    }
  });
  if (foundInUserCode) {
    hardcodedFound.push(str);
  }
});

if (hardcodedFound.length === 0) {
  console.log('   All hardcoded strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded strings still found: ❌ FAIL');
  hardcodedFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 4: Check for proper translation key usage
console.log('\n✅ Test 4: Translation key usage');
const translationKeys = [
  't(\'auth.createAccount\')',
  't(\'auth.fullName\')',
  't(\'auth.createPassword\')',
  't(\'auth.confirmPassword\')',
  't(\'common.continue\')',
  't(\'auth.signIn\')',
  't(\'auth.verifyPhoneNumber\')',
  't(\'auth.sendOTP\')',
  't(\'auth.verifyAndContinue\')',
  't(\'auth.resendOTP\')'
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (registerScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 5: Check that required translation keys exist in en.js
console.log('\n✅ Test 5: Translation keys in en.js');
const requiredKeys = [
  'createAccount:',
  'fullName:',
  'createPassword:',
  'confirmPassword:',
  'continue:',
  'signIn:',
  'verifyPhoneNumber:',
  'sendOTP:',
  'verifyAndContinue:',
  'resendOTP:',
  'pleaseEnterFullName:',
  'passwordMinLength:',
  'passwordsDoNotMatch:',
  'registrationOtpSent:',
  'registrationError:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check for Alert.alert with proper translation keys
console.log('\n✅ Test 6: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = registerScreenContent.match(alertPattern);
const totalAlerts = (registerScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for string interpolation usage
console.log('\n✅ Test 7: String interpolation');
const hasStringInterpolation = registerScreenContent.includes('t(\'auth.wellSendOtpTo\', { phoneNumber:') || 
                                registerScreenContent.includes('t(\'auth.resendOTPIn\', { seconds:');
console.log(`   String interpolation used: ${hasStringInterpolation ? '✅ PASS' : '❌ FAIL'}`);

// Test 8: Check placeholder text internationalization
console.log('\n✅ Test 8: Placeholder text internationalization');
const placeholderPattern = /placeholder=\{t\(/g;
const placeholderMatches = registerScreenContent.match(placeholderPattern);
const totalPlaceholders = (registerScreenContent.match(/placeholder=/g) || []).length;
const translatedPlaceholders = placeholderMatches ? placeholderMatches.length : 0;

console.log(`   Translated placeholders: ${translatedPlaceholders}/${totalPlaceholders} ${translatedPlaceholders === totalPlaceholders ? '✅ PASS' : '❌ PARTIAL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 8;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (hardcodedFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (hasStringInterpolation) passedTests++;
if (translatedPlaceholders === totalPlaceholders) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 RegisterScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
} else {
  console.log('\n⚠️  RegisterScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test registration flow with different languages');
console.log('2. Verify form validation messages in multiple languages');
console.log('3. Test password requirements display in different languages');
console.log('4. Proceed to OnboardingScreen.js implementation');
