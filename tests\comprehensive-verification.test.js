/**
 * Comprehensive Verification of Swahili Translation File
 * 
 * This test performs a thorough verification to ensure:
 * 1. File syntax is valid
 * 2. All screenshot keys are present and accessible
 * 3. No structural issues exist
 * 4. Keys are properly formatted
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 COMPREHENSIVE SWAHILI TRANSLATION VERIFICATION\n');

// 1. SYNTAX CHECK
console.log('1️⃣ SYNTAX VALIDATION:');
try {
  const swPath = path.join(__dirname, '../locales/sw.js');
  delete require.cache[require.resolve('../locales/sw.js')]; // Clear cache
  const swModule = require('../locales/sw.js');
  
  if (swModule && swModule.default) {
    console.log('   ✅ File syntax is valid');
    console.log('   ✅ Module exports correctly');
    console.log(`   ✅ Translation object type: ${typeof swModule.default}`);
  } else {
    console.log('   ❌ Module structure is invalid');
    return;
  }
} catch (error) {
  console.log('   ❌ Syntax error found:');
  console.log(`   ${error.message}`);
  return;
}

// 2. LOAD TRANSLATIONS
const swTranslations = require('../locales/sw.js').default;
console.log(`   ✅ Loaded ${Object.keys(swTranslations).length} top-level sections`);

// 3. SCREENSHOT KEYS VERIFICATION
console.log('\n2️⃣ SCREENSHOT KEYS VERIFICATION:');

// Screenshot 1: Edit Profile Screen
console.log('\n📱 SCREENSHOT 1 - Edit Profile Screen:');
const editProfileKeys = [
  'editProfile',
  'save', 
  'fullName',
  'emailAddress',
  'phoneNumber',
  'dateOfBirth',
  'country',
  'preferredLanguage'
];

let editProfileMissing = [];
editProfileKeys.forEach(key => {
  // Check root level
  if (swTranslations[key]) {
    console.log(`   ✅ ROOT: ${key} = "${swTranslations[key]}"`);
  }
  // Check common section
  else if (swTranslations.common && swTranslations.common[key]) {
    console.log(`   ✅ COMMON: ${key} = "${swTranslations.common[key]}"`);
  }
  // Check profile section
  else if (swTranslations.profile && swTranslations.profile[key]) {
    console.log(`   ✅ PROFILE: ${key} = "${swTranslations.profile[key]}"`);
  }
  else {
    console.log(`   ❌ MISSING: ${key}`);
    editProfileMissing.push(key);
  }
});

// Screenshot 2: Account Verification Screen
console.log('\n📱 SCREENSHOT 2 - Account Verification Screen:');
const verificationKeys = [
  'accountVerification',
  'basicAccount',
  'limitedFeaturesAndTransactionLimits',
  'currentLimits',
  'dailyLimit',
  'monthlyLimit',
  'verificationSteps',
  'basicInformation',
  'phoneVerification',
  'emailVerification',
  'identityVerification',
  'completed'
];

let verificationMissing = [];
verificationKeys.forEach(key => {
  // Check multiple locations
  if (swTranslations[key]) {
    console.log(`   ✅ ROOT: ${key} = "${swTranslations[key]}"`);
  }
  else if (swTranslations.verification && swTranslations.verification[key]) {
    console.log(`   ✅ VERIFICATION: ${key} = "${swTranslations.verification[key]}"`);
  }
  else if (swTranslations.common && swTranslations.common[key]) {
    console.log(`   ✅ COMMON: ${key} = "${swTranslations.common[key]}"`);
  }
  else {
    console.log(`   ❌ MISSING: ${key}`);
    verificationMissing.push(key);
  }
});

// Screenshot 3: Privacy & Data Screen
console.log('\n📱 SCREENSHOT 3 - Privacy & Data Screen:');
const privacyKeys = [
  'privacyAndData',
  'controlHowYourDataIsUsedAndShared',
  'dataConsent',
  'chooseWhatDataYoureComfortableSharingWithUs',
  'essentialServices',
  'required',
  'analyticsAndPerformance',
  'marketingCommunications',
  'dataSharing',
  'locationServices',
  'communicationPreferences'
];

let privacyMissing = [];
privacyKeys.forEach(key => {
  // Check multiple locations
  if (swTranslations[key]) {
    console.log(`   ✅ ROOT: ${key} = "${swTranslations[key]}"`);
  }
  else if (swTranslations.profile && swTranslations.profile[key]) {
    console.log(`   ✅ PROFILE: ${key} = "${swTranslations.profile[key]}"`);
  }
  else if (swTranslations.common && swTranslations.common[key]) {
    console.log(`   ✅ COMMON: ${key} = "${swTranslations.common[key]}"`);
  }
  else {
    console.log(`   ❌ MISSING: ${key}`);
    privacyMissing.push(key);
  }
});

// 4. DUPLICATE KEYS CHECK
console.log('\n3️⃣ DUPLICATE KEYS CHECK:');
const allKeys = new Set();
const duplicates = [];

// Check for duplicates between root and sections
Object.keys(swTranslations).forEach(key => {
  if (typeof swTranslations[key] === 'string') {
    allKeys.add(`root.${key}`);
  } else if (typeof swTranslations[key] === 'object') {
    Object.keys(swTranslations[key]).forEach(subKey => {
      const fullKey = `${key}.${subKey}`;
      if (allKeys.has(`root.${subKey}`)) {
        duplicates.push(`${subKey} (exists in both root and ${key} section)`);
      }
      allKeys.add(fullKey);
    });
  }
});

if (duplicates.length > 0) {
  console.log(`   ⚠️  Found ${duplicates.length} potential conflicts:`);
  duplicates.slice(0, 10).forEach(dup => console.log(`   - ${dup}`));
} else {
  console.log('   ✅ No critical duplicate conflicts found');
}

// 5. CAMELCASE VALUES CHECK
console.log('\n4️⃣ CAMELCASE VALUES CHECK:');
const camelCasePattern = /^[a-z]+[A-Z][a-zA-Z]*$/;
const camelCaseValues = [];

const checkCamelCase = (obj, prefix = '') => {
  Object.entries(obj).forEach(([key, value]) => {
    if (typeof value === 'string' && camelCasePattern.test(value)) {
      camelCaseValues.push({ key: `${prefix}${key}`, value });
    } else if (typeof value === 'object' && value !== null) {
      checkCamelCase(value, `${prefix}${key}.`);
    }
  });
};

checkCamelCase(swTranslations);

if (camelCaseValues.length > 0) {
  console.log(`   ❌ Found ${camelCaseValues.length} camelCase values (will show as raw keys):`);
  camelCaseValues.forEach(({ key, value }) => {
    console.log(`   - ${key}: "${value}"`);
  });
} else {
  console.log('   ✅ No camelCase values found');
}

// 6. SUMMARY
console.log('\n📋 VERIFICATION SUMMARY:');
console.log('========================');
const totalMissing = editProfileMissing.length + verificationMissing.length + privacyMissing.length;
console.log(`Edit Profile Missing Keys: ${editProfileMissing.length}`);
console.log(`Verification Missing Keys: ${verificationMissing.length}`);
console.log(`Privacy & Data Missing Keys: ${privacyMissing.length}`);
console.log(`CamelCase Values: ${camelCaseValues.length}`);
console.log(`Duplicate Conflicts: ${duplicates.length}`);
console.log(`Total Issues: ${totalMissing + camelCaseValues.length}`);

// 7. DIAGNOSIS
console.log('\n🔬 DIAGNOSIS:');
console.log('=============');

if (totalMissing === 0 && camelCaseValues.length === 0) {
  console.log('✅ TRANSLATION FILE: PERFECT');
  console.log('   All screenshot keys are properly translated');
  console.log('   No structural issues found');
  console.log('   File should work correctly with the app');
} else {
  console.log('⚠️  TRANSLATION FILE: HAS ISSUES');
  if (totalMissing > 0) {
    console.log(`   - ${totalMissing} keys missing from screenshots`);
  }
  if (camelCaseValues.length > 0) {
    console.log(`   - ${camelCaseValues.length} values will show as raw keys`);
  }
}

// 8. POSSIBLE CAUSES IF STILL NOT WORKING
console.log('\n🔍 IF APP STILL SHOWS ENGLISH/RAW KEYS:');
console.log('======================================');
console.log('Possible causes:');
console.log('1. App cache not cleared - try force-closing and restarting app');
console.log('2. App looking for different key names than what we have');
console.log('3. App not properly importing the sw.js file');
console.log('4. Language switching logic not working correctly');
console.log('5. App using nested key paths (e.g., t("common.editProfile") vs t("editProfile"))');

// 9. TESTING RECOMMENDATIONS
console.log('\n🧪 TESTING RECOMMENDATIONS:');
console.log('===========================');
console.log('1. Clear app cache completely');
console.log('2. Restart the app');
console.log('3. Switch to English, then back to Swahili');
console.log('4. Check if ANY Swahili text appears anywhere in the app');
console.log('5. If no Swahili appears at all, the issue is with app configuration');
console.log('6. If some Swahili appears but not on these screens, the issue is key naming');

if (totalMissing === 0 && camelCaseValues.length === 0) {
  console.log('\n🎯 CONCLUSION: Translation file is correct. Issue is likely in app configuration or caching.');
}
