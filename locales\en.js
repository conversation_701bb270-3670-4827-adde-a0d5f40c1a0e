/**
 * English (en) translations for JiraniPay
 * Complete translation coverage for all app features
 */

export default {
  // Common UI elements
  common: {
    continue: 'Continue',
    cancel: 'Cancel',
    back: 'Back',
    next: 'Next',
    continue: 'Continue',
    done: 'Done',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    retry: 'Retry',
    close: 'Close',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    confirm: 'Confirm',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    refresh: 'Refresh',
    share: 'Share',
    notAvailable: 'N/A',
    friend: 'Friend',
    copy: 'Copy',
    paste: 'Paste',
    select: 'Select',
    selectAll: 'Select All',
    clear: 'Clear',
    reset: 'Reset',
    apply: 'Apply',
    submit: 'Submit',
    send: 'Send',
    receive: 'Receive',
    view: 'View',
    hide: 'Hide',
    show: 'Show',
    enable: 'Enable',
    disable: 'Disable',
    on: 'On',
    off: 'Off',
    active: 'Active',
    inactive: 'Inactive',
    available: 'Available',
    unavailable: 'Unavailable',
    online: 'Online',
    offline: 'Offline',
    connected: 'Connected',
    disconnected: 'Disconnected',
    pending: 'Pending',
    completed: 'Completed',
    failed: 'Failed',
    cancelled: 'Cancelled',
    processing: 'Processing',
    verified: 'Verified',
    unverified: 'Unverified',
    required: 'Required',
    optional: 'Optional',
    recommended: 'Recommended',
    new: 'New',
    updated: 'Updated',
    recent: 'Recent',
    popular: 'Popular',
    featured: 'Featured',
    all: 'All',
    none: 'None',
    other: 'Other',
    more: 'More',
    less: 'Less',
    today: 'Today',
    yesterday: 'Yesterday',
    tomorrow: 'Tomorrow',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    thisYear: 'This Year',
    lastWeek: 'Last Week',
    lastMonth: 'Last Month',
    lastYear: 'Last Year',
    nextWeek: 'Next Week',
    nextMonth: 'Next Month',
    nextYear: 'Next Year',

    // Additional common terms
    method: 'Method',
    provider: 'Provider',
    service: 'Service',
    account: 'Account',
    balance: 'Balance',
    amount: 'Amount',
    total: 'Total',
    fee: 'Fee',
    cost: 'Cost',

    // Alert and error messages
    errorTitle: 'Error',
    successTitle: 'Success',
    warningTitle: 'Warning',
    infoTitle: 'Information',
    confirmTitle: 'Confirm',

    // Profile and account
    walletBalance: 'Wallet Balance',
    transactions: 'Transactions',
    accountLevel: 'Account Level',

    // Actions and buttons
    takePhoto: 'Take Photo',
    unknownDevice: 'Unknown Device',
    currentDevice: 'Current Device',
    unknownLocation: 'Unknown location',
    activeNow: 'Active now',
    hoursAgo: '{hours} hours ago',
    daysAgo: '{days} days ago',
    chooseFromGallery: 'Choose from Gallery',
    updateProfilePicture: 'Update Profile Picture',
    chooseUpdateMethod: 'Choose how you want to update your profile picture',
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to logout?',

    // Features and services
    comingSoon: 'Coming Soon',
    featureAvailable: 'Feature Available',
    thisFeatureWillBeAvailable: 'This feature will be available soon!',
    notifyMe: 'Notify Me',

    // Time periods
    monthly: 'Monthly',
    weekly: 'Weekly',
    daily: 'Daily',
    yearly: 'Yearly',

    // Authentication & Login specific
    otpSent: 'OTP Sent',
    pleaseCheckYourPhoneForTheVerificationCode: 'Please check your phone for the verification code',
    error: 'Error',
    somethingWentWrongPleaseTryAgain: 'Something went wrong. Please try again.',
    developmentMode: '🔧 Development Mode',
    pleaseEnterAPasswordWithAtLeast6Characters: 'Please enter a password with at least 6 characters',
    passwordNotSet: 'Password Not Set',
    yourAccountWasCreatedWithPhoneVerificationOnly: 'Your account was created with phone verification only. Would you like to set up a password now or continue with OTP login?',
    info: 'Info',
    switchedToOtpLoginTap: 'Switched to OTP login. Tap "Send OTP" to continue.',
    setUpPassword: 'Set Up Password',
    toSetUpAPasswordWeNeedToVerifyYourIdentityFirst: 'To set up a password, we need to verify your identity first. An OTP will be sent to your phone.',
    pleaseVerifyTheOtpSentToYourPhone: 'Please verify the OTP sent to your phone, then try logging in with your password again.',
    authenticationFailed: 'Authentication Failed',
    pleaseTryAgainOrUsePhoneNumberLogin: 'Please try again or use phone number login',
    biometricAuthenticationFailed: 'Biometric authentication failed',
    otpResent: 'OTP Resent',
    aNewVerificationCodeHasBeenSentToYourPhone: 'A new verification code has been sent to your phone',
    continue: 'Continue',

    // Wallet specific
    workingOfflineWithCachedData: 'Working offline with cached data',
    availableBalance: 'Available Balance',
    topUp: 'Top Up',
    send: 'Send',
    history: 'History',
    settings: 'Settings',
    spendingLimits: 'Spending Limits',
    loadingWallet: 'Loading wallet...',
    myWallet: 'My Wallet',
    noTransactionsYet: 'No transactions yet',
    startUsingYourJiranipayWallet: 'Start using your JiraniPay wallet to see your transaction history here',

    // Wallet actions and descriptions
    topUpWalletAction: 'Top Up Wallet',
    addMoneyToYourJiranipayWallet: 'Add money to your JiraniPay wallet via:\n\n• Mobile Money (MTN, Airtel, UTL)\n• Bank Transfer (Free)\n• Agent Locations\n• Debit/Credit Cards\n\nChoose your preferred method:',
    mobileMoneyTopup: 'Mobile Money Top-Up',
    toTopUpViaMobileMoney: 'To top up via Mobile Money:\n\n1. Dial *165*3# (MTN) or *185*9# (Airtel)\n2. Select "Pay Bill"\n3. Enter Merchant Code: 123456\n4. Enter your phone number\n5. Enter amount and confirm\n\nFunds will reflect instantly!',
    bankTransferDetails: 'Bank Transfer Details',
    transferToStanbicBank: 'Transfer to:\n\nBank: Stanbic Bank Uganda\nAccount: JiraniPay Ltd\nAccount No: *************\nReference: Your phone number\n\nFunds reflect within 30 minutes during banking hours.',
    sendMoneyToContacts: 'Send money to contacts:\n\n• Phone number transfers\n• Contact book integration\n• QR code payments\n• Instant transfers\n• Secure authentication\n\nNavigating to Send Money...',
    accessYourSavingsAccountsAndGoals: 'Access your savings accounts and goals',
    manageYourWallet: 'Manage your wallet:\n\n• Set spending limits\n• Auto-save features\n• Transaction notifications\n• Security settings\n\nWallet settings coming soon!',
    viewYourSpendingInsights: 'View your spending insights:\n\n• Category breakdown\n• Spending trends\n• Budget recommendations\n• Financial insights\n\nAnalytics feature coming soon!',
    generateQrCodesToReceivePayments: 'Generate QR codes to receive payments:\n\n• Personal payment QR codes\n• Fixed or open amount options\n• Share via messaging apps\n• Save to gallery\n• Instant payment processing\n\nNavigating to QR generator...',
    receiveViaQr: 'Receive via QR',

    // Additional button and option texts
    mobileMoneyOption: 'Mobile Money',
    bankTransferOption: 'Bank Transfer',

    // Financial features
    savings: 'Savings'
  },

  // Authentication & Onboarding
  auth: {
    welcomeBack: 'Welcome Back',
    goodMorning: 'Good Morning',
    goodAfternoon: 'Good Afternoon',
    goodEvening: 'Good Evening',
    chooseLoginMethod: 'Choose your login method',
    otpLogin: 'OTP Login',
    passwordLogin: 'Password Login',
    enterPhoneNumber: 'Enter phone number',
    enterPassword: 'Enter your password',
    forgotPassword: 'Forgot Password?',
    sendOTP: 'Send OTP',
    login: 'Login',
    verifyOTP: 'Verify OTP',
    resendOTP: 'Resend OTP',
    resendOTPIn: 'Resend OTP in',
    dontHaveAccount: "Don't have an account?",
    signUp: 'Sign Up',
    useBiometric: 'Use Biometric Login',
    
    // Registration
    createAccount: 'Create Account',
    getStarted: 'Enter your details to get started',
    fullName: 'Full Name',
    createPassword: 'Create Password',
    confirmPassword: 'Confirm Password',
    passwordRequirements: 'Password Requirements:',
    atLeast8Chars: '• At least 8 characters',
    containsNumber: '• Contains at least one number',
    containsSpecialChar: '• Contains at least one special character',
    passwordsMatch: '• Passwords match',
    alreadyHaveAccount: 'Already have an account?',
    signIn: 'Sign In',
    
    // Forgot Password
    resetPassword: 'Reset Password',
    enterPhoneToReset: 'Enter your phone number to reset your password',
    createNewPassword: 'Create New Password',
    newPassword: 'New Password',
    confirmNewPassword: 'Confirm New Password',
    
    // Country & Network
    selectCountry: 'Select Country',
    network: 'Network',
    
    // Validation Messages
    invalidPhone: 'Please enter a valid phone number',
    invalidCredentials: 'Invalid credentials',
    passwordTooShort: 'Password must be at least 8 characters long',
    passwordMismatch: 'Passwords do not match',
    nameRequired: 'Please enter your full name',
    otpSent: 'OTP sent to your phone number',
    otpInvalid: 'Invalid OTP',
    otpExpired: 'OTP code has expired. Please request a new code.',
    loginSuccessful: 'Login successful',
    accountCreated: 'Your account has been created successfully',
    passwordReset: 'Your password has been reset successfully',

    // Additional auth messages from LoginScreen
    failedToSendOTP: 'Failed to send OTP',
    useOTPLogin: 'Use OTP Login',
    failedToInitiatePasswordSetup: 'Failed to initiate password setup. Please try again.',
    loginFailed: 'Login Failed',
    tryAgain: 'Try Again',
    useOTPInstead: 'Use OTP Instead',
    pleaseEnterCompleteOTP: 'Please enter the complete OTP',
    failedToResendOTP: 'Failed to resend OTP',

    // Missing OTP verification strings
    otpSentTo: 'Enter the 6-digit code sent to {phoneNumber}',
    goBack: 'Go Back',
    networkError: 'Network error. Please check your connection and try again.',

    // Registration strings
    accountCreatedSuccessfully: 'Account created successfully!',
    registrationFailed: 'Registration failed',
    registrationError: 'Registration Error',
    termsAndConditions: 'Terms and Conditions',
    agreeToTerms: 'I agree to the Terms and Conditions',
    privacyPolicy: 'Privacy Policy',
    agreeToPrivacyPolicy: 'I agree to the Privacy Policy',

    // Registration validation
    pleaseEnterFullName: 'Please enter your full name (minimum 2 characters)',
    passwordMinLength: 'Password must be at least 8 characters long',
    passwordMustContainDigit: 'Password must contain at least one digit (0-9)',
    passwordMustContainLowercase: 'Password must contain at least one lowercase letter (a-z)',
    passwordMustContainUppercase: 'Password must contain at least one uppercase letter (A-Z)',
    passwordMustContainSymbol: 'Password must contain at least one symbol (!@#$%^&*()_+-=[]{};\'\\:"|<>?,./`~)',
    passwordsDoNotMatch: 'Passwords do not match',

    // Registration flow messages
    registrationOtpSent: 'Registration OTP Sent',
    checkPhoneForVerificationCode: 'Please check your phone for the verification code to complete your account setup',
    failedToSendRegistrationOtp: 'Failed to send registration OTP',
    registrationVerificationFailed: 'Registration Verification Failed',
    invalidOtpTryAgain: 'Invalid OTP. Please try again.',
    registrationOtpExpired: 'Registration OTP has expired. Please request a new code.',
    invalidRegistrationOtp: 'Invalid registration OTP. Please check and try again.',
    networkErrorDuringRegistration: 'Network error during registration. Please check your connection and try again.',
    registrationOtpResent: 'Registration OTP Resent',
    newVerificationCodeSent: 'A new verification code has been sent to your phone',
    failedToResendRegistrationOtp: 'Failed to resend registration OTP',

    // Registration form placeholders
    fullName: 'Full Name',
    enterPhoneNumberExample: 'Enter phone number (e.g., {example})',
    createPassword: 'Create Password',
    confirmPassword: 'Confirm Password',

    // Registration screen text
    createAccount: 'Create Account',
    enterDetailsToGetStarted: 'Enter your details to get started',
    network: 'Network',
    passwordRequirements: 'Password Requirements (Supabase):',
    atLeast8Characters: 'At least 8 characters',
    containsAtLeastOneDigit: 'Contains at least one digit (0-9)',
    containsAtLeastOneLowercase: 'Contains at least one lowercase letter (a-z)',
    containsAtLeastOneUppercase: 'Contains at least one uppercase letter (A-Z)',
    containsAtLeastOneSymbol: 'Contains at least one symbol (!@#$%^&*()_+-=[]{};\'\\:"|<>?,./`~)',
    passwordsMatch: 'Passwords match',
    alreadyHaveAccount: 'Already have an account?',
    signIn: 'Sign In',
    verifyPhoneNumber: 'Verify Phone Number',
    wellSendOtpTo: 'We\'ll send an OTP to {phoneNumber}',
    sendOTP: 'Send OTP',
    verifyAndContinue: 'Verify & Continue',
    resendOTPIn: 'Resend OTP in {seconds}s'
  },

  // Time-based greetings with names
  greetings: {
    goodMorningName: 'Good Morning, {name}',
    goodAfternoonName: 'Good Afternoon, {name}',
    goodEveningName: 'Good Evening, {name}',
    goodMorning: 'Good Morning',
    goodAfternoon: 'Good Afternoon',
    goodEvening: 'Good Evening'
  },

  // Profile
  profile: {
    title: 'Profile',
    verifiedAccount: 'Verified Account',
    pendingVerification: 'Pending Verification',
    customerService: 'Reach our customer service team',
    editProfile: 'Edit Profile',
    accountSettings: 'Account Settings',
    securitySettings: 'Security Settings',
    privacySettings: 'Privacy Settings',
    notificationSettings: 'Notification Settings',
    helpSupport: 'Help & Support',
    aboutApp: 'About App',
    signOut: 'Sign Out',

    // Profile Management
    accountManagement: 'Account Management',
    updatePersonalInfo: 'Update your personal information',
    accountVerification: 'Account Verification',
    verifyIdentity: 'Verify your identity',
    securityPrivacy: 'Security & Privacy',
    manageSecuritySettings: 'Manage your security settings',
    preferences: 'Preferences',
    customizeAppExperience: 'Customize your app experience',
    supportHelp: 'Support & Help',
    getHelpSupport: 'Get help and support',

    // Edit Profile
    personalInformation: 'Personal Information',
    fullName: 'Full Name',
    emailAddress: 'Email Address',
    phoneNumber: 'Phone Number',
    dateOfBirth: 'Date of Birth',
    country: 'Country',
    preferredLanguage: 'Preferred Language',
    save: 'Save',
    cancel: 'Cancel',
    saving: 'Saving...',

    // Profile Validation
    nameRequired: 'Name is required',
    validEmailRequired: 'Valid email is required',
    validPhoneRequired: 'Valid phone number is required',
    profileUpdated: 'Profile updated',
    profileUpdateFailed: 'Profile update failed',

    // Profile Screen Specific
    loadingProfile: 'Loading profile...',
    accountLevel: 'Account Level',
    walletBalance: 'Wallet Balance',
    transactions: 'Transactions',

    // Profile Actions
    updateProfilePicture: 'Update Profile Picture',
    chooseUpdateMethod: 'Choose how you want to update your profile picture',
    takePhoto: 'Take Photo',
    chooseFromGallery: 'Choose from Gallery',

    // Account Management Section
    accountVerificationDesc: 'Verify your identity for higher limits',

    // Security & Privacy Section
    securityAndPrivacy: 'Security & Privacy',
    securitySettingsDesc: 'PIN, biometric auth, and security options',
    privacyAndData: 'Privacy & Data',
    privacyDataDesc: 'Control your data and privacy settings',

    // App Preferences Section
    appPreferences: 'App Preferences',
    darkMode: 'Dark Mode',
    darkModeDesc: 'Switch to dark theme',
    notifications: 'Notifications',
    notificationsDesc: 'Manage notification preferences',
    currency: 'Currency',
    currencyDesc: 'Select your preferred currency',

    // Help & Support Section
    helpAndSupport: 'Help & Support',
    faq: 'Frequently Asked Questions',
    faqDesc: 'Get answers to common questions',
    aiAssistant: 'AI Assistant',
    aiAssistantDesc: 'Get instant help and financial advice',
    contactUs: 'Contact Us',
    contactUsDesc: 'Reach our customer service team',

    // Logout
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to logout?',

    // Error messages
    errorTakingPhoto: 'Failed to take photo',
    errorPickingImage: 'Failed to pick image',
    errorUpdatingNotifications: 'Failed to update notification settings. Your preference has been saved.',
    errorChangingLanguage: 'Failed to change language. Please try again.',
    errorChangingCurrency: 'Failed to change currency. Please try again.',
    errorLogout: 'Failed to logout. Please try again.',

    // Notifications
    notificationsSaved: 'Notification preference saved. For full notification support, use a development build instead of Expo Go.',

    // FAQ Alert
    faqAlert: 'Comprehensive FAQ section with guides for:\n\n• How to send money\n• Bill payment process\n• Wallet top-up methods\n• QR code usage\n• Account verification\n• Security best practices\n\nFull FAQ section coming soon!',

    // Contact Support Alert
    contactSupportAlert: 'Email: <EMAIL>\nPhone: +************\n\nOur customer service team is available 24/7 to assist you with any questions or issues.',

    // Profile Error
    profileError: 'Unable to open profile at the moment. Please try again.',

    // Edit Profile Screen
    editProfile: 'Edit Profile',
    personalInformation: 'Personal Information',
    fullName: 'Full Name',
    emailAddress: 'Email Address',
    phoneNumber: 'Phone Number',
    dateOfBirth: 'Date of Birth',
    country: 'Country',
    preferredLanguage: 'Preferred Language',
    save: 'Save',
    cancel: 'Cancel',
    saving: 'Saving...',

    // Edit Profile Validation Messages
    fullNameRequired: 'Full name is required',
    fullNameMinLength: 'Full name must be at least 2 characters',
    validEmailRequired: 'Please enter a valid email address',
    phoneNumberRequired: 'Phone number is required',
    validUgandaPhoneRequired: 'Please enter a valid Uganda phone number',
    validDateOfBirthRequired: 'Please enter a valid date of birth',
    userNotAuthenticated: 'User not authenticated',
    profileUpdateFailed: 'Failed to update profile',
    profileUpdateFailedDatabase: 'Profile update failed due to a database issue. Please try again.',
    networkError: 'Network error. Please check your connection and try again.',
    validationError: 'Please check that all required fields are filled correctly.',
    profileUpdateFailedTitle: 'Profile Update Failed',
    failedToUpdateProfile: 'Failed to update profile. Please try again.',
    yourProfileHasBeenUpdatedSuccessfully: 'Your profile has been updated successfully',
    unsavedChanges: 'Unsaved Changes',
    youHaveUnsavedChangesAreYouSureYouWantToGoBack: 'You have unsaved changes. Are you sure you want to go back?',
    pleaseCorrectTheErrorsAndTryAgain: 'Please correct the errors and try again',

    // Edit Profile Placeholders
    enterYourFullName: 'Enter your full name',
    enterYourEmailAddress: 'Enter your email address',
    enterYourPhoneNumber: 'Enter your phone number',
    selectYourDateOfBirth: 'Select your date of birth',
    discard: 'Discard',

    // Account Verification Screen
    accountVerification: 'Account Verification',
    verificationSteps: 'Verification Steps',
    viewStatus: 'View Status',
    viewLimits: 'View Limits',
    emailVerification: 'Email Verification',
    verifyEmailAddress: 'Verify your email address',
    identityVerification: 'Identity Verification',
    uploadGovernmentId: 'Upload government-issued ID',
    addressVerification: 'Address Verification',
    confirmResidentialAddress: 'Confirm your residential address',

    // Security Settings
    biometricAuthentication: 'Biometric Authentication',
    enableBiometric: 'Enable fingerprint/face authentication',
    changePassword: 'Change Password',
    updateAccountPassword: 'Update your account password',
    twoFactorAuth: 'Two-Factor Authentication',
    addExtraLayerSecurity: 'Add an extra layer of security',
    sessionTimeout: 'Session Timeout',
    trustedDevices: 'Trusted Devices',
    manageDeviceAccess: 'Manage devices that can access your account',
    securityActivity: 'Security Activity',
    viewRecentSecurityEvents: 'View recent security events and login history',
    securityTips: 'Security Tips',
    learnAccountSecurity: 'Learn how to keep your account secure',

    // PIN Management
    setUpPin: 'Set Up PIN',
    changePin: 'Change PIN',
    createSixDigitPin: 'Create a 6-digit PIN for secure access',
    currentPin: 'Current PIN',
    newPin: 'New PIN',
    confirmPin: 'Confirm PIN',
    pinSetUpSuccessfully: 'PIN set up successfully',
    pinChangedSuccessfully: 'PIN changed successfully',

    // Session Timeout
    sessionTimeoutUpdated: 'Session Timeout Updated',
    sessionWillTimeoutAfterMinutes: 'Your session will now timeout after {minutes} minutes of inactivity.',
    failedToUpdateSessionTimeout: 'Failed to update session timeout',

    // PIN Management
    setUpPin: 'Set Up PIN',
    changePin: 'Change PIN',
    createSixDigitPin: 'Create a 6-digit PIN for secure access',
    updateSixDigitPin: 'Update your 6-digit security PIN',
    currentPin: 'Current PIN',
    newPin: 'New PIN',
    confirmPin: 'Confirm PIN',
    pinMustBeSixDigits: 'PIN must be 6 digits',
    pinsDoNotMatch: 'PINs do not match',
    pinSetUpSuccessfully: 'PIN set up successfully',
    pinChangedSuccessfully: 'PIN changed successfully',

    // Account Security
    accountSecurity: 'Account Security',
    autoLogoutAfterMinutes: 'Auto-logout after {minutes} minutes',

    // Security Information
    securityInformation: 'Security Information',

    // Two-Factor Authentication
    enableTwoFactorAuthentication: 'Enable Two-Factor Authentication',
    secureAccountWithAdditionalVerification: 'Secure your account with an additional verification step',
    twoFactorAuthenticationEnabled: 'Two-factor authentication enabled successfully via {method}',
    twoFactorAuthenticationDisabled: 'Two-factor authentication disabled',
    failedToDisableTwoFactor: 'Failed to disable two-factor authentication',
    failedToUpdateTwoFactor: 'Failed to update two-factor authentication',
    verificationSuccessfulButFailedToSave: 'Verification successful but failed to save settings. Please try again.',

    // Session Timeout
    sessionTimeoutUpdated: 'Session Timeout Updated',
    sessionWillTimeoutAfterMinutes: 'Your session will now timeout after {minutes} minutes of inactivity.',
    failedToUpdateSessionTimeout: 'Failed to update session timeout',
    pleaseLogInToAccessSessionSettings: 'Please log in to access session settings',
    highSecurityFrequentReauth: 'High security - frequent re-authentication',
    balancedSecurityAndConvenience: 'Balanced security and convenience',
    standardSecurityRecommended: 'Standard security (recommended)',
    extendedSessionForConvenience: 'Extended session for convenience',
    lowSecurityMaximumConvenience: 'Low security - maximum convenience',

    // Privacy Controls
    dataPrivacy: 'Data Privacy',
    manageDataSharing: 'Manage how your data is shared',
    marketingCommunications: 'Marketing Communications',
    receivePromotionalEmails: 'Receive promotional emails and offers',
    analyticsTracking: 'Analytics Tracking',
    helpImproveServices: 'Help us improve our services',
    thirdPartySharing: 'Third-Party Sharing',
    shareDataPartners: 'Share data with trusted partners',

    // Data Consent
    dataConsent: 'Data Consent',
    chooseWhatDataYouAreComfortableSharing: 'Choose what data you\'re comfortable sharing with us',
    essentialServices: 'Essential Services',
    requiredForCoreAppFunctionality: 'Required for core app functionality and security',
    analyticsAndPerformance: 'Analytics & Performance',
    helpUsImproveTheAppBySharing: 'Help us improve the app by sharing usage data',
    marketingCommunications: 'Marketing Communications',
    receivePersonalizedOffersAndTips: 'Receive personalized offers and financial tips',
    dataSharing: 'Data Sharing',
    shareAnonymizedDataWithPartners: 'Share anonymized data with trusted partners',

    // Data Rights
    yourDataRights: 'Your Data Rights',
    exportMyData: 'Export My Data',
    downloadACopyOfAllYourData: 'Download a copy of all your data',
    privacyPolicy: 'Privacy Policy',
    readOurComprehensivePrivacyPolicy: 'Read our comprehensive privacy policy',
    dataProtection: 'Data Protection',
    learnAboutYourDataRights: 'Learn about your data rights and protection',

    // Danger Zone
    dangerZone: 'Danger Zone',
    deleteAccount: 'Delete Account',
    permanentlyDeleteYourAccount: 'Permanently delete your account and all data',

    // Export Data
    exportData: 'Export Data',
    exportComplete: 'Export Complete',
    yourDataHasBeenExported: 'Your data has been exported successfully. File size: {size} KB',
    share: 'Share',
    exportFailed: 'Export Failed',
    failedToExportData: 'Failed to export data',
    exporting: 'Exporting...',
    export: 'Export',

    // Account Deletion
    areYouSureYouWantToDelete: 'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently removed.',
    accountDeletionRequested: 'Account Deletion Requested',
    failedToRequestAccountDeletion: 'Failed to request account deletion',

    // Trusted Devices
    removeTrustedDevice: 'Remove Trusted Device',
    confirmRemoveDevice: 'Are you sure you want to remove "{deviceName}" from your trusted devices? You\'ll need to verify your identity the next time you sign in from this device.',
    deviceRemovedSuccess: '"{deviceName}" has been removed from your trusted devices.',
    deviceTrustedSuccess: 'This device has been added to your trusted devices. You won\'t need additional verification when signing in from this device.',

    // Notification Settings
    pushNotifications: 'Push Notifications',
    transactionAlerts: 'Transaction Alerts',
    securityAlerts: 'Security Alerts',
    marketingNotifications: 'Marketing Notifications',
    systemUpdates: 'System Updates',

    // Help & Support
    frequentlyAskedQuestions: 'Frequently Asked Questions',
    privacyPolicy: 'Privacy Policy',

    // FAQ Screen
    moneyTransfer: 'Money Transfer',
    howDoISendMoneyToAnotherUser: 'How do I send money to another JiraniPay user?',
    toSendMoneyGoToSendMoney: 'To send money: 1) Go to the Send Money section, 2) Enter the recipient\'s phone number or scan their QR code, 3) Enter the amount, 4) Add a note (optional), 5) Confirm with your PIN. The money will be transferred instantly.',
    whatAreTheTransferLimits: 'What are the transfer limits?',
    dailyLimitsDependOnVerification: 'Daily limits depend on your verification level: Basic (UGX 500,000), Verified (UGX 2,000,000), Premium (UGX 10,000,000). You can increase limits by completing account verification.',
    stillNeedHelp: 'Still need help?',
    ourCustomerSupportTeamIsAvailable: 'Our customer support team is available 24/7 to assist you.',

    // Contact Support Screen
    liveAIChat: 'Live AI Chat',
    instantAIAssistant: 'Instant AI Assistant',
    getImmediateHelpFromOurAI: 'Get immediate help from our AI assistant',
    aiAssistant: 'AI Assistant',
    callSupport: 'Call Support',
    customerService247: '24/7 Customer Service',
    speakDirectlyWithOurSupport: 'Speak directly with our support team',
    whatsappSupport: 'WhatsApp Support',
    chatOnWhatsapp: 'Chat on WhatsApp',
    messageUsOnWhatsappForQuickHelp: 'Message us on WhatsApp for quick help',
    emailSupport: 'Email Support',
    generalInquiries: 'General Inquiries',
    sendUsYourQuestionsAndConcerns: 'Send us your questions and concerns',
    securityHotline: 'Security Hotline',
    emergencySecurityIssues: 'Emergency Security Issues',
    reportSecurityIncidentsImmediately: 'Report security incidents immediately',
    technicalSupport: 'Technical Support',
    appAndTechnicalIssues: 'App & Technical Issues',
    getHelpWithAppFunctionality: 'Get help with app functionality',
    immediate: 'Immediate',
    instant: 'Instant',
    minutes515: '5-15 minutes',
    hours24: '2-4 hours',
    hours12: '1-2 hours',
    monFri8am8pm: 'Mon-Fri 8AM-8PM',
    monFri8am6pm: 'Mon-Fri 8AM-6PM',

    // AI Chat Screen
    typeYourMessage: 'Type your message...',
    clearChatHistory: 'Clear Chat History',
    areYouSureYouWantToClearAllChat: 'Are you sure you want to clear all chat messages? This action cannot be undone.',
    clear: 'Clear',
    failedToSendMessage: 'Failed to send message. Please try again.',
    failedToSendMessageShort: 'Failed to send message',
    wallet: 'Wallet',
    balanceAndTransactions: 'Balance & transactions',
    checkMyBalance: '💰 Check my balance',
    sendMoney: 'Send Money',
    transferToAnyone: 'Transfer to anyone',
    sendMoneyEmoji: '📤 Send money',
    payBills: 'Pay Bills',
    umemeWaterAirtime: 'UMEME, Water, Airtime',
    security: 'Security',
    accountProtection: 'Account protection',
    qrScanner: 'QR Scanner',
    quickPayments: 'Quick payments',
    qrScannerEmoji: '📱 QR scanner',

    // Account Actions
    deleteAccount: 'Delete Account',
    exportData: 'Export Data',
    clearCache: 'Clear Cache',
    rateApp: 'Rate App',
    shareApp: 'Share App'
  },

  // Dashboard & Home
  dashboard: {
    title: 'Dashboard',
    welcome: 'Welcome to',
    balance: 'Balance',
    totalBalance: 'Total Balance',
    availableBalance: 'Available Balance',
    quickActions: 'Quick Actions',
    recentTransactions: 'Recent Transactions',
    viewAll: 'View All',
    enhanced: 'Enhanced',
    noTransactions: 'No transactions yet',
    transactionsWillAppear: 'Your recent transactions will appear here',
    sendMoney: 'Send Money',
    payBills: 'Pay Bills',
    topUp: 'Top Up',
    scanQR: 'Scan QR',
    savings: 'Savings',
    analytics: 'Analytics',
    topCategory: 'Top Category',
    startUsing: 'Start using JiraniPay to see insights',
    makeTransactions: 'Make transactions to unlock AI-powered financial insights',
    financialInsights: 'Financial Insights',
    quickActionsTitle: 'Quick Actions',
    notifications: 'Notifications',
    notificationsError: 'Unable to open notifications at the moment. Please try again.',
    promotions: {
      schoolFees: 'PAY SCHOOL\nFEES AND WIN',
      doubleData: 'DOUBLE DATA\nWEEKEND',
      doubleDataSubtitle: 'Get 2x data on all bundles',
      cashback: '5% CASHBACK\nON BILLS',
      cashbackSubtitle: 'Pay utilities & earn back'
    },
    quickActions: {
      buyBundles: 'Buy Bundles',
      sendMoney: 'Send Money',
      payBills: 'Pay Bills',
      budgetInsights: 'Budget Insights',
      savings: 'Savings',
      savingsDashboard: 'Savings',
      investments: 'Investments',
      planning: 'Planning',
      requestMoney: 'Request Money',
      scanPay: 'Scan & Pay',
      buyAirtime: 'Buy Airtime'
    },

    // Alert messages for dashboard features
    alerts: {
      errorLoadingData: 'Failed to load dashboard data. Please try again.',
      transactionHistory: 'Transaction History',
      transactionHistoryDesc: 'View all your transactions with:\n\n• Detailed transaction records\n• Filter by date and type\n• Export statements\n• Search functionality\n\nFull history coming soon!',

      financialInsights: 'Financial Insights',
      financialInsightsDesc: 'View detailed analytics:\n\n• Spending breakdown by category\n• Monthly and yearly trends\n• Budget recommendations\n• Savings insights\n• Financial health score\n\nNavigating to Analytics...',

      billPayments: 'Bill Payments',
      billPaymentsDesc: 'Access all bill payment services:\n\n• Electricity (UMEME)\n• Water (NWSC)\n• Internet & TV\n• Mobile Airtime & Data\n• Government Services\n• Insurance Payments\n\nFull bill payment services coming soon!',

      sendMoney: 'Send Money',
      sendMoneyDesc: 'Send money to contacts:\n\n• Phone number transfers\n• Contact book integration\n• QR code payments\n• Instant transfers\n• Secure authentication\n\nSend money feature coming soon!',

      qrPay: 'QR Pay',
      qrPayDesc: 'Opening QR scanner...',

      payBills: 'Pay Bills',
      payBillsDesc: 'Pay your bills easily:\n\n• Electricity (UMEME)\n• Water (NWSC)\n• Internet & TV\n• Mobile airtime\n• Government services\n\nBill payment feature available!',

      savings: 'Savings',
      savingsDesc: 'Save money and achieve your goals:\n\n• Create savings goals\n• Automatic transfers\n• Track progress\n• Earn interest\n\nSavings feature available!',

      buyDataBundles: 'Buy Data Bundles',
      buyDataBundlesDesc: 'Purchase data bundles for MTN, Airtel, and UTL networks',

      buyAirtime: 'Buy Airtime',
      buyAirtimeDesc: 'Purchase airtime for MTN, Airtel, and UTL networks',

      requestMoney: 'Request Money',
      requestMoneyDesc: 'Request money from contacts:\n\n• Send payment requests\n• Contact book integration\n• QR code requests\n• Instant notifications\n• Secure authentication\n\nRequest money feature coming soon!',

      budgetInsights: 'Budget Insights',
      budgetInsightsDesc: 'AI-powered financial insights:\n\n• Spending analysis\n• Budget recommendations\n• Savings optimization\n• Financial health score\n\nBudget insights feature available!',

      transactionHistoryAvailable: 'View all your transactions with:\n\n• Detailed transaction records\n• Filter by date and type\n• Export statements\n• Search functionality\n\nTransaction history available!',

      // Top up wallet
      topUpWallet: 'Top Up Wallet',
      topUpWalletDesc: 'Add money to your JiraniPay wallet via:\n\n• Mobile Money (MTN, Airtel, UTL)\n• Bank Transfer (Free)\n• Agent Locations\n• Debit/Credit Cards\n\nChoose your preferred method:',

      mobileMoneyTopUp: 'Mobile Money Top-Up',
      mobileMoneyTopUpDesc: 'To top up via Mobile Money:\n\n1. Dial *165*3# (MTN) or *185*9# (Airtel)\n2. Select "Pay Bill"\n3. Enter Merchant Code: 123456\n4. Enter your phone number\n5. Enter amount and confirm\n\nFunds will reflect instantly!',

      bankTransfer: 'Bank Transfer',
      bankTransferDesc: 'Transfer to:\n\nBank: Stanbic Bank Uganda\nAccount: JiraniPay Ltd\nAccount No: *************\nReference: Your phone number\n\nFunds reflect within 30 minutes during banking hours.',

      mobileMoneyOption: 'Mobile Money',
      bankTransferOption: 'Bank Transfer',

      // Missing dashboard strings
      investments: 'Investments',
      investmentFeaturesAvailable: 'Investment features are now available!',
      financialPlanning: 'Financial Planning',
      financialPlanningToolsAvailable: 'Financial planning tools are now available!',
      testFeatures: 'Test Features',
      testScreenNavigationFailed: 'Test screen navigation failed',
      monthlySpending: 'Monthly Spending'
    }
  },

  // Wallet & Transactions
  wallet: {
    title: 'Wallet',
    myWallet: 'My Wallet',
    balance: 'Balance',
    availableBalance: 'Available Balance',
    walletBalance: 'Wallet Balance',
    transactions: 'Transactions',
    transactionHistory: 'Transaction History',
    recentTransactions: 'Recent Transactions',
    sendMoney: 'Send Money',
    receiveMoney: 'Receive Money',
    topUp: 'Top Up',
    topUpWallet: 'Top Up Wallet',
    withdraw: 'Withdraw',
    transfer: 'Transfer',
    deposit: 'Deposit',
    payment: 'Payment',
    refund: 'Refund',
    fee: 'Fee',
    total: 'Total',
    amount: 'Amount',
    recipient: 'Recipient',
    sender: 'Sender',
    reference: 'Reference',
    description: 'Description',
    date: 'Date',
    time: 'Time',
    status: 'Status',
    type: 'Type',
    category: 'Category',
    provider: 'Provider',
    account: 'Account',
    accountNumber: 'Account Number',
    phoneNumber: 'Phone Number',
    transactionId: 'Transaction ID',
    receiptNumber: 'Receipt Number',

    // Wallet Actions
    viewAll: 'View All',
    refresh: 'Refresh',
    settings: 'Settings',
    qrPay: 'QR Pay',
    scanQR: 'Scan QR',
    history: 'History',

    // Top Up
    selectTopUpMethod: 'Select Top-Up Method',
    mobileMoneyTopUp: 'Mobile Money Top-Up',
    bankTransfer: 'Bank Transfer',
    enterAmount: 'Enter Amount',
    enterPhoneNumber: 'Enter Phone Number',
    confirmTopUp: 'Confirm Top-Up',
    topUpSuccessful: 'Top-Up Successful!',
    topUpFailed: 'Top-Up Failed',

    // Send Money
    selectRecipient: 'Select Recipient',
    enterRecipientDetails: 'Enter Recipient Details',
    recipientName: 'Recipient Name',
    recipientPhone: 'Recipient Phone',
    transferAmount: 'Transfer Amount',
    transferFee: 'Transfer Fee',
    totalCost: 'Total Cost',
    confirmTransfer: 'Confirm Transfer',
    transferSuccessful: 'Transfer Successful!',
    transferFailed: 'Transfer Failed',

    // Transaction Status
    pending: 'Pending',
    completed: 'Completed',
    failed: 'Failed',
    cancelled: 'Cancelled',
    processing: 'Processing',

    // Spending Limits
    spendingLimits: 'Spending Limits',
    dailyLimit: 'Daily Limit',
    monthlyLimit: 'Monthly Limit',
    spentToday: 'Spent Today',
    spentThisMonth: 'Spent This Month',
    remainingToday: 'Remaining Today',
    remainingThisMonth: 'Remaining This Month',

    // Error Messages
    insufficientBalance: 'Insufficient balance',
    invalidAmount: 'Invalid amount',
    invalidPhoneNumber: 'Invalid phone number',
    networkError: 'Network error',
    transactionFailed: 'Transaction failed',
    loadingProfile: 'Loading profile...',
    loadingWallet: 'Loading wallet...',
    loadingTransactions: 'Loading transactions...',
    phoneNotAvailable: 'Phone not available',
    user: 'User',
    creationFailed: 'Creation Failed',
    loginRequired: 'Login Required',
    notAvailable: 'Not Available',
    errorLoading: 'Error Loading',
    jiranipayWallet: 'JiraniPay Wallet',
    mainAccount: 'Main Account',
    send: 'Send',
    topUp: 'Top Up',
    qrPay: 'QR Pay',
    confirmTransaction: 'Confirm Transaction',
    transactionSuccessful: 'Transaction Successful',
    transactionFailed: 'Transaction Failed',
    insufficientFunds: 'Insufficient funds',
    dailyLimitExceeded: 'Daily limit exceeded',
    monthlyLimitExceeded: 'Monthly limit exceeded',
    invalidAmount: 'Invalid amount',
    minimumAmount: 'Minimum amount',
    maximumAmount: 'Maximum amount',
    transactionLimits: 'Transaction Limits',
    dailyLimit: 'Daily Limit',
    monthlyLimit: 'Monthly Limit',
    remainingDaily: 'Remaining Daily',
    remainingMonthly: 'Remaining Monthly',

    // Missing wallet strings
    workingOfflineWithCachedData: 'Working offline with cached data',
    failedToLoadWalletData: 'Failed to load wallet data',
    walletCreatedSuccessfully: 'Wallet created successfully',
    enterAmount: 'Enter amount',
    transferLimitExceeded: 'Transfer limit exceeded',
    confirmTransfer: 'Confirm transfer'
  },

  // Currency & Formatting
  currency: {
    ugx: 'Ugandan Shilling',
    kes: 'Kenyan Shilling', 
    tzs: 'Tanzanian Shilling',
    rwf: 'Rwandan Franc',
    bif: 'Burundian Franc',
    etb: 'Ethiopian Birr',
    usd: 'US Dollar',
    eur: 'Euro',
    gbp: 'British Pound',
    selectCurrency: 'Select Currency',
    preferredCurrency: 'Preferred Currency',
    currencySettings: 'Currency Settings',
    exchangeRate: 'Exchange Rate',
    convertedAmount: 'Converted Amount',
    conversionRate: 'Conversion Rate',
    lastUpdated: 'Last Updated',
    updateRates: 'Update Rates',
    rateUnavailable: 'Rate unavailable',
    conversionError: 'Conversion error',
    currencyUpdated: 'Currency updated to {currency}',
    updateError: 'Failed to update currency. Please try again.'
  },

  // Navigation
  navigation: {
    home: 'Home',
    dashboard: 'Dashboard',
    wallet: 'Wallet',
    bills: 'Bills',
    sendMoney: 'Send Money',
    payBills: 'Pay Bills',
    savings: 'Savings',
    analytics: 'Analytics',
    profile: 'Profile',
    settings: 'Settings',
    help: 'Help & Support',
    notifications: 'Notifications',
    security: 'Security',
    about: 'About',
    logout: 'Logout',
    qrPay: 'QR Pay'
  },

  // QR Code
  qr: {
    title: 'QR Pay & Scan',
    subtitle: 'Scan QR codes to pay or generate QR codes to receive payments',
    scanQRCode: 'Scan QR Code',
    generateQR: 'Generate QR',
    payAndScan: 'QR Pay & Scan'
  },

  // Buttons
  buttons: {
    login: 'Login',
    signup: 'Sign Up',
    continue: 'Continue',
    cancel: 'Cancel',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    confirm: 'Confirm',
    retry: 'Retry',
    refresh: 'Refresh',
    share: 'Share',
    copy: 'Copy',
    send: 'Send',
    receive: 'Receive',
    topUp: 'Top Up',
    withdraw: 'Withdraw',
    viewAll: 'View All',
    viewDetails: 'View Details',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    done: 'Done',
    apply: 'Apply',
    reset: 'Reset',
    update: 'Update',
    enable: 'Enable',
    disable: 'Disable'
  },

  // Error Messages
  errors: {
    networkError: 'Network connection error. Please check your internet connection.',
    serverError: 'Server error. Please try again later.',
    invalidInput: 'Invalid input. Please check your information.',
    authenticationFailed: 'Authentication failed. Please try again.',
    insufficientFunds: 'Insufficient funds for this transaction.',
    transactionFailed: 'Transaction failed. Please try again.',
    limitExceeded: 'Transaction limit exceeded.',
    invalidAmount: 'Invalid amount entered.',
    invalidPhoneNumber: 'Invalid phone number format.',
    invalidEmail: 'Invalid email address format.',
    passwordTooWeak: 'Password is too weak. Please choose a stronger password.',
    passwordMismatch: 'Passwords do not match.',
    accountNotFound: 'Account not found.',
    accountLocked: 'Account is temporarily locked.',
    sessionExpired: 'Your session has expired. Please login again.',
    permissionDenied: 'Permission denied.',
    fileUploadFailed: 'File upload failed.',
    cameraPermissionDenied: 'Camera permission is required.',
    locationPermissionDenied: 'Location permission is required.',
    biometricNotAvailable: 'Biometric authentication is not available.',
    biometricFailed: 'Biometric authentication failed.',
    qrCodeInvalid: 'Invalid QR code.',
    currencyConversionFailed: 'Currency conversion failed.',
    rateNotAvailable: 'Exchange rate not available.',
    serviceUnavailable: 'Service temporarily unavailable.',
    maintenanceMode: 'App is under maintenance. Please try again later.',
    updateRequired: 'App update required. Please update to continue.',
    deviceNotSupported: 'Device not supported.',
    featureNotAvailable: 'Feature not available in your region.',
    dailyLimitReached: 'Daily transaction limit reached.',
    monthlyLimitReached: 'Monthly transaction limit reached.',
    minimumAmountNotMet: 'Minimum transaction amount not met.',
    maximumAmountExceeded: 'Maximum transaction amount exceeded.',
    recipientNotFound: 'Recipient not found.',
    invalidRecipient: 'Invalid recipient information.',
    duplicateTransaction: 'Duplicate transaction detected.',
    transactionTimeout: 'Transaction timed out.',
    invalidOTP: 'Invalid OTP code.',
    otpExpired: 'OTP code has expired.',
    tooManyAttempts: 'Too many failed attempts. Please try again later.',
    accountSuspended: 'Account has been suspended.',
    kycRequired: 'KYC verification required.',
    documentUploadRequired: 'Document upload required.',
    verificationPending: 'Verification is pending.',
    verificationFailed: 'Verification failed.',
    invalidDocument: 'Invalid document format.',
    documentExpired: 'Document has expired.',
    addressVerificationRequired: 'Address verification required.',
    phoneVerificationRequired: 'Phone number verification required.',
    emailVerificationRequired: 'Email verification required.'
  },

  // Success Messages
  success: {
    loginSuccessful: 'Login successful!',
    registrationSuccessful: 'Registration successful!',
    transactionSuccessful: 'Transaction completed successfully!',
    passwordUpdated: 'Password updated successfully!',
    profileUpdated: 'Profile updated successfully!',
    settingsSaved: 'Settings saved successfully!',
    languageUpdated: 'Language updated successfully!',
    currencyUpdated: 'Currency updated successfully!',
    notificationsSent: 'Notifications sent successfully!',
    backupCreated: 'Backup created successfully!',
    dataExported: 'Data exported successfully!',
    accountVerified: 'Account verified successfully!',
    documentUploaded: 'Document uploaded successfully!',
    paymentSuccessful: 'Payment completed successfully!',
    transferSuccessful: 'Transfer completed successfully!',
    topUpSuccessful: 'Top-up completed successfully!',
    withdrawalSuccessful: 'Withdrawal completed successfully!',
    savingsGoalCreated: 'Savings goal created successfully!',
    savingsGoalUpdated: 'Savings goal updated successfully!',
    reminderSet: 'Reminder set successfully!',
    subscriptionUpdated: 'Subscription updated successfully!',
    feedbackSubmitted: 'Feedback submitted successfully!',
    supportTicketCreated: 'Support ticket created successfully!',
    passwordReset: 'Password reset successfully!',
    emailVerified: 'Email verified successfully!',
    phoneVerified: 'Phone number verified successfully!',
    biometricEnabled: 'Biometric authentication enabled!',
    biometricDisabled: 'Biometric authentication disabled!',
    securityUpdated: 'Security settings updated successfully!',
    privacyUpdated: 'Privacy settings updated successfully!',
    preferencesUpdated: 'Preferences updated successfully!',
    cacheCleared: 'Cache cleared successfully!',
    logoutSuccessful: 'Logout successful!'
  },

  // Settings
  settings: {
    title: 'Settings',
    languageAndCurrency: 'Language & Currency',
    language: 'Language',
    languageDescription: 'Choose your preferred language for the app',
    languageInfo: 'The app will restart to apply language changes',
    languageUpdated: 'Language updated successfully',
    languageUpdateError: 'Failed to update language. Please try again.',
    currencyDescription: 'Choose your preferred currency for transactions',
    currencyInfo: 'All amounts will be displayed in your preferred currency with real-time conversion',
    security: 'Security',
    privacy: 'Privacy',
    notifications: 'Notifications',
    account: 'Account',
    support: 'Support',
    about: 'About',
    version: 'Version',
    buildNumber: 'Build Number',
    termsOfService: 'Terms of Service',
    privacyPolicy: 'Privacy Policy',
    contactUs: 'Contact Us',
    rateApp: 'Rate App',
    shareApp: 'Share App',
    clearCache: 'Clear Cache',
    exportData: 'Export Data',
    deleteAccount: 'Delete Account',
    signOut: 'Sign Out',

    // Missing settings strings
    walletSettings: 'Wallet Settings',
    enterDailySpendingLimit: 'Enter daily spending limit',
    enterMonthlySpendingLimit: 'Enter monthly spending limit',
    securitySettings: 'Security Settings',
    enableBiometricAuthentication: 'Enable biometric authentication',
    changePin: 'Change PIN',
    twoFactorAuthentication: 'Two-factor authentication',
    notificationSettings: 'Notification Settings',
    transactionAlerts: 'Transaction Alerts',
    securityAlerts: 'Security Alerts',
    marketingNotifications: 'Marketing Notifications'
  },

  // Bills & Payments
  bills: {
    payBill: 'Pay Bill',
    categories: 'CATEGORIES',

    // Error messages
    failedToLoadBillPaymentOptions: 'Failed to load bill payment options. Please try again.',

    // Interface text
    enterPaybillName: 'Enter Paybill name',
    schoolFees: 'School Fees',
    educationPayments: 'Education payments',

    // Bill confirmation screen
    paymentFailed: 'Payment Failed',
    unableToProcessPayment: 'Unable to process payment. Please try again.',
    confirmPayment: 'Confirm Payment',
    reviewPaymentDetails: 'Please review your payment details',
    accountDetails: 'ACCOUNT DETAILS',
    provider: 'Provider',
    category: 'Category',
    serviceType: 'Service Type',
    account: 'Account',
    customer: 'Customer',
    reference: 'Reference',
    paymentDetails: 'PAYMENT DETAILS',
    amount: 'Amount',
    serviceCharge: 'Service Charge',
    vat: 'VAT',
    total: 'Total',
    paymentWillBeProcessedImmediately: 'Payment will be processed immediately',
    paymentIsSecureAndEncrypted: 'Your payment is secure and encrypted',
    processing: 'Processing...',
    viewAll: 'VIEW ALL',
    showLess: 'SHOW LESS',
    utilities: 'Utilities',
    airtime: 'Airtime & Data',
    government: 'Government',
    education: 'Education',
    insurance: 'Insurance',
    entertainment: 'Entertainment',

    // Bill Categories
    electricity: 'Electricity',
    water: 'Water',
    internet: 'Internet',
    mobile: 'Mobile',
    schoolFees: 'School Fees',
    financial: 'Financial Service',
    postpaid: 'Postpaid',
    solar: 'Solar',
    tv: 'Pay TV',
    gaming: 'Gaming',
    governmentServices: 'Government Services',
    payTV: 'Pay TV',

    // Bill Subtitles
    powerBillsUtilities: 'Power bills & utilities',
    waterBillsUtilities: 'Water bills & utilities',
    mobileTopUpData: 'Mobile top-up & data',
    bankingFinance: 'Banking & finance',
    monthlySubscriptions: 'Monthly subscriptions',
    solarEnergyPayments: 'Solar energy payments',
    tvSubscriptions: 'TV subscriptions',
    gamingEntertainment: 'Gaming & entertainment',
    officialPayments: 'Official payments',
    televisionSubscriptions: 'Television subscriptions',

    // Bill Details
    selectProvider: 'Select Provider',
    searchProviders: 'Search providers...',
    accountNumber: 'Account Number',
    customerName: 'Customer Name',
    reference: 'Reference',
    amount: 'Amount',
    enterAccountNumber: 'Enter account number',
    enterCustomerName: 'Enter customer name',
    enterReference: 'Enter reference (optional)',
    enterAmount: 'Enter amount',

    // Bill Confirmation
    confirmPayment: 'Confirm Payment',
    reviewPaymentDetails: 'Please review your payment details',
    paymentDetails: 'Payment Details',
    serviceFee: 'Service Fee',
    totalAmount: 'Total Amount',
    confirmAndPay: 'Confirm & Pay',

    // Bill Success
    paymentSuccessful: 'Payment Successful!',
    billPaidSuccessfully: 'Your bill has been paid successfully',
    transactionId: 'Transaction ID',
    receiptNumber: 'Receipt Number',
    downloadReceipt: 'Download Receipt',
    shareReceipt: 'Share Receipt',
    payAnotherBill: 'Pay Another Bill',
    backToHome: 'Back to Home',

    // Error Messages
    invalidAccountNumber: 'Invalid account number',
    invalidAmount: 'Invalid amount',
    paymentFailed: 'Payment failed',
    insufficientBalance: 'Insufficient balance',
    networkError: 'Network error',
    tryAgain: 'Try again',

    // Missing bill payment strings
    selectBiller: 'Select a biller',
    searchBillers: 'Search billers...',
    noBillersFound: 'No billers found',
    billPaymentSuccessful: 'Bill payment successful',
    enterAccountNumber: 'Enter account number',
    enterCustomerName: 'Enter customer name',
    selectPaymentMethod: 'Select payment method',
    confirmPayment: 'Confirm Payment'
  },

  // Onboarding
  onboarding: {
    // Slide titles
    payBillsEasily: 'Pay Bills Easily',
    multiplePaymentOptions: 'Multiple Payment Options',
    aiPoweredInsights: 'AI-Powered Insights',

    // Slide descriptions
    payBillsDescription: 'Pay your utility bills, airtime, and more with just a few taps',
    multiplePaymentDescription: 'Link bank accounts, mobile money, or use your in-app wallet',
    aiInsightsDescription: 'Get smart financial advice and track your spending habits',

    // Navigation
    skip: 'Skip',
    getStarted: 'Get Started'
  },

  // Send Money & Transfers
  sendMoney: {
    // Error messages
    failedToLoadContacts: 'Failed to load contacts. Please try again.',
    contactsPermissionRequired: 'Contacts Permission Required',
    contactsPermissionMessage: 'To send money to your contacts, please allow access to your contacts in Settings. You can still enter phone numbers manually.',
    noPhoneNumberFound: 'No phone number found for this contact',
    qrScanner: 'QR Scanner',
    failedToOpenQRScanner: 'Failed to open QR scanner. Please try again.',

    // Interface text
    enterNumber: 'Enter Number',
    scanQR: 'Scan QR',
    request: 'Request',
    loadingContacts: 'Loading contacts...',
    searchContactsPlaceholder: 'Search contacts or enter phone number',
    manualEntry: 'Manual Entry',

    // Tab labels
    allContacts: 'All Contacts',
    recent: 'Recent',
    favorites: 'Favorites',

    // Empty state messages
    noRecentTransfers: 'No Recent Transfers',
    noFavoriteContacts: 'No Favorite Contacts',
    noContactsFound: 'No Contacts Found',
    noContactsAvailable: 'No Contacts Available',
    recentTransfersWillAppear: 'Your recent transfers will appear here',
    addContactsToFavorites: 'Add contacts to favorites for quick access',
    tryDifferentSearchTerm: 'Try a different search term',
    allowContactsAccess: 'Allow contacts access to see your contacts',
  },

  // Transfer Confirmation
  transfer: {
    // Error messages
    authenticationFailed: 'Authentication Failed',
    biometricAuthFailedMessage: 'Biometric authentication failed. Please try PIN authentication.',
    authenticationFailedTryAgain: 'Authentication failed. Please try again.',
    invalidPIN: 'Invalid PIN',
    pleaseEnter4DigitPIN: 'Please enter a 4-digit PIN',
    pinVerification: 'PIN Verification',
    pinVerificationUnavailable: 'PIN verification service temporarily unavailable',
    pinVerificationFailed: 'PIN verification failed. Please try again.',
    transferFailed: 'Transfer Failed',
    transferCouldNotBeCompleted: 'Transfer could not be completed',
    transferFailedTryAgain: 'Transfer failed. Please try again.',

    // Interface text
    transferSummary: 'Transfer Summary',
    transferDetails: 'Transfer Details',
    amount: 'Amount',
    transferFee: 'Transfer Fee',
    purpose: 'Purpose',
    totalAmount: 'Total Amount',
    freeTransfer: 'Free Transfer!',
    confirmTransfer: 'Confirm Transfer',
    chooseAuthenticationMethod: 'Choose your preferred authentication method to complete this transfer',
    pinAuthentication: 'PIN Authentication',
    enterYour4DigitPIN: 'Enter your 4-digit PIN',
    enterPIN: 'Enter PIN',
    verifyPIN: 'Verify PIN',
    enterPINPlaceholder: 'Enter PIN',
    title: 'Send Money',
    selectRecipient: 'Select Recipient',
    searchContacts: 'Search contacts...',
    recentTransfers: 'Recent Transfers',
    favorites: 'Favorites',
    manualEntry: 'Manual Entry',
    scanQR: 'Scan QR Code',

    // Error handling
    error: 'Error',
    failedToLoadContacts: 'Failed to load contacts. Please try again.',
    noPhoneNumberFound: 'No phone number found for this contact',

    // QR functionality
    qrScanner: 'QR Scanner',
    failedToOpenQrScanner: 'Failed to open QR scanner. Please try again.',

    // Recipient validation
    recipientFound: 'Recipient Found',
    proceedWithTransfer: 'Proceed with transfer?',
    validationError: 'Validation Error',
    failedToValidateRecipient: 'Failed to validate recipient. Please try again.',

    // Transfer flow
    enterAmount: 'Enter Amount',
    transferFee: 'Transfer Fee',
    totalCost: 'Total Cost',
    addNote: 'Add Note (Optional)',
    confirmTransfer: 'Confirm Transfer',
    transferSuccessful: 'Transfer Successful!',
    transferFailed: 'Transfer Failed',

    // Contact picker
    selectContact: 'Select Contact',
    noContactsFound: 'No contacts found',
    permissionRequired: 'Permission required to access contacts'
  },

  // Account Verification
  verification: {
    title: 'Account Verification',
    verificationSteps: 'Verification Steps',

    // Account levels
    basicAccount: 'Basic Account',
    basicAccountDescription: 'Limited features and transaction limits',
    standardAccount: 'Standard Account',
    standardAccountDescription: 'Enhanced features and higher limits',
    premiumAccount: 'Premium Account',
    premiumAccountDescription: 'Full access to all features',

    // Verification steps
    basicInformation: 'Basic Information',
    basicInformationDescription: 'Complete your profile with name, phone, and email',
    phoneVerification: 'Phone Verification',
    phoneVerificationDescription: 'Verify your phone number with OTP',
    emailVerificationDescription: 'Verify your email address',
    identityVerificationDescription: 'Upload your National ID or Passport',
    addressVerificationDescription: 'Upload utility bill or bank statement',

    // Document upload
    uploadDocument: 'Upload Document',
    takePhoto: 'Take Photo',
    chooseFromGallery: 'Choose from Gallery',
    documentUploadedSuccessfully: 'Document uploaded successfully',
    failedToUploadDocument: 'Failed to upload document',
    selectDocumentType: 'Select Document Type',
    nationalId: 'National ID',
    passport: 'Passport',
    drivingLicense: 'Driving License',
    utilityBill: 'Utility Bill',

    // Email verification
    emailVerification: 'Email Verification',
    verifyYourEmailAddress: 'Verify your email address',
    verificationCodeSent: 'Verification code sent',
    invalidVerificationCode: 'Invalid verification code',
    enterVerificationCode: 'Enter verification code',
    resendCode: 'Resend Code',

    // Identity verification
    identityVerification: 'Identity Verification',
    uploadGovernmentId: 'Upload government-issued ID',
    takeSelfie: 'Take Selfie',
    verifyIdentity: 'Verify Identity',

    // Address verification
    addressVerification: 'Address Verification',
    confirmResidentialAddress: 'Confirm your residential address',
    uploadProofOfAddress: 'Upload proof of address',

    // Status messages
    verificationPending: 'Verification Pending',
    verificationComplete: 'Verification Complete',
    verificationFailed: 'Verification Failed',
    documentsUnderReview: 'Documents under review',
    verificationApproved: 'Verification approved',
    verificationRejected: 'Verification rejected'
  },

  // Security Settings
  security: {
    biometricNotAvailableMessage: 'Biometric authentication is not available on this device. Please set up fingerprint or face recognition in your device settings.',
  },

  // Two-Factor Authentication
  twoFactor: {
    verificationCodeSent: 'Verification Code Sent',
    failedToSendVerificationCode: 'Failed to send verification code',
    invalidVerificationCode: 'Invalid verification code. Please try again.',
    smsTextMessage: 'SMS Text Message',
    sendToPhone: 'Send to {phoneNumber}',
    phoneNumberRequired: 'Phone number required',
    email: 'Email',
    sendToEmail: 'Send to {email}',
    emailAddressRequired: 'Email address required',
    enterCodeSentTo: 'Enter the 6-digit code sent to your {method}',
    phone: 'phone',
    sending: 'Sending...',
    resendCode: 'Resend Code',
  },

  // Analytics & Insights
  analytics: {
    title: 'Analytics',

    // Time periods
    week: 'Week',
    month: 'Month',
    year: 'Year',

    // Analytics sections
    spendingAnalytics: 'Spending Analytics',
    spendingByCategory: 'Spending by Category',
    spendingTrend: 'Spending Trend',
    financialInsights: 'Financial Insights',
    quickBudgetTips: 'Quick Budget Tips',

    // Chart labels
    expenses: 'Expenses',
    income: 'Income',
    transactionsCount: '{count} transactions',

    // States and messages
    loadingAnalytics: 'Loading analytics...',
    noSpendingDataAvailable: 'No spending data available',

    // Reports
    monthlyReport: 'Monthly Report',
    weeklyReport: 'Weekly Report',
    yearlyReport: 'Yearly Report',
    categoryBreakdown: 'Category Breakdown',
    exportData: 'Export Data',
    downloadReport: 'Download Report',
  },

  // Analytics Export
  analyticsExport: {
    title: 'Export Analytics',

    // Export formats
    pdfReport: 'PDF Report',
    pdfDescription: 'Comprehensive report with charts and insights',
    excelCsv: 'Excel/CSV',
    excelDescription: 'Raw data for further analysis',

    // Chart titles
    completeAnalyticsReport: 'Complete Analytics Report',
    spendingTrendsAnalysis: 'Spending Trends Analysis',
    categorySpendingReport: 'Category Spending Report',
    monthlyComparisonReport: 'Monthly Comparison Report',
    savingsProgressReport: 'Savings Progress Report',
    investmentPerformanceReport: 'Investment Performance Report',

    // Export options
    includeCharts: 'Include Charts',
    includeChartsDescription: 'Add visual charts to the report',
    includeInsights: 'Include Insights',
    includeInsightsDescription: 'Add AI-generated financial insights',
    includeRawData: 'Include Raw Data',
    includeRawDataDescription: 'Add detailed transaction data',

    // Export actions
    exportReport: 'Export Report',
    exporting: 'Exporting...',
    share: 'Share',

    // Success and error messages
    exportSuccessful: 'Export Successful',
    exportSuccessMessage: '{title} has been exported successfully!',
    exportFailed: 'Export Failed',
    exportFailedMessage: 'Failed to export analytics. Please try again.',

    // Budget insights
    budgetInsights: 'Budget Insights',
    setBudget: 'Set Budget',
    budgetExceeded: 'Budget exceeded',
    onTrack: 'On track',
    budgetRemaining: 'Budget remaining',
    spendingTrends: 'Spending Trends',

    // Categories
    food: 'Food & Dining',
    transport: 'Transport',
    utilities: 'Utilities',
    entertainment: 'Entertainment',
    shopping: 'Shopping',
    healthcare: 'Healthcare',
    education: 'Education',
    other: 'Other',

    // Insights
    topSpendingCategory: 'Top spending category',
    averageMonthlySpending: 'Average monthly spending',
    savingsRate: 'Savings rate',
    financialHealthScore: 'Financial health score'
  },

  // Savings Analytics
  savingsAnalytics: {
    title: 'Savings Analytics',

    // Key metrics
    keyMetrics: 'Key Metrics',
    totalSavings: 'Total Savings',
    interestEarned: 'Interest Earned',
    avgMonthlyDeposit: 'Avg Monthly Deposit',
    goalCompletion: 'Goal Completion',

    // Sections
    accountBreakdown: 'Account Breakdown',
    monthlyTrends: 'Monthly Trends',
    smartInsights: 'Smart Insights',

    // Chart labels
    deposits: 'Deposits',
    withdrawals: 'Withdrawals',

    // Insights
    excellentProgress: 'Excellent Progress!',
    goalProgressMessage: "You're {percentage}% towards your savings goals",
    goalAlert: 'Goal Alert',
    increaseContributionsMessage: 'Consider increasing your monthly contributions to reach your goals faster',
    emergencyFundRecommendation: 'Emergency Fund Recommendation',
    createEmergencyFundMessage: 'Consider creating an emergency fund for unexpected expenses',
    buildEmergencyFund: 'Build Emergency Fund',
    emergencyFundGoalMessage: 'Aim for 3-6 months of expenses in your emergency fund',
    earningInterest: 'Earning Interest!',
    interestEarnedMessage: "You've earned {amount} in interest",

    // States and messages
    loadingAnalytics: 'Loading analytics...',
    noAnalyticsAvailable: 'No Analytics Available',
    createAccountsToSeeAnalytics: 'Create savings accounts and start saving to see detailed analytics and insights.',
    createSavingsAccount: 'Create Savings Account',
    pleaseLogInToViewAnalytics: 'Please log in to view analytics',
    failedToLoadAnalytics: 'Failed to load analytics',
    exportAnalyticsComingSoon: 'Export analytics will be available soon!',
  },

  // Investment Analytics
  investmentAnalytics: {
    title: 'Investment Analytics',

    // Performance metrics
    performanceMetrics: 'Performance Metrics',
    totalReturn: 'Total Return',
    annualizedReturn: 'Annualized Return',
    volatility: 'Volatility',
    sharpeRatio: 'Sharpe Ratio',
    maxDrawdown: 'Max Drawdown',
    winRate: 'Win Rate',

    // Risk analysis
    riskAnalysis: 'Risk Analysis',
    bestDay: 'Best Day',
    worstDay: 'Worst Day',
    totalTrades: 'Total Trades',
    avgHolding: 'Avg Holding',
    portfolioBeta: 'Portfolio Beta',

    // Allocation
    sectorAllocation: 'Sector Allocation',
    assetAllocation: 'Asset Allocation',
    advancedAnalytics: 'Advanced Analytics',

    // Export functionality
    exportAnalytics: 'Export Analytics',
    chooseExportFormat: 'Choose export format:',
    pdfReport: 'PDF Report',
    excelData: 'Excel Data',

    // Success and error messages
    exportSuccessful: 'Export Successful',
    exportFailed: 'Export Failed',
    noData: 'No Data',
    noAnalyticsDataAvailable: 'No analytics data available to export',
    pdfExportSuccess: 'Investment analytics exported as PDF successfully!',
    pdfReportGenerated: 'PDF report has been generated successfully!',
    pdfExportFailed: 'Failed to export analytics as PDF',
    csvExportSuccess: 'Investment analytics exported as CSV successfully!',
    csvFileGenerated: 'CSV file has been generated successfully!',
    csvExportFailed: 'Failed to export analytics as CSV',

    // States
    loadingAnalytics: 'Loading analytics...',
    failedToLoadAnalytics: 'Failed to load analytics',
  },

  // Budget Insights
  budgetInsights: {
    title: 'Budget Insights',
    aiPoweredRecommendations: 'AI-Powered Recommendations',

    // Budget score
    budgetScore: 'Budget Score',
    excellent: 'Excellent',
    good: 'Good',
    fair: 'Fair',
    needsImprovement: 'Needs Improvement',
    excellentDescription: "You're doing great! Keep it up.",
    goodDescription: 'Good progress! A few tweaks can improve your score.',
    improvementDescription: "There's room for improvement. Let's work on it together.",

    // Sections
    aiRecommendations: '🤖 AI Recommendations',
    quickWins: '⚡ Quick Wins',
    easyChangesWithImmediateImpact: 'Easy changes with immediate impact',
    personalizedTips: '💡 Personalized Tips',
    basedOnSpendingPatterns: 'Based on your spending patterns',

    // Recommendation details
    impact: 'Impact',
    actionSteps: 'Action Steps',

    // Alert messages
    readyToStartSaving: 'Ready to start saving?\\n\\nAction Steps:\\n{steps}',
    readyToOptimizeBills: 'Ready to optimize your bills?\\n\\nAction Steps:\\n{steps}',
    readyToBuildEmergencyFund: 'Ready to build your emergency fund?\\n\\nAction Steps:\\n{steps}',
    readyToImplementRecommendation: 'Ready to implement this recommendation?\\n\\nAction Steps:\\n{steps}',

    // Button texts
    setReminder: 'Set Reminder',
    autoSavings: 'Auto Savings',
    startSaving: 'Start Saving',
    startNow: 'Start Now',
    createFund: 'Create Fund',

    // States
    generatingAIInsights: 'Generating AI insights...',
  },

  // Components
  components: {
    // Contact picker
    selectContact: 'Select Contact',
    searchContacts: 'Search contacts...',
    noContactsFound: 'No contacts found',
    permissionRequired: 'Permission required',

    // OTP input
    enterOtp: 'Enter OTP',
    invalidOtpFormat: 'Invalid OTP format',
    otpRequired: 'OTP is required',

    // Wallet card
    availableBalance: 'Available Balance',
    hideBalance: 'Hide Balance',
    showBalance: 'Show Balance',
    balanceHidden: '****',

    // Transaction modal
    transactionDetails: 'Transaction Details',
    close: 'Close',
    shareReceipt: 'Share Receipt',
    downloadReceipt: 'Download Receipt',

    // Loading states
    loadingContacts: 'Loading contacts...',
    loadingTransactions: 'Loading transactions...',
    loadingData: 'Loading data...',

    // Empty states
    noDataAvailable: 'No data available',
    noResultsFound: 'No results found',
    tryAgainLater: 'Please try again later'
  },

  // Help & Support
  support: {
    frequentlyAskedQuestions: 'Frequently Asked Questions',
    contactSupport: 'Contact Support',
    reportIssue: 'Report an Issue',
    feedbackSuggestions: 'Feedback & Suggestions',
    userGuide: 'User Guide',
    termsConditions: 'Terms & Conditions',
    howCanWeHelpYou: 'How can we help you?',
    findAnswersToCommonQuestionsAboutUsingJiranipayIfY: 'Find answers to common questions about using JiraniPay. If you need more help, our support team is always ready to assist.',
    stillNeedHelp: 'Still need help?',
    ourCustomerSupportTeamIsAvailable247ToAssistYou: 'Our customer support team is available 24/7 to assist you with any questions or concerns.',

    // FAQ Categories
    moneyTransfer: 'Money Transfer',
    billPayments: 'Bill Payments',
    walletManagement: 'Wallet Management',
    qrCodeScanner: 'QR Code Scanner',
    accountSecurity: 'Account Security',

    // FAQ Questions
    howDoISendMoney: 'How do I send money to another JiraniPay user?',
    whatAreTransferLimits: 'What are the transfer limits?',
    howLongDoTransfersTake: 'How long do transfers take?',
    whichBillsCanIPay: 'Which bills can I pay through JiraniPay?',
    howDoIPayUtilityBills: 'How do I pay utility bills?',
    canIScheduleRecurringPayments: 'Can I schedule recurring bill payments?',
    howDoIAddMoney: 'How do I add money to my wallet?',
    isMyMoneySafe: 'Is my money safe in JiraniPay wallet?',
    canIWithdrawMoney: 'Can I withdraw money from my wallet?',
    howDoIUseQRScanner: 'How do I use the QR code scanner?',
    whatCanIDoWithQRCodes: 'What can I do with QR codes?',
    howDoIGenerateQRCode: 'How do I generate my QR code?',
    howDoIVerifyAccount: 'How do I verify my account?',
    whatSecurityFeatures: 'What security features does JiraniPay offer?',
    whatIfISuspectFraud: 'What should I do if I suspect fraud?',

    // FAQ Answers
    sendMoneyAnswer: 'To send money: 1) Go to the Send Money section, 2) Enter the recipient\'s phone number or scan their QR code, 3) Enter the amount, 4) Add a note (optional), 5) Confirm with your PIN. The money will be transferred instantly.',
    transferLimitsAnswer: 'Daily limits depend on your verification level: Basic (UGX 500,000), Verified (UGX 2,000,000), Premium (UGX 10,000,000). You can increase limits by completing account verification.',
    transferTimeAnswer: 'JiraniPay to JiraniPay transfers are instant. Bank transfers take 1-3 business days. Mobile money transfers are usually instant but may take up to 30 minutes during peak times.',
    billPaymentsAnswer: 'You can pay: Electricity (UMEME, KPLC, TANESCO), Water bills, Internet/Cable TV, School fees, Insurance premiums, Government services, and Mobile airtime/data for all major networks across East Africa.',
    payUtilityBillsAnswer: 'Go to Bill Payments → Select utility type → Enter your account/meter number → Confirm amount → Pay with wallet balance or linked account. You\'ll receive instant confirmation and receipt.',
    recurringPaymentsAnswer: 'Yes! When paying a bill, select "Set as Recurring" and choose frequency (weekly, monthly, quarterly). JiraniPay will automatically pay your bills on the scheduled dates.',
    addMoneyAnswer: 'You can top up via: 1) Bank transfer (instant), 2) Mobile money (MTN, Airtel, etc.), 3) Agent locations, 4) Debit/Credit cards. Go to Wallet → Add Money and select your preferred method.',
    moneySafetyAnswer: 'Yes! Your funds are secured with bank-level encryption, stored in regulated financial institutions, and protected by insurance. We use multi-factor authentication and real-time fraud monitoring.',
    withdrawMoneyAnswer: 'Yes, you can withdraw to: Your linked bank account (free), Mobile money account (small fee), or visit any JiraniPay agent location for cash withdrawal.',
    qrScannerAnswer: 'Tap the QR scanner icon → Point your camera at any JiraniPay QR code → The app will automatically detect and process the payment or contact information. Perfect for quick payments to merchants!',
    qrCodeUsesAnswer: 'QR codes allow you to: Pay merchants instantly, Add contacts quickly, Receive payments (generate your own QR), Access special offers, and Join group payments or savings circles.',
    generateQRAnswer: 'Go to Receive Money → Tap "Generate QR Code" → Set amount (optional) → Share the QR code. Others can scan it to send you money instantly without typing your phone number.',
    verifyAccountAnswer: 'Go to Profile → Account Verification → Upload: Valid ID (National ID, Passport, or Driver\'s License), Proof of address (utility bill or bank statement), and take a selfie. Verification usually takes 24-48 hours.',
    securityFeaturesAnswer: 'We provide: Biometric login (fingerprint/face), Two-factor authentication, Transaction PINs, Real-time fraud monitoring, Account alerts, and the ability to freeze your account instantly if needed.',
    fraudAnswer: 'Immediately: 1) Freeze your account in the app, 2) Contact support via the app or hotline, 3) Change your PIN and password, 4) Report the incident. We have 24/7 fraud monitoring and will investigate promptly.',

    // AI Chat Screen
    jiranipayAiAssistant: 'JiraniPay AI Assistant',
    startingAiAssistant: 'Starting AI Assistant...',
    aiIsTyping: 'AI is typing...',
    quickHelp: 'Quick Help',
    quickActions: 'Quick Actions',
    chooseWhatYoudLikeHelpWith: 'Choose what you\'d like help with',
    tipYouCanAlsoTypeYourQuestionsNaturally: 'Tip: You can also type your questions naturally',
    pleaseLogInToUseAiChat: 'Please log in to use AI chat',
    failedToInitializeChat: 'Failed to initialize chat',
    failedToStartChatSession: 'Failed to start chat session',
    failedToSendMessagePleaseTryAgain: 'Failed to send message. Please try again.',
    failedToSendMessage: 'Failed to send message',
    clearChatHistory: 'Clear Chat History',
    areYouSureYouWantToClearAllChatMessagesThisActionC: 'Are you sure you want to clear all chat messages? This action cannot be undone.',
    clear: 'Clear',
    aiIsThinking: '🤖 AI is thinking...',
    readyToHelpWithJiraniPay: '✨ Ready to help with JiraniPay',

    // Contact Support Screen
    wereHereToHelpYou247: 'We\'re here to help you 24/7',
    customerSupport: 'Customer Support',
    ourDedicatedSupportTeamIsAvailable247ToAssistYouWi: 'Our dedicated support team is available 24/7 to assist you with any questions or concerns.',
    createTicket: 'Create Ticket',
    submitADetailedSupportRequest: 'Submit a detailed support request',
    aiAssistant: 'AI Assistant',
    getInstantHelpFromAi: 'Get instant help from AI',
    contactMethods: 'Contact Methods',
    chooseTheBestWayToReachUsBasedOnYourNeeds: 'Choose the best way to reach us based on your needs',
    whatCanWeHelpYouWith: 'What can we help you with?',
    browseCommonSupportCategoriesToFindTheRightAssista: 'Browse common support categories to find the right assistance',
    emergencySupport: 'Emergency Support',
    securityEmergency: 'Security Emergency',
    ifYouSuspectFraudOrUnauthorizedAccessToYourAccount: 'If you suspect fraud or unauthorized access to your account, contact us immediately.',
    supportHours: 'Support Hours',
    phoneSupport: 'Phone Support',
    alwaysAvailable247: '24/7 - Always Available',
    emailSupport: 'Email Support',
    mondayFriday800Am600Pm: 'Monday-Friday 8:00 AM - 6:00 PM',
    technicalSupport: 'Technical Support',
    mondayFriday800Am800Pm: 'Monday-Friday 8:00 AM - 8:00 PM',
    additionalResources: 'Additional Resources',
    securityFaq: 'Security FAQ',
    findAnswersToCommonSecurityQuestions: 'Find answers to common security questions',
    securityTips: 'Security Tips',
    learnHowToKeepYourAccountSecure: 'Learn how to keep your account secure',
    callSupport: 'Call Support',
    doYouWantToCallPhonenumber: 'Do you want to call {phoneNumber}?',
    whatsappSupport: 'WhatsApp Support',
    doYouWantToMessageUsOnWhatsapp: 'Do you want to message us on WhatsApp?',
    whatsappNotAvailable: 'WhatsApp Not Available',
    whatsappIsNotInstalledOnYourDevicePleaseInstallWha: 'WhatsApp is not installed on your device. Please install WhatsApp or use another contact method.',
    doYouWantToSendAnEmailToEmail: 'Do you want to send an email to {email}?',
    commonIssuesInThisCategory: 'Common issues in this category:',
    callInstead: 'Call Instead'
  }
};
