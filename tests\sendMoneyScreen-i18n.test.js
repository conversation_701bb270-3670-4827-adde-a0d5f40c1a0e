/**
 * SendMoneyScreen i18n Implementation Test
 * 
 * Tests to verify that SendMoneyScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing SendMoneyScreen.js i18n Implementation\n');

// Read the SendMoneyScreen.js file
const sendMoneyScreenPath = path.join(__dirname, '../screens/SendMoneyScreen.js');
const sendMoneyScreenContent = fs.readFileSync(sendMoneyScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = sendMoneyScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = sendMoneyScreenContent.includes('const { t }') || sendMoneyScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded Alert.alert strings replacement
console.log('\n✅ Test 3: Hardcoded Alert.alert strings replacement');
const hardcodedAlertStrings = [
  'Error',
  'Failed to load contacts. Please try again.',
  'Contacts Permission Required',
  'To send money to your contacts, please allow access to your contacts in Settings. You can still enter phone numbers manually.',
  'No phone number found for this contact',
  'QR Scanner',
  'Failed to open QR scanner. Please try again.'
];

let hardcodedAlertsFound = [];
hardcodedAlertStrings.forEach(str => {
  // Check if the string appears in Alert.alert calls (not in translation keys)
  const alertPattern = new RegExp(`Alert\\.alert\\([^)]*['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (alertPattern.test(sendMoneyScreenContent)) {
    hardcodedAlertsFound.push(str);
  }
});

if (hardcodedAlertsFound.length === 0) {
  console.log('   All hardcoded Alert.alert strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Alert.alert strings still found: ❌ FAIL');
  hardcodedAlertsFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 4: Check for hardcoded Text component strings
console.log('\n✅ Test 4: Hardcoded Text component strings');
const hardcodedTextStrings = [
  'Enter Number',
  'Scan QR',
  'Request',
  'Loading contacts...',
  'All Contacts',
  'Recent',
  'Favorites',
  'No Recent Transfers',
  'No Favorite Contacts',
  'No Contacts Found',
  'No Contacts Available'
];

let hardcodedTextFound = [];
hardcodedTextStrings.forEach(str => {
  // Check if the string appears in Text components (not in translation keys)
  const textPattern = new RegExp(`<Text[^>]*>\\s*${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*</Text>`, 'g');
  const directTextPattern = new RegExp(`['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (textPattern.test(sendMoneyScreenContent) || 
      (directTextPattern.test(sendMoneyScreenContent) && !sendMoneyScreenContent.includes(`t('sendMoney.`))) {
    hardcodedTextFound.push(str);
  }
});

if (hardcodedTextFound.length === 0) {
  console.log('   All hardcoded Text strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Text strings still found: ❌ FAIL');
  hardcodedTextFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 5: Check for proper translation key usage
console.log('\n✅ Test 5: Translation key usage');
const translationKeys = [
  't(\'sendMoney.failedToLoadContacts\')',
  't(\'sendMoney.contactsPermissionRequired\')',
  't(\'sendMoney.contactsPermissionMessage\')',
  't(\'sendMoney.noPhoneNumberFound\')',
  't(\'sendMoney.qrScanner\')',
  't(\'sendMoney.failedToOpenQRScanner\')',
  't(\'sendMoney.enterNumber\')',
  't(\'sendMoney.scanQR\')',
  't(\'sendMoney.request\')',
  't(\'sendMoney.loadingContacts\')',
  't(\'sendMoney.searchContactsPlaceholder\')',
  't(\'sendMoney.allContacts\')',
  't(\'sendMoney.recent\')',
  't(\'sendMoney.favorites\')',
  't(\'sendMoney.manualEntry\')'
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (sendMoneyScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check that required translation keys exist in en.js
console.log('\n✅ Test 6: Translation keys in en.js');
const requiredKeys = [
  'failedToLoadContacts:',
  'contactsPermissionRequired:',
  'contactsPermissionMessage:',
  'noPhoneNumberFound:',
  'qrScanner:',
  'failedToOpenQRScanner:',
  'enterNumber:',
  'scanQR:',
  'request:',
  'loadingContacts:',
  'searchContactsPlaceholder:',
  'allContacts:',
  'recent:',
  'favorites:',
  'manualEntry:',
  'noRecentTransfers:',
  'noFavoriteContacts:',
  'noContactsFound:',
  'noContactsAvailable:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for Alert.alert with proper translation keys
console.log('\n✅ Test 7: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = sendMoneyScreenContent.match(alertPattern);
const totalAlerts = (sendMoneyScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 8: Check for placeholder text internationalization
console.log('\n✅ Test 8: Placeholder text internationalization');
const placeholderPattern = /placeholder=\{t\(/g;
const placeholderMatches = sendMoneyScreenContent.match(placeholderPattern);
const totalPlaceholders = (sendMoneyScreenContent.match(/placeholder=/g) || []).length;
const translatedPlaceholders = placeholderMatches ? placeholderMatches.length : 0;

console.log(`   Translated placeholders: ${translatedPlaceholders}/${totalPlaceholders} ${translatedPlaceholders === totalPlaceholders ? '✅ PASS' : '❌ PARTIAL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 8;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (hardcodedAlertsFound.length === 0) passedTests++;
if (hardcodedTextFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (translatedPlaceholders === totalPlaceholders) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 SendMoneyScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
} else {
  console.log('\n⚠️  SendMoneyScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test send money flow with different languages');
console.log('2. Verify contact picker and QR scanner integration');
console.log('3. Test tab switching and empty states');
console.log('4. Proceed to BillPaymentScreen.js implementation');
