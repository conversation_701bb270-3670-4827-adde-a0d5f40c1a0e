/**
 * BudgetInsightsScreen i18n Implementation Test
 * 
 * Tests to verify that BudgetInsightsScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing BudgetInsightsScreen.js i18n Implementation\n');

// Read the BudgetInsightsScreen.js file
const budgetInsightsScreenPath = path.join(__dirname, '../screens/BudgetInsightsScreen.js');
const budgetInsightsScreenContent = fs.readFileSync(budgetInsightsScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = budgetInsightsScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = budgetInsightsScreenContent.includes('const { t }') || budgetInsightsScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded Alert.alert strings replacement
console.log('\n✅ Test 3: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = budgetInsightsScreenContent.match(alertPattern);
const totalAlerts = (budgetInsightsScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 4: Check for hardcoded Text component strings
console.log('\n✅ Test 4: Hardcoded Text component strings');
const hardcodedTextStrings = [
  'Budget Insights',
  'AI-Powered Recommendations',
  'Budget Score',
  'Excellent',
  'Good',
  'Fair',
  'Needs Improvement',
  'AI Recommendations',
  'Quick Wins',
  'Easy changes with immediate impact',
  'Personalized Tips',
  'Based on your spending patterns',
  'Impact:',
  'Action Steps:',
  'Generating AI insights...'
];

let hardcodedTextFound = [];
hardcodedTextStrings.forEach(str => {
  const textPattern = new RegExp(`<Text[^>]*>\\s*${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*</Text>`, 'g');
  const directTextPattern = new RegExp(`['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (textPattern.test(budgetInsightsScreenContent) || 
      (directTextPattern.test(budgetInsightsScreenContent) && !budgetInsightsScreenContent.includes(`t('budgetInsights.`))) {
    hardcodedTextFound.push(str);
  }
});

if (hardcodedTextFound.length === 0) {
  console.log('   All hardcoded Text strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Text strings still found: ❌ FAIL');
  hardcodedTextFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 5: Check for proper translation key usage
console.log('\n✅ Test 5: Translation key usage');
const translationKeys = [
  't(\'budgetInsights.title\')',
  't(\'budgetInsights.aiPoweredRecommendations\')',
  't(\'budgetInsights.budgetScore\')',
  't(\'budgetInsights.excellent\')',
  't(\'budgetInsights.good\')',
  't(\'budgetInsights.fair\')',
  't(\'budgetInsights.needsImprovement\')',
  't(\'budgetInsights.aiRecommendations\')',
  't(\'budgetInsights.quickWins\')',
  't(\'budgetInsights.personalizedTips\')',
  't(\'budgetInsights.impact\')',
  't(\'budgetInsights.actionSteps\')',
  't(\'budgetInsights.generatingAIInsights\')'
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (budgetInsightsScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check for string interpolation usage
console.log('\n✅ Test 6: String interpolation');
const interpolationPatterns = [
  't(\'budgetInsights.readyToStartSaving\', {',
  't(\'budgetInsights.readyToOptimizeBills\', {',
  't(\'budgetInsights.readyToBuildEmergencyFund\', {',
  't(\'budgetInsights.readyToImplementRecommendation\', {'
];

let interpolationFound = 0;
interpolationPatterns.forEach(pattern => {
  if (budgetInsightsScreenContent.includes(pattern)) {
    interpolationFound++;
  }
});

console.log(`   String interpolation patterns: ${interpolationFound}/${interpolationPatterns.length} ${interpolationFound === interpolationPatterns.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for score label function internationalization
console.log('\n✅ Test 7: Score label function internationalization');
const scoreLabelPattern = /getScoreLabel.*t\('budgetInsights\./g;
const scoreLabelMatches = budgetInsightsScreenContent.match(scoreLabelPattern);
const scoreLabelInternationalized = scoreLabelMatches && scoreLabelMatches.length > 0;

console.log(`   Score label function internationalized: ${scoreLabelInternationalized ? '✅ PASS' : '❌ FAIL'}`);

// Test 8: Check that required translation keys exist in en.js
console.log('\n✅ Test 8: Translation keys in en.js');
const requiredKeys = [
  'title:',
  'aiPoweredRecommendations:',
  'budgetScore:',
  'excellent:',
  'good:',
  'fair:',
  'needsImprovement:',
  'excellentDescription:',
  'goodDescription:',
  'improvementDescription:',
  'aiRecommendations:',
  'quickWins:',
  'easyChangesWithImmediateImpact:',
  'personalizedTips:',
  'basedOnSpendingPatterns:',
  'impact:',
  'actionSteps:',
  'readyToStartSaving:',
  'readyToOptimizeBills:',
  'readyToBuildEmergencyFund:',
  'readyToImplementRecommendation:',
  'setReminder:',
  'autoSavings:',
  'startSaving:',
  'startNow:',
  'createFund:',
  'generatingAIInsights:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 9: Check budgetInsights section exists in en.js
console.log('\n✅ Test 9: BudgetInsights section in en.js');
const budgetInsightsSection = enTranslationsContent.includes('budgetInsights: {');
console.log(`   BudgetInsights section exists: ${budgetInsightsSection ? '✅ PASS' : '❌ FAIL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 9;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (hardcodedTextFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (interpolationFound === interpolationPatterns.length) passedTests++;
if (scoreLabelInternationalized) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (budgetInsightsSection) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 BudgetInsightsScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
  console.log('   Budget analysis, spending patterns, and recommendation text are internationalized!');
} else {
  console.log('\n⚠️  BudgetInsightsScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test budget insights with different languages');
console.log('2. Verify AI recommendations display correctly');
console.log('3. Test dynamic value interpolation in recommendations');
console.log('4. Analytics and Insights Screens implementation COMPLETE!');
