/**
 * Specific Keys Debug Test
 * 
 * Tests the exact keys that are showing as raw camelCase in the app
 * to identify why they're not being translated properly
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 SPECIFIC KEYS DEBUG TEST\n');

// Load translation files
let enTranslations, swTranslations;

try {
  delete require.cache[require.resolve('../locales/en.js')];
  delete require.cache[require.resolve('../locales/sw.js')];
  
  enTranslations = require('../locales/en.js').default;
  swTranslations = require('../locales/sw.js').default;
  
  console.log('✅ Translation files loaded successfully\n');
} catch (error) {
  console.log('❌ Error loading translation files:', error.message);
  process.exit(1);
}

// Simulate the translation function from LanguageContext
const simulateTranslation = (translations, key, language = 'en') => {
  console.log(`🔍 Testing key "${key}" in ${language.toUpperCase()}:`);
  
  if (!key || typeof key !== 'string') {
    console.log(`   ❌ Invalid key type: ${typeof key}`);
    return key;
  }

  const keys = key.split('.');
  let translation = translations;
  let path = '';

  console.log(`   📍 Key path: ${keys.join(' → ')}`);

  // Navigate through nested keys
  for (const k of keys) {
    path += (path ? '.' : '') + k;
    
    if (translation && typeof translation === 'object') {
      translation = translation[k];
      console.log(`   📂 ${path}: ${translation !== undefined ? '✅ Found' : '❌ Not found'} (type: ${typeof translation})`);
    } else {
      console.log(`   ❌ ${path}: Cannot navigate further (current type: ${typeof translation})`);
      translation = undefined;
      break;
    }
  }

  // Check final result
  if (translation === undefined || translation === null) {
    console.log(`   ❌ Final result: undefined/null`);
    return key; // This is what causes the raw key to show
  }

  if (typeof translation === 'object' && translation !== null) {
    console.log(`   ❌ Final result: object with keys [${Object.keys(translation).join(', ')}]`);
    return key; // This also causes the raw key to show
  }

  if (typeof translation === 'string') {
    console.log(`   ✅ Final result: "${translation}"`);
    return translation;
  }

  console.log(`   ❌ Final result: unexpected type ${typeof translation}`);
  return key;
};

// Test the problematic keys from the screenshots
const problematicKeys = [
  'editProfile',
  'fullName', 
  'emailAddress',
  'phoneNumber',
  'accountVerification',
  'privacyAndData'
];

console.log('🎯 TESTING PROBLEMATIC KEYS:\n');
console.log('=' .repeat(60));

problematicKeys.forEach(key => {
  console.log(`\n📱 KEY: "${key}"`);
  console.log('-'.repeat(40));
  
  // Test in English
  const enResult = simulateTranslation(enTranslations, key, 'en');
  
  // Test in Swahili  
  const swResult = simulateTranslation(swTranslations, key, 'sw');
  
  console.log(`\n📊 SUMMARY for "${key}":`);
  console.log(`   English: ${enResult === key ? '❌ FAILED' : '✅ SUCCESS'} → "${enResult}"`);
  console.log(`   Swahili: ${swResult === key ? '❌ FAILED' : '✅ SUCCESS'} → "${swResult}"`);
  
  if (enResult === key || swResult === key) {
    console.log(`   🚨 This key will show as raw camelCase in the app!`);
  }
});

// Test nested keys that might work
console.log('\n\n🔍 TESTING NESTED ALTERNATIVES:\n');
console.log('=' .repeat(60));

const nestedAlternatives = [
  'common.editProfile',
  'profile.editProfile', 
  'common.fullName',
  'profile.fullName',
  'common.accountVerification',
  'verification.accountVerification'
];

nestedAlternatives.forEach(key => {
  console.log(`\n📱 NESTED KEY: "${key}"`);
  console.log('-'.repeat(40));
  
  const enResult = simulateTranslation(enTranslations, key, 'en');
  const swResult = simulateTranslation(swTranslations, key, 'sw');
  
  console.log(`\n📊 SUMMARY for "${key}":`);
  console.log(`   English: ${enResult === key ? '❌ FAILED' : '✅ SUCCESS'} → "${enResult}"`);
  console.log(`   Swahili: ${swResult === key ? '❌ FAILED' : '✅ SUCCESS'} → "${swResult}"`);
});

// Check for duplicate keys
console.log('\n\n🔍 CHECKING FOR DUPLICATE KEYS:\n');
console.log('=' .repeat(60));

const checkDuplicates = (obj, prefix = '', duplicates = {}) => {
  Object.keys(obj).forEach(key => {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof obj[key] === 'string') {
      if (!duplicates[key]) {
        duplicates[key] = [];
      }
      duplicates[key].push(fullKey);
    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
      checkDuplicates(obj[key], fullKey, duplicates);
    }
  });
  
  return duplicates;
};

const enDuplicates = checkDuplicates(enTranslations);
const swDuplicates = checkDuplicates(swTranslations);

problematicKeys.forEach(key => {
  console.log(`\n🔑 "${key}" locations:`);
  
  if (enDuplicates[key]) {
    console.log(`   English: Found in ${enDuplicates[key].length} locations:`);
    enDuplicates[key].forEach(location => {
      console.log(`     - ${location}`);
    });
  } else {
    console.log(`   English: ❌ Not found anywhere`);
  }
  
  if (swDuplicates[key]) {
    console.log(`   Swahili: Found in ${swDuplicates[key].length} locations:`);
    swDuplicates[key].forEach(location => {
      console.log(`     - ${location}`);
    });
  } else {
    console.log(`   Swahili: ❌ Not found anywhere`);
  }
});

console.log('\n\n🎯 DIAGNOSIS:\n');
console.log('=' .repeat(60));
console.log('If keys are showing as raw camelCase in the app, the most likely causes are:');
console.log('1. ❌ Key not found in translation files');
console.log('2. ❌ Key returns an object instead of a string');
console.log('3. ❌ Translation function not working properly');
console.log('4. ❌ App not using the correct translation context');
console.log('5. ❌ Caching issues preventing updates');

console.log('\n🔧 NEXT STEPS:');
console.log('==============');
console.log('1. Check which keys are failing in the test above');
console.log('2. Verify the app is using the correct useLanguage hook');
console.log('3. Check for console warnings in the app');
console.log('4. Clear all caches and restart the app');
