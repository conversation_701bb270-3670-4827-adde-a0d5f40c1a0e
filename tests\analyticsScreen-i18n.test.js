/**
 * AnalyticsScreen i18n Implementation Test
 * 
 * Tests to verify that AnalyticsScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing AnalyticsScreen.js i18n Implementation\n');

// Read the AnalyticsScreen.js file
const analyticsScreenPath = path.join(__dirname, '../screens/AnalyticsScreen.js');
const analyticsScreenContent = fs.readFileSync(analyticsScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = analyticsScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = analyticsScreenContent.includes('const { t }') || analyticsScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded Alert.alert strings (should be none)
console.log('\n✅ Test 3: Alert.alert calls (should be none)');
const alertPattern = /Alert\.alert\(/g;
const alertMatches = analyticsScreenContent.match(alertPattern);
const alertCount = alertMatches ? alertMatches.length : 0;
console.log(`   Alert.alert calls found: ${alertCount} ${alertCount === 0 ? '✅ PASS' : '❌ FAIL'}`);

// Test 4: Check for hardcoded Text component strings
console.log('\n✅ Test 4: Hardcoded Text component strings');
const hardcodedTextStrings = [
  'Spending Analytics',
  'Spending by Category',
  'Spending Trend',
  'Financial Insights',
  'Quick Budget Tips',
  'Loading analytics...',
  'No spending data available',
  'Expenses',
  'Income',
  'Week',
  'Month',
  'Year',
  'transactions'
];

let hardcodedTextFound = [];
hardcodedTextStrings.forEach(str => {
  // Check if the string appears in Text components (not in translation keys)
  const textPattern = new RegExp(`<Text[^>]*>\\s*${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*</Text>`, 'g');
  const directTextPattern = new RegExp(`['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (textPattern.test(analyticsScreenContent) || 
      (directTextPattern.test(analyticsScreenContent) && !analyticsScreenContent.includes(`t('analytics.`))) {
    hardcodedTextFound.push(str);
  }
});

if (hardcodedTextFound.length === 0) {
  console.log('   All hardcoded Text strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Text strings still found: ❌ FAIL');
  hardcodedTextFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 5: Check for proper translation key usage
console.log('\n✅ Test 5: Translation key usage');
const translationKeys = [
  't(\'analytics.spendingAnalytics\')',
  't(\'analytics.spendingByCategory\')',
  't(\'analytics.spendingTrend\')',
  't(\'analytics.financialInsights\')',
  't(\'analytics.quickBudgetTips\')',
  't(\'analytics.loadingAnalytics\')',
  't(\'analytics.noSpendingDataAvailable\')',
  't(\'analytics.expenses\')',
  't(\'analytics.income\')',
  't(\'analytics.week\')',
  't(\'analytics.month\')',
  't(\'analytics.year\')',
  't(\'analytics.transactionsCount\''
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (analyticsScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check that required translation keys exist in en.js
console.log('\n✅ Test 6: Translation keys in en.js');
const requiredKeys = [
  'week:',
  'month:',
  'year:',
  'spendingAnalytics:',
  'spendingByCategory:',
  'spendingTrend:',
  'financialInsights:',
  'quickBudgetTips:',
  'expenses:',
  'income:',
  'transactionsCount:',
  'loadingAnalytics:',
  'noSpendingDataAvailable:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for string interpolation usage
console.log('\n✅ Test 7: String interpolation');
const interpolationPattern = /t\('analytics\.transactionsCount',\s*\{\s*count:/g;
const interpolationMatches = analyticsScreenContent.match(interpolationPattern);
const interpolationCount = interpolationMatches ? interpolationMatches.length : 0;

console.log(`   String interpolation usage: ${interpolationCount} instances ${interpolationCount > 0 ? '✅ PASS' : '❌ FAIL'}`);

// Test 8: Check for periods array internationalization
console.log('\n✅ Test 8: Periods array internationalization');
const periodsPattern = /periods\s*=\s*\[[\s\S]*?t\('analytics\.(week|month|year)'\)/g;
const periodsMatches = analyticsScreenContent.match(periodsPattern);
const periodsInternationalized = periodsMatches && periodsMatches.length > 0;

console.log(`   Periods array internationalized: ${periodsInternationalized ? '✅ PASS' : '❌ FAIL'}`);

// Test 9: Check analytics section exists in en.js
console.log('\n✅ Test 9: Analytics section in en.js');
const analyticsSection = enTranslationsContent.includes('analytics: {');
console.log(`   Analytics section exists: ${analyticsSection ? '✅ PASS' : '❌ FAIL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 9;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (alertCount === 0) passedTests++;
if (hardcodedTextFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (interpolationCount > 0) passedTests++;
if (periodsInternationalized) passedTests++;
if (analyticsSection) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 AnalyticsScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
  console.log('   Chart labels, data visualization text, and dynamic values are internationalized!');
} else {
  console.log('\n⚠️  AnalyticsScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test analytics dashboard with different languages');
console.log('2. Verify chart labels display correctly');
console.log('3. Test dynamic value interpolation (amounts, percentages, dates)');
console.log('4. Proceed to AnalyticsExportScreen.js implementation');
