/**
 * Final Swahili Translation Check
 * 
 * Focuses on the specific issues mentioned by the user:
 * - Account verification page translations
 * - Edit profile page translations  
 * - Privacy and data page translations
 * - Raw camelCase keys appearing as text
 */

const fs = require('fs');
const path = require('path');

console.log('🎯 Final Swahili Translation Check\n');

// Load Swahili translation file
const swPath = path.join(__dirname, '../locales/sw.js');
const swContent = fs.readFileSync(swPath, 'utf8');

// Extract all translation keys and values
const extractTranslations = (content) => {
  const translations = {};
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    const match = line.match(/^\s*([a-zA-Z][a-zA-Z0-9]*)\s*:\s*'([^']*)'.*$/);
    if (match) {
      const [, key, value] = match;
      translations[key] = value;
    }
  }
  
  return translations;
};

const swTranslations = extractTranslations(swContent);

console.log('📊 Swahili Translation Statistics:');
console.log(`   Total translation keys: ${Object.keys(swTranslations).length}`);

// Check specific sections mentioned by user
console.log('\n🔐 Account Verification Page Check:');
const verificationKeys = [
  'accountVerification',
  'verificationSteps', 
  'phoneVerification',
  'emailVerification',
  'identityVerification',
  'biometricVerification',
  'uploadDocument',
  'takePhoto',
  'chooseFromGallery',
  'nationalId',
  'passport',
  'drivingLicense',
  'utilityBill',
  'verificationPending',
  'verificationComplete',
  'verificationFailed'
];

let verificationIssues = 0;
verificationKeys.forEach(key => {
  if (swTranslations[key]) {
    const value = swTranslations[key];
    if (value === key || /^[a-z]+[A-Z]/.test(value)) {
      console.log(`   ❌ ${key}: "${value}" (raw key or camelCase)`);
      verificationIssues++;
    } else {
      console.log(`   ✅ ${key}: "${value}"`);
    }
  } else {
    console.log(`   ❌ ${key}: MISSING`);
    verificationIssues++;
  }
});

console.log('\n👤 Edit Profile Page Check:');
const profileKeys = [
  'editProfile',
  'fullName',
  'emailAddress',
  'phoneNumber',
  'updateProfile',
  'profileUpdated',
  'profileUpdateFailed',
  'uploadProfilePicture',
  'takePhoto',
  'chooseFromGallery',
  'removePhoto'
];

let profileIssues = 0;
profileKeys.forEach(key => {
  if (swTranslations[key]) {
    const value = swTranslations[key];
    if (value === key || /^[a-z]+[A-Z]/.test(value)) {
      console.log(`   ❌ ${key}: "${value}" (raw key or camelCase)`);
      profileIssues++;
    } else {
      console.log(`   ✅ ${key}: "${value}"`);
    }
  } else {
    console.log(`   ❌ ${key}: MISSING`);
    profileIssues++;
  }
});

console.log('\n🔒 Privacy and Data Page Check:');
const privacyKeys = [
  'privacyAndData',
  'dataConsent',
  'privacyPolicy',
  'dataSharing',
  'yourDataRights',
  'exportMyData',
  'dataProtection',
  'deleteAccount',
  'exportData',
  'essentialServices',
  'analyticsAndPerformance',
  'marketingCommunications'
];

let privacyIssues = 0;
privacyKeys.forEach(key => {
  if (swTranslations[key]) {
    const value = swTranslations[key];
    if (value === key || /^[a-z]+[A-Z]/.test(value)) {
      console.log(`   ❌ ${key}: "${value}" (raw key or camelCase)`);
      privacyIssues++;
    } else {
      console.log(`   ✅ ${key}: "${value}"`);
    }
  } else {
    console.log(`   ❌ ${key}: MISSING`);
    privacyIssues++;
  }
});

// Check for camelCase values that might be raw keys
console.log('\n🔤 CamelCase Issues Check:');
const camelCasePattern = /^[a-z]+[A-Z][a-zA-Z]*$/;
const camelCaseIssues = [];

Object.entries(swTranslations).forEach(([key, value]) => {
  if (camelCasePattern.test(value)) {
    camelCaseIssues.push({ key, value });
  }
});

if (camelCaseIssues.length > 0) {
  console.log(`   Found ${camelCaseIssues.length} potential camelCase issues:`);
  camelCaseIssues.forEach(({ key, value }) => {
    console.log(`   ❌ ${key}: "${value}"`);
  });
} else {
  console.log('   ✅ No camelCase issues found');
}

// Check for English words that should be translated
console.log('\n🔤 English Words Check:');
const englishWords = ['Account', 'Verification', 'Profile', 'Settings', 'Privacy', 'Data', 'Security', 'Login', 'Password'];
const englishWordIssues = [];

Object.entries(swTranslations).forEach(([key, value]) => {
  englishWords.forEach(word => {
    if (value.includes(word) && word !== 'Data') { // We allow some Data usage for technical terms
      englishWordIssues.push({ key, value, word });
    }
  });
});

if (englishWordIssues.length > 0) {
  console.log(`   Found ${englishWordIssues.length} English word issues:`);
  englishWordIssues.slice(0, 10).forEach(({ key, value, word }) => {
    console.log(`   ❌ ${key}: "${value}" (contains "${word}")`);
  });
} else {
  console.log('   ✅ No problematic English words found');
}

// Check for authentication and form keys
console.log('\n🔐 Authentication & Forms Check:');
const authKeys = [
  'login',
  'signUp',
  'createAccount',
  'fullName',
  'emailAddress',
  'phoneNumber',
  'password',
  'confirmPassword',
  'termsAndConditions',
  'agreeToTerms',
  'agreeToPrivacyPolicy'
];

let authIssues = 0;
authKeys.forEach(key => {
  if (swTranslations[key]) {
    const value = swTranslations[key];
    if (value === key || /^[a-z]+[A-Z]/.test(value)) {
      console.log(`   ❌ ${key}: "${value}" (raw key or camelCase)`);
      authIssues++;
    } else {
      console.log(`   ✅ ${key}: "${value}"`);
    }
  } else {
    console.log(`   ❌ ${key}: MISSING`);
    authIssues++;
  }
});

// Summary
const totalIssues = verificationIssues + profileIssues + privacyIssues + camelCaseIssues.length + englishWordIssues.length + authIssues;

console.log('\n📋 FINAL SUMMARY:');
console.log('=================');
console.log(`Account Verification Issues: ${verificationIssues}`);
console.log(`Edit Profile Issues: ${profileIssues}`);
console.log(`Privacy & Data Issues: ${privacyIssues}`);
console.log(`Authentication Issues: ${authIssues}`);
console.log(`CamelCase Issues: ${camelCaseIssues.length}`);
console.log(`English Word Issues: ${englishWordIssues.length}`);
console.log(`Total Issues: ${totalIssues}`);

if (totalIssues === 0) {
  console.log('\n🎉 SWAHILI TRANSLATIONS: ✅ EXCELLENT!');
  console.log('   All main sections are properly translated!');
  console.log('   Ready for production use!');
} else if (totalIssues <= 5) {
  console.log('\n✅ SWAHILI TRANSLATIONS: 🔄 VERY GOOD');
  console.log('   Minor issues remaining, mostly ready for use.');
} else if (totalIssues <= 15) {
  console.log('\n⚠️  SWAHILI TRANSLATIONS: 🔄 GOOD PROGRESS');
  console.log('   Some issues need attention, but major sections are working.');
} else {
  console.log('\n❌ SWAHILI TRANSLATIONS: 🔄 NEEDS MORE WORK');
  console.log('   Several issues need to be addressed.');
}

console.log('\n🎯 USER EXPERIENCE IMPACT:');
console.log('==========================');
if (verificationIssues === 0) {
  console.log('✅ Account verification page: Fully translated');
} else {
  console.log(`❌ Account verification page: ${verificationIssues} issues`);
}

if (profileIssues === 0) {
  console.log('✅ Edit profile page: Fully translated');
} else {
  console.log(`❌ Edit profile page: ${profileIssues} issues`);
}

if (privacyIssues === 0) {
  console.log('✅ Privacy and data page: Fully translated');
} else {
  console.log(`❌ Privacy and data page: ${privacyIssues} issues`);
}

if (camelCaseIssues.length === 0) {
  console.log('✅ No raw translation keys showing as text');
} else {
  console.log(`❌ ${camelCaseIssues.length} raw keys might show as text`);
}

console.log('\n🚀 DEPLOYMENT STATUS:');
console.log('=====================');
if (totalIssues <= 3) {
  console.log('🟢 READY FOR PRODUCTION');
  console.log('   Users will see proper Swahili throughout the app');
} else if (totalIssues <= 10) {
  console.log('🟡 MOSTLY READY');
  console.log('   Most users will see proper Swahili with minor issues');
} else {
  console.log('🔴 NEEDS MORE WORK');
  console.log('   Users may see English text or raw keys in some areas');
}
