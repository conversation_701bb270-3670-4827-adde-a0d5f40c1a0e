/**
 * Swahili (sw) translations for JiraniPay
 * Complete translation coverage for all app features
 */

export default {
  // Common UI elements
  common: {
    continue: '<PERSON><PERSON><PERSON>',
    cancel: '<PERSON><PERSON><PERSON>',
    back: '<PERSON><PERSON>',
    next: '<PERSON><PERSON><PERSON><PERSON>',
    done: '<PERSON><PERSON><PERSON><PERSON>',
    loading: 'Inapakia...',
    error: '<PERSON><PERSON><PERSON>',
    success: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    retry: '<PERSON><PERSON><PERSON>',
    close: '<PERSON><PERSON>',
    save: '<PERSON><PERSON><PERSON>',
    edit: '<PERSON><PERSON>',
    delete: '<PERSON><PERSON>',
    confirm: 'Thibitisha',
    yes: 'Ndiyo',
    no: '<PERSON><PERSON>a',
    ok: '<PERSON><PERSON>',
    search: 'Ta<PERSON>ta',
    filter: 'Chuja',
    sort: 'Panga',
    refresh: 'Onyesha Upya',
    share: '<PERSON><PERSON>i',
    copy: 'Naki<PERSON>',
    paste: '<PERSON><PERSON>',
    select: 'Chagua',
    selectAll: 'Chagua Yote',
    clear: 'Futa',
    notAvailable: 'Haipatikani',
    friend: '<PERSON><PERSON><PERSON>',
    reset: '<PERSON><PERSON> Upya',
    apply: '<PERSON><PERSON>',
    submit: '<PERSON><PERSON><PERSON>',
    send: '<PERSON><PERSON>',
    receive: '<PERSON><PERSON><PERSON>',
    view: '<PERSON><PERSON>',
    hide: '<PERSON><PERSON>',
    show: '<PERSON><PERSON><PERSON>',
    enable: '<PERSON><PERSON>',
    disable: '<PERSON><PERSON>',
    on: '<PERSON><PERSON><PERSON><PERSON>',
    off: 'I<PERSON><PERSON><PERSON><PERSON>',
    active: 'Hai',
    inactive: 'Haikai',
    available: 'Inapatikana',
    unavailable: 'Haipatikani',
    online: 'Mtandaoni',
    offline: 'Nje ya Mtandao',
    connected: 'Imeunganishwa',
    disconnected: 'Imetenganishwa',
    pending: 'Inasubiri',
    completed: 'Imekamilika',
    failed: 'Imeshindwa',
    cancelled: 'Imeghairiwa',
    processing: 'Inachakatwa',
    verified: 'Imethibitishwa',
    unverified: 'Haijathibitishwa',
    required: 'Inahitajika',
    optional: 'Si Lazima',
    recommended: 'Inapendekezwa',
    new: 'Mpya',
    updated: 'Imesasishwa',
    recent: 'Za Hivi Karibuni',
    popular: 'Maarufu',
    featured: 'Zilizoangaziwa',
    all: 'Zote',
    none: 'Hakuna',
    other: 'Nyingine',
    more: 'Zaidi',
    less: 'Kidogo',
    today: 'Leo',
    yesterday: 'Jana',
    tomorrow: 'Kesho',
    thisWeek: 'Wiki Hii',
    thisMonth: 'Mwezi Huu',
    thisYear: 'Mwaka Huu',
    lastWeek: 'Wiki Iliyopita',
    lastMonth: 'Mwezi Uliopita',
    lastYear: 'Mwaka Uliopita',
    nextWeek: 'Wiki Ijayo',
    nextMonth: 'Mwezi Ujao',
    nextYear: 'Mwaka Ujao',

    // Additional common terms
    method: 'Njia',
    provider: 'Mtoa Huduma',
    service: 'Huduma',
    account: 'Akaunti',
    balance: 'Salio',
    amount: 'Kiasi',
    total: 'Jumla',
    fee: 'Ada',
    cost: 'Gharama',

    // Alert and error messages
    errorTitle: 'Hitilafu',
    successTitle: 'Imefanikiwa',
    warningTitle: 'Onyo',
    infoTitle: 'Taarifa',
    confirmTitle: 'Thibitisha',

    // Profile and account
    walletBalance: 'Salio la Mkoba',
    transactions: 'Miamala',
    accountLevel: 'Kiwango cha Akaunti',

    // Actions and buttons
    takePhoto: 'Piga Picha',
    chooseFromGallery: 'Chagua kutoka Galari',
    updateProfilePicture: 'Sasisha Picha ya Wasifu',
    chooseUpdateMethod: 'Chagua jinsi unavyotaka kusasisha picha yako ya wasifu',
    logout: 'Toka',
    logoutConfirm: 'Una uhakika unataka kutoka?',

    // Features and services
    comingSoon: 'Inakuja Hivi Karibuni',
    featureAvailable: 'Kipengele Kinapatikana',
    thisFeatureWillBeAvailable: 'Kipengele hiki kitapatikana hivi karibuni!',
    notifyMe: 'Nijulishe',

    // Time periods
    monthly: 'Kila Mwezi',
    weekly: 'Kila Wiki',
    daily: 'Kila Siku',
    yearly: 'Kila Mwaka',

    // Authentication & Login specific (Swahili)
    otpSent: 'OTP Imetumwa',
    pleaseCheckYourPhoneForTheVerificationCode: 'Tafadhali angalia simu yako kwa msimbo wa uthibitisho',
    error: 'Hitilafu',
    somethingWentWrongPleaseTryAgain: 'Kuna hitilafu. Tafadhali jaribu tena.',
    pleaseEnterAPasswordWithAtLeast6Characters: 'Tafadhali ingiza nywila yenye angalau herufi 6',
    passwordNotSet: 'Nywila Haijawekwa',
    yourAccountWasCreatedWithPhoneVerificationOnly: 'Akaunti yako iliundwa kwa uthibitisho wa simu tu. Je, ungependa kuweka nywila sasa au kuendelea na kuingia kwa OTP?',
    info: 'Taarifa',
    switchedToOtpLoginTap: 'Imebadilishwa kwenda kuingia kwa OTP. Bonyeza "Tuma OTP" kuendelea.',
    setUpPassword: 'Weka Nywila',
    toSetUpAPasswordWeNeedToVerifyYourIdentityFirst: 'Kuweka nywila, tunahitaji kuthibitisha utambulisho wako kwanza. OTP itatumwa kwenye simu yako.',
    pleaseVerifyTheOtpSentToYourPhone: 'Tafadhali thibitisha OTP iliyotumwa kwenye simu yako, kisha jaribu kuingia kwa nywila yako tena.',
    authenticationFailed: 'Uthibitisho Umeshindwa',
    pleaseTryAgainOrUsePhoneNumberLogin: 'Tafadhali jaribu tena au tumia kuingia kwa nambari ya simu',
    biometricAuthenticationFailed: 'Uthibitisho wa kibayolojia umeshindwa',
    otpResent: 'OTP Imetumwa Tena',
    aNewVerificationCodeHasBeenSentToYourPhone: 'Msimbo mpya wa uthibitisho umetumwa kwenye simu yako',

    // Wallet specific (Swahili)
    workingOfflineWithCachedData: 'Inafanya kazi nje ya mtandao na data iliyohifadhiwa',
    availableBalance: 'Salio Linaloweza Kutumika',
    topUp: 'Jaza',
    send: 'Tuma',
    history: 'Historia',
    settings: 'Mipangilio',
    spendingLimits: 'Mipaka ya Matumizi',
    loadingWallet: 'Inapakia mkoba...',
    myWallet: 'Mkoba Wangu',
    noTransactionsYet: 'Hakuna miamala bado',
    startUsingYourJiranipayWallet: 'Anza kutumia mkoba wako wa JiraniPay kuona historia ya miamala hapa',

    // Wallet actions and descriptions (Swahili)
    topUpWalletAction: 'Jaza Mkoba',
    addMoneyToYourJiranipayWallet: 'Ongeza pesa kwenye mkoba wako wa JiraniPay kupitia:\n\n• Pesa za Simu (MTN, Airtel, UTL)\n• Uhamisho wa Benki (Bure)\n• Maeneo ya Wakala\n• Kadi za Malipo\n\nChagua njia unayopendelea:',
    mobileMoneyTopup: 'Kujaza kwa Pesa za Simu',
    toTopUpViaMobileMoney: 'Kujaza kupitia Pesa za Simu:\n\n1. Piga *165*3# (MTN) au *185*9# (Airtel)\n2. Chagua "Lipa Bill"\n3. Ingiza Msimbo wa Mchuuzi: 123456\n4. Ingiza nambari yako ya simu\n5. Ingiza kiasi na thibitisha\n\nPesa zitaonekana mara moja!',
    bankTransferDetails: 'Maelezo ya Uhamisho wa Benki',
    transferToStanbicBank: 'Hamisha kwenda:\n\nBenki: Stanbic Bank Uganda\nAkaunti: JiraniPay Ltd\nNambari ya Akaunti: *************\nRejea: Nambari yako ya simu\n\nPesa zinaonekana ndani ya dakika 30 wakati wa masaa ya benki.',
    sendMoney: 'Tuma Pesa',
    sendMoneyToContacts: 'Tuma pesa kwa anwani:\n\n• Uhamisho wa nambari ya simu\n• Ujumuishaji wa kitabu cha anwani\n• Malipo ya msimbo wa QR\n• Uhamisho wa papo hapo\n• Uthibitisho salama\n\nInaelekea kwenye Kutuma Pesa...',
    transactionHistory: 'Historia ya Miamala',
    savings: 'Akiba',
    accessYourSavingsAccountsAndGoals: 'Fikia akaunti zako za akiba na malengo',
    walletSettings: 'Mipangilio ya Mkoba',
    manageYourWallet: 'Simamia mkoba wako:\n\n• Weka mipaka ya matumizi\n• Vipengele vya kujihifadhia\n• Arifa za miamala\n• Mipangilio ya usalama\n\nMipangilio ya mkoba inakuja hivi karibuni!',
    analytics: 'Uchambuzi',
    viewYourSpendingInsights: 'Ona maarifa yako ya matumizi:\n\n• Mgawanyiko wa kategoria\n• Mielekeo ya matumizi\n• Mapendekezo ya bajeti\n• Maarifa ya kifedha\n\nKipengele cha uchambuzi kinakuja hivi karibuni!',
    receiveViaQr: 'Pokea kupitia QR',
    generateQrCodesToReceivePayments: 'Tengeneza misimbo ya QR kupokea malipo:\n\n• Misimbo ya malipo ya kibinafsi\n• Chaguo za kiasi kilichowekwa au wazi\n• Shiriki kupitia programu za ujumbe\n• Hifadhi kwenye galari\n• Uchakataji wa malipo wa papo hapo\n\nInaelekea kwenye kizalishaji cha QR...',

    // Additional button and option texts (Swahili)
    mobileMoneyOption: 'Pesa za Simu',
    bankTransferOption: 'Uhamisho wa Benki',

    // Financial features
    savings: 'Akiba'
  },

  // Authentication & Onboarding
  auth: {
    welcomeBack: 'Karibu Tena',
    goodMorning: 'Habari za Asubuhi',
    goodAfternoon: 'Habari za Mchana',
    goodEvening: 'Habari za Jioni',
    chooseLoginMethod: 'Chagua njia ya kuingia',
    otpLogin: 'Kuingia kwa OTP',
    passwordLogin: 'Kuingia kwa Nenosiri',
    enterPhoneNumber: 'Weka nambari ya simu',
    enterPassword: 'Weka nenosiri lako',
    forgotPassword: 'Umesahau Nenosiri?',
    sendOTP: 'Tuma OTP',
    login: 'Ingia',
    verifyOTP: 'Thibitisha OTP',
    resendOTP: 'Tuma OTP Tena',
    resendOTPIn: 'Tuma OTP tena baada ya',
    dontHaveAccount: 'Huna akaunti?',
    signUp: 'Jisajili',
    useBiometric: 'Tumia Alama za Kibiolojia',
    
    // Registration
    createAccount: 'Tengeneza Akaunti',
    getStarted: 'Weka maelezo yako ili kuanza',
    fullName: 'Jina Kamili',
    createPassword: 'Tengeneza Nenosiri',
    confirmPassword: 'Thibitisha Nenosiri',
    passwordRequirements: 'Mahitaji ya Nenosiri:',
    atLeast8Chars: '• Angalau herufi 8',
    containsNumber: '• Ina angalau nambari moja',
    containsSpecialChar: '• Ina angalau alama maalum moja',
    passwordsMatch: '• Nenosiri zinalingana',
    alreadyHaveAccount: 'Tayari una akaunti?',
    signIn: 'Ingia',
    
    // Forgot Password
    resetPassword: 'Weka Nenosiri Jipya',
    enterPhoneToReset: 'Weka nambari ya simu ili kubadilisha nenosiri',
    createNewPassword: 'Tengeneza Nenosiri Jipya',
    newPassword: 'Nenosiri Jipya',
    confirmNewPassword: 'Thibitisha Nenosiri Jipya',
    
    // Country & Network
    selectCountry: 'Chagua Nchi',
    network: 'Mtandao',
    
    // Validation Messages
    invalidPhone: 'Tafadhali weka nambari sahihi ya simu',
    invalidCredentials: 'Maelezo si sahihi',
    passwordTooShort: 'Nenosiri lazima liwe na angalau herufi 8',
    passwordMismatch: 'Nenosiri hazilingani',
    nameRequired: 'Tafadhali weka jina lako kamili',
    otpSent: 'OTP imetumwa kwenye simu yako',
    otpInvalid: 'OTP si sahihi',
    loginSuccessful: 'Umeingia kikamilifu',
    accountCreated: 'Akaunti yako imetengenezwa kikamilifu',
    passwordReset: 'Nenosiri lako limebadilishwa kikamilifu',

    // Additional auth messages from LoginScreen
    failedToSendOTP: 'Imeshindwa kutuma OTP',
    useOTPLogin: 'Tumia Kuingia kwa OTP',
    failedToInitiatePasswordSetup: 'Imeshindwa kuanzisha mipangilio ya nywila. Tafadhali jaribu tena.',
    loginFailed: 'Kuingia Kumeshindwa',
    tryAgain: 'Jaribu Tena',
    useOTPInstead: 'Tumia OTP Badala Yake',
    pleaseEnterCompleteOTP: 'Tafadhali ingiza OTP kamili',
    failedToResendOTP: 'Imeshindwa kutuma OTP tena'
  },

  // Time-based greetings with names
  greetings: {
    goodMorningName: 'Habari za Asubuhi, {name}',
    goodAfternoonName: 'Habari za Mchana, {name}',
    goodEveningName: 'Habari za Jioni, {name}',
    goodMorning: 'Habari za Asubuhi',
    goodAfternoon: 'Habari za Mchana',
    goodEvening: 'Habari za Jioni'
  },

  // Profile
  profile: {
    title: 'Wasifu',
    verifiedAccount: 'Akaunti Iliyothibitishwa',
    pendingVerification: 'Inasubiri Uthibitisho',
    customerService: 'Wasiliana na timu yetu ya huduma kwa wateja',
    editProfile: 'Hariri Wasifu',
    accountSettings: 'Mipangilio ya Akaunti',
    securitySettings: 'Mipangilio ya Usalama',
    privacySettings: 'Mipangilio ya Faragha',
    notificationSettings: 'Mipangilio ya Arifa',
    helpSupport: 'Msaada na Usaidizi',
    aboutApp: 'Kuhusu Programu',
    signOut: 'Toka',

    // Profile Management
    accountManagement: 'Usimamizi wa Akaunti',
    updatePersonalInfo: 'Sasisha maelezo yako ya kibinafsi',
    accountVerification: 'Uthibitisho wa Akaunti',
    verifyIdentity: 'Thibitisha utambulisho wako',
    securityPrivacy: 'Usalama na Faragha',
    manageSecuritySettings: 'Simamia mipangilio ya usalama',
    preferences: 'Mapendeleo',
    customizeAppExperience: 'Badilisha uzoefu wa programu',
    supportHelp: 'Msaada na Uongozi',
    getHelpSupport: 'Pata msaada na uongozi',

    // Edit Profile
    personalInformation: 'Maelezo ya Kibinafsi',
    fullName: 'Jina Kamili',
    emailAddress: 'Anwani ya Barua Pepe',
    phoneNumber: 'Nambari ya Simu',
    dateOfBirth: 'Tarehe ya Kuzaliwa',
    country: 'Nchi',
    preferredLanguage: 'Lugha Unayopendelea',
    save: 'Hifadhi',
    cancel: 'Ghairi',
    saving: 'Inahifadhi...',

    // Profile Validation
    nameRequired: 'Jina linahitajika',
    validEmailRequired: 'Barua pepe sahihi inahitajika',
    validPhoneRequired: 'Nambari ya simu sahihi inahitajika',
    profileUpdated: 'Wasifu umesasishwa',
    profileUpdateFailed: 'Kusasisha wasifu kumeshindwa',

    // Profile Screen Specific
    loadingProfile: 'Inapakia wasifu...',
    accountLevel: 'Kiwango cha Akaunti',
    walletBalance: 'Salio la Mkoba',
    transactions: 'Miamala',

    // Profile Actions
    updateProfilePicture: 'Sasisha Picha ya Wasifu',
    chooseUpdateMethod: 'Chagua jinsi unavyotaka kusasisha picha yako ya wasifu',
    takePhoto: 'Piga Picha',
    chooseFromGallery: 'Chagua kutoka Galari',

    // Account Management Section
    accountVerification: 'Uthibitisho wa Akaunti',
    accountVerificationDesc: 'Thibitisha utambulisho wako kwa mipaka ya juu',

    // Security & Privacy Section
    securityAndPrivacy: 'Usalama na Faragha',
    securitySettings: 'Mipangilio ya Usalama',
    securitySettingsDesc: 'PIN, uthibitisho wa kibayolojia, na chaguo za usalama',
    privacyAndData: 'Faragha na Data',
    privacyDataDesc: 'Dhibiti data yako na mipangilio ya faragha',

    // App Preferences Section
    appPreferences: 'Mapendeleo ya Programu',
    darkMode: 'Hali ya Giza',
    darkModeDesc: 'Badili kwenda mandhari ya giza',
    notifications: 'Arifa',
    notificationsDesc: 'Simamia mapendeleo ya arifa',
    currency: 'Sarafu',
    currencyDesc: 'Chagua sarafu unayopendelea',

    // Help & Support Section
    helpAndSupport: 'Msaada na Uongozi',
    faq: 'Maswali Yanayoulizwa Mara kwa Mara',
    faqDesc: 'Pata majibu ya maswali ya kawaida',
    aiAssistant: 'Msaidizi wa AI',
    aiAssistantDesc: 'Pata msaada wa papo hapo na ushauri wa kifedha',
    contactUs: 'Wasiliana Nasi',
    contactUsDesc: 'Wasiliana na timu yetu ya huduma kwa wateja',

    // Logout
    logout: 'Toka',
    logoutConfirm: 'Una uhakika unataka kutoka?',

    // Error messages
    errorTakingPhoto: 'Imeshindwa kupiga picha',
    errorPickingImage: 'Imeshindwa kuchagua picha',
    errorUpdatingNotifications: 'Imeshindwa kusasisha mipangilio ya arifa. Pendeleo lako limehifadhiwa.',
    errorChangingLanguage: 'Imeshindwa kubadilisha lugha. Tafadhali jaribu tena.',
    errorChangingCurrency: 'Imeshindwa kubadilisha sarafu. Tafadhali jaribu tena.',
    errorLogout: 'Imeshindwa kutoka. Tafadhali jaribu tena.',

    // Notifications
    notificationsSaved: 'Pendeleo la arifa limehifadhiwa. Kwa msaada kamili wa arifa, tumia ujenzi wa maendeleo badala ya Expo Go.',

    // FAQ Alert
    faqAlert: 'Sehemu ya kina ya FAQ na miongozo ya:\n\n• Jinsi ya kutuma pesa\n• Mchakato wa malipo ya bili\n• Njia za kujaza mkoba\n• Matumizi ya msimbo wa QR\n• Uthibitisho wa akaunti\n• Mazoea bora ya usalama\n\nSehemu kamili ya FAQ inakuja hivi karibuni!',

    // Contact Support Alert
    contactSupportAlert: 'Barua pepe: <EMAIL>\nSimu: +256703089916\n\nTimu yetu ya huduma kwa wateja inapatikana masaa 24/7 kukusaidia na maswali au masuala yoyote.',

    // Profile Error
    profileError: 'Haiwezi kufungua wasifu kwa sasa. Tafadhali jaribu tena.',

    // Edit Profile Screen
    editProfile: 'Hariri Wasifu',
    personalInformation: 'Taarifa za Kibinafsi',
    fullName: 'Jina Kamili',
    emailAddress: 'Anwani ya Barua Pepe',
    phoneNumber: 'Nambari ya Simu',
    dateOfBirth: 'Tarehe ya Kuzaliwa',
    country: 'Nchi',
    preferredLanguage: 'Lugha Unayopendelea',
    save: 'Hifadhi',
    cancel: 'Ghairi',
    saving: 'Inahifadhi...',

    // Edit Profile Validation Messages
    fullNameRequired: 'Jina kamili linahitajika',
    fullNameMinLength: 'Jina kamili lazima liwe na angalau herufi 2',
    validEmailRequired: 'Tafadhali ingiza anwani sahihi ya barua pepe',
    phoneNumberRequired: 'Nambari ya simu inahitajika',
    validUgandaPhoneRequired: 'Tafadhali ingiza nambari sahihi ya simu ya Uganda',
    validDateOfBirthRequired: 'Tafadhali ingiza tarehe sahihi ya kuzaliwa',
    userNotAuthenticated: 'Mtumiaji hajathibitishwa',
    profileUpdateFailed: 'Imeshindwa kusasisha wasifu',
    profileUpdateFailedDatabase: 'Kusasisha wasifu kumeshindwa kwa sababu ya tatizo la hifadhidata. Tafadhali jaribu tena.',
    networkError: 'Hitilafu ya mtandao. Tafadhali angalia muunganisho wako na ujaribu tena.',
    validationError: 'Tafadhali angalia kuwa sehemu zote zinazohitajika zimejazwa kwa usahihi.',
    profileUpdateFailedTitle: 'Kusasisha Wasifu Kumeshindwa',
    failedToUpdateProfile: 'Imeshindwa kusasisha wasifu. Tafadhali jaribu tena.',
    yourProfileHasBeenUpdatedSuccessfully: 'Wasifu wako umesasishwa kwa mafanikio',
    unsavedChanges: 'Mabadiliko Yasiyohifadhiwa',
    youHaveUnsavedChangesAreYouSureYouWantToGoBack: 'Una mabadiliko yasiyohifadhiwa. Una uhakika unataka kurudi nyuma?',
    pleaseCorrectTheErrorsAndTryAgain: 'Tafadhali sahihisha makosa na ujaribu tena',

    // Edit Profile Placeholders
    enterYourFullName: 'Ingiza jina lako kamili',
    enterYourEmailAddress: 'Ingiza anwani yako ya barua pepe',
    enterYourPhoneNumber: 'Ingiza nambari yako ya simu',
    selectYourDateOfBirth: 'Chagua tarehe yako ya kuzaliwa',
    discard: 'Ondoa',

    // Account Verification Screen
    accountVerification: 'Uthibitisho wa Akaunti',
    verificationSteps: 'Hatua za Uthibitisho',
    viewStatus: 'Ona Hali',
    viewLimits: 'Ona Mipaka',
    emailVerification: 'Uthibitisho wa Barua Pepe',
    verifyEmailAddress: 'Thibitisha anwani yako ya barua pepe',
    identityVerification: 'Uthibitisho wa Utambulisho',
    uploadGovernmentId: 'Pakia kitambulisho cha serikali',
    addressVerification: 'Uthibitisho wa Anwani',
    confirmResidentialAddress: 'Thibitisha anwani yako ya makazi',

    // Security Settings
    biometricAuthentication: 'Uthibitisho wa Kibayolojia',
    enableBiometric: 'Wezesha uthibitisho wa kidole/uso',
    changePassword: 'Badilisha Nywila',
    updateAccountPassword: 'Sasisha nywila ya akaunti',
    twoFactorAuth: 'Uthibitisho wa Hatua Mbili',
    addExtraLayerSecurity: 'Ongeza safu ya ziada ya usalama',
    sessionTimeout: 'Muda wa Kikao',
    trustedDevices: 'Vifaa Vinavyoaminika',
    manageDeviceAccess: 'Simamia ufikiaji wa vifaa',
    securityActivity: 'Shughuli za Usalama',
    viewRecentSecurityEvents: 'Ona matukio ya hivi karibuni ya usalama',
    securityTips: 'Vidokezo vya Usalama',
    learnAccountSecurity: 'Jifunze jinsi ya kulinda akaunti yako',

    // PIN Management
    setUpPin: 'Weka PIN',
    changePin: 'Badilisha PIN',
    createSixDigitPin: 'Unda PIN ya tarakimu 6 kwa ufikiaji salama',
    currentPin: 'PIN ya Sasa',
    newPin: 'PIN Mpya',
    confirmPin: 'Thibitisha PIN',
    pinSetUpSuccessfully: 'PIN imewekwa kwa mafanikio',
    pinChangedSuccessfully: 'PIN imebadilishwa kwa mafanikio',

    // Session Timeout
    sessionTimeoutUpdated: 'Muda wa Kikao Umesasishwa',
    sessionWillTimeoutAfterMinutes: 'Kikao chako kitaisha baada ya dakika {minutes} za kutofanya kitu.',
    failedToUpdateSessionTimeout: 'Imeshindwa kusasisha muda wa kikao',

    // Privacy Controls
    dataPrivacy: 'Faragha ya Data',
    manageDataSharing: 'Simamia kushiriki data',
    marketingCommunications: 'Mawasiliano ya Uuzaji',
    receivePromotionalEmails: 'Pokea barua pepe za matangazo',
    analyticsTracking: 'Ufuatiliaji wa Uchambuzi',
    helpImproveServices: 'Saidia kuboresha huduma',
    thirdPartySharing: 'Kushiriki na Wahusika wa Tatu',
    shareDataPartners: 'Shiriki data na washirika',

    // Data Consent
    dataConsent: 'Idhini ya Data',
    chooseWhatDataYouAreComfortableSharing: 'Chagua data unayojisikia vizuri kushiriki nasi',
    essentialServices: 'Huduma Muhimu',
    requiredForCoreAppFunctionality: 'Inahitajika kwa utendaji wa msingi wa programu na usalama',
    analyticsAndPerformance: 'Uchambuzi na Utendaji',
    helpUsImproveTheAppBySharing: 'Tusaidie kuboresha programu kwa kushiriki data ya matumizi',
    marketingCommunications: 'Mawasiliano ya Uuzaji',
    receivePersonalizedOffersAndTips: 'Pokea matoleo ya kibinafsi na vidokezo vya kifedha',
    dataSharing: 'Kushiriki Data',
    shareAnonymizedDataWithPartners: 'Shiriki data isiyojulikana na washirika wanaoaminika',

    // Data Rights
    yourDataRights: 'Haki Zako za Data',
    exportMyData: 'Hamisha Data Yangu',
    downloadACopyOfAllYourData: 'Pakua nakala ya data yako yote',
    privacyPolicy: 'Sera ya Faragha',
    readOurComprehensivePrivacyPolicy: 'Soma sera yetu kamili ya faragha',
    dataProtection: 'Ulinzi wa Data',
    learnAboutYourDataRights: 'Jifunze kuhusu haki zako za data na ulinzi',

    // Danger Zone
    dangerZone: 'Eneo la Hatari',
    deleteAccount: 'Futa Akaunti',
    permanentlyDeleteYourAccount: 'Futa kabisa akaunti yako na data yote',

    // Export Data
    exportData: 'Hamisha Data',
    exportComplete: 'Uhamisho Umekamilika',
    yourDataHasBeenExported: 'Data yako imehamiswa kwa mafanikio. Ukubwa wa faili: {size} KB',
    share: 'Shiriki',
    exportFailed: 'Uhamisho Umeshindwa',
    failedToExportData: 'Imeshindwa kuhamisha data',
    exporting: 'Inahamisha...',
    export: 'Hamisha',

    // Account Deletion
    areYouSureYouWantToDelete: 'Una uhakika unataka kufuta akaunti yako? Kitendo hiki hakiwezi kutenduliwa na data yako yote itafutwa kabisa.',
    accountDeletionRequested: 'Ombi la Kufuta Akaunti Limetumwa',
    failedToRequestAccountDeletion: 'Imeshindwa kuomba kufuta akaunti',

    // Notification Settings
    pushNotifications: 'Arifa za Kusukuma',
    transactionAlerts: 'Arifa za Miamala',
    securityAlerts: 'Arifa za Usalama',
    marketingNotifications: 'Arifa za Uuzaji',
    systemUpdates: 'Masasisho ya Mfumo',

    // Help & Support
    frequentlyAskedQuestions: 'Maswali Yanayoulizwa Mara kwa Mara',
    contactSupport: 'Wasiliana na Msaada',
    reportIssue: 'Ripoti Tatizo',
    feedbackSuggestions: 'Maoni na Mapendekezo',
    userGuide: 'Mwongozo wa Mtumiaji',
    termsConditions: 'Masharti na Hali',




    // FAQ Screen
    moneyTransfer: 'Uhamisho wa Pesa',
    howDoISendMoneyToAnotherUser: 'Ninawezaje kutuma pesa kwa mtumiaji mwingine wa JiraniPay?',
    toSendMoneyGoToSendMoney: 'Kutuma pesa: 1) Nenda kwenye sehemu ya Kutuma Pesa, 2) Ingiza nambari ya simu ya mpokeaji au changanua msimbo wake wa QR, 3) Ingiza kiasi, 4) Ongeza ujumbe (si lazima), 5) Thibitisha kwa PIN yako. Pesa zitahamiswa papo hapo.',
    whatAreTheTransferLimits: 'Mipaka ya uhamisho ni ipi?',
    dailyLimitsDependOnVerification: 'Mipaka ya kila siku inategemea kiwango chako cha uthibitisho: Msingi (UGX 500,000), Imethibitishwa (UGX 2,000,000), Bora (UGX 10,000,000). Unaweza kuongeza mipaka kwa kukamilisha uthibitisho wa akaunti.',
    stillNeedHelp: 'Bado unahitaji msaada?',
    ourCustomerSupportTeamIsAvailable: 'Timu yetu ya msaada kwa wateja inapatikana masaa 24/7 kukusaidia.',

    // Contact Support Screen
    liveAIChat: 'Mazungumzo ya Moja kwa Moja ya AI',
    instantAIAssistant: 'Msaidizi wa AI wa Papo Hapo',
    getImmediateHelpFromOurAI: 'Pata msaada wa papo hapo kutoka kwa msaidizi wetu wa AI',
    aiAssistant: 'Msaidizi wa AI',
    callSupport: 'Piga Simu Msaada',
    customerService247: 'Huduma kwa Wateja 24/7',
    speakDirectlyWithOurSupport: 'Zungumza moja kwa moja na timu yetu ya msaada',
    whatsappSupport: 'Msaada wa WhatsApp',
    chatOnWhatsapp: 'Zungumza kwenye WhatsApp',
    messageUsOnWhatsappForQuickHelp: 'Tutumie ujumbe kwenye WhatsApp kwa msaada wa haraka',
    emailSupport: 'Msaada wa Barua Pepe',
    generalInquiries: 'Maswali ya Jumla',
    sendUsYourQuestionsAndConcerns: 'Tutumie maswali na wasiwasi wako',
    securityHotline: 'Simu ya Dharura ya Usalama',
    emergencySecurityIssues: 'Masuala ya Dharura ya Usalama',
    reportSecurityIncidentsImmediately: 'Ripoti matukio ya usalama papo hapo',
    technicalSupport: 'Msaada wa Kiufundi',
    appAndTechnicalIssues: 'Masuala ya Programu na Kiufundi',
    getHelpWithAppFunctionality: 'Pata msaada na utendaji wa programu',
    immediate: 'Papo Hapo',
    instant: 'Mara Moja',
    minutes515: 'Dakika 5-15',
    hours24: 'Masaa 2-4',
    hours12: 'Saa 1-2',
    monFri8am8pm: 'Jumatatu-Ijumaa 8AM-8PM',
    monFri8am6pm: 'Jumatatu-Ijumaa 8AM-6PM',

    // AI Chat Screen
    typeYourMessage: 'Andika ujumbe wako...',
    clearChatHistory: 'Futa Historia ya Mazungumzo',
    areYouSureYouWantToClearAllChat: 'Una uhakika unataka kufuta ujumbe wote wa mazungumzo? Kitendo hiki hakiwezi kutenduliwa.',
    clear: 'Futa',
    failedToSendMessage: 'Imeshindwa kutuma ujumbe. Tafadhali jaribu tena.',
    failedToSendMessageShort: 'Imeshindwa kutuma ujumbe',
    wallet: 'Mkoba',
    balanceAndTransactions: 'Salio na miamala',
    checkMyBalance: '💰 Angalia salio langu',
    sendMoney: 'Tuma Pesa',
    transferToAnyone: 'Hamisha kwa mtu yeyote',
    sendMoneyEmoji: '📤 Tuma pesa',
    payBills: 'Lipa Bili',
    umemeWaterAirtime: 'UMEME, Maji, Airtime',
    security: 'Usalama',
    accountProtection: 'Ulinzi wa akaunti',
    qrScanner: 'Mchunguzi wa QR',
    quickPayments: 'Malipo ya haraka',
    qrScannerEmoji: '📱 Mchunguzi wa QR',

    // Account Actions
    deleteAccount: 'Futa Akaunti',
    exportData: 'Hamisha Data',
    clearCache: 'Futa Hifadhi Muda',
    rateApp: 'Kadiria Programu',
    shareApp: 'Shiriki Programu'
  },

  // Dashboard & Home
  dashboard: {
    title: 'Dashibodi',
    welcome: 'Karibu',
    balance: 'Salio',
    totalBalance: 'Jumla ya Salio',
    availableBalance: 'Salio Linalotumika',
    quickActions: 'Vitendo vya Haraka',
    recentTransactions: 'Miamala ya Hivi Karibuni',
    viewAll: 'Ona Yote',
    noTransactions: 'Hakuna miamala bado',
    transactionsWillAppear: 'Miamala yako ya hivi karibuni yataonekana hapa',
    sendMoney: 'Tuma Pesa',
    payBills: 'Lipa Bili',
    topUp: 'Jaza',
    scanQR: 'Changanua QR',
    savings: 'Akiba',
    analytics: 'Uchambuzi',
    topCategory: 'Jamii ya Juu',
    startUsing: 'Anza kutumia JiraniPay kuona maarifa',
    makeTransactions: 'Fanya miamala ili kufungua maarifa ya kifedha ya AI',
    financialInsights: 'Maarifa ya Kifedha',
    quickActionsTitle: 'Vitendo vya Haraka',
    notifications: 'Arifa',
    notificationsError: 'Haiwezi kufungua arifa kwa sasa. Tafadhali jaribu tena.',
    promotions: {
      schoolFees: 'LIPA ADA ZA\nSHULE NA USHINDE',
      doubleData: 'DATA MARA\nMBILI WIKENDI',
      doubleDataSubtitle: 'Pata data mara 2 kwenye bundles zote',
      cashback: '5% CASHBACK\nKWENYE BILI',
      cashbackSubtitle: 'Lipa huduma na upate rudi'
    },
    quickActions: {
      buyBundles: 'Nunua Bundles',
      sendMoney: 'Tuma Pesa',
      payBills: 'Lipa Bili',
      budgetInsights: 'Maarifa ya Bajeti',
      savings: 'Akiba',
      requestMoney: 'Omba Pesa',
      scanPay: 'Changanua na Lipa',
      buyAirtime: 'Nunua Muda wa Simu'
    },

    // Alert messages for dashboard features
    alerts: {
      errorLoadingData: 'Imeshindwa kupakia data ya dashibodi. Tafadhali jaribu tena.',
      transactionHistory: 'Historia ya Miamala',
      transactionHistoryDesc: 'Ona miamala yako yote na:\n\n• Rekodi za kina za miamala\n• Chuja kwa tarehe na aina\n• Hamisha taarifa\n• Utendaji wa kutafuta\n\nHistoria kamili inakuja hivi karibuni!',

      financialInsights: 'Maarifa ya Kifedha',
      financialInsightsDesc: 'Ona uchambuzi wa kina:\n\n• Mgawanyo wa matumizi kwa jamii\n• Mielekeo ya kila mwezi na mwaka\n• Mapendekezo ya bajeti\n• Maarifa ya akiba\n• Alama ya afya ya kifedha\n\nInaelekea kwenye Uchambuzi...',

      billPayments: 'Malipo ya Bili',
      billPaymentsDesc: 'Fikia huduma zote za malipo ya bili:\n\n• Umeme (UMEME)\n• Maji (NWSC)\n• Mtandao na TV\n• Airtime na Data ya Simu\n• Huduma za Serikali\n• Malipo ya Bima\n\nHuduma kamili za malipo ya bili zinakuja hivi karibuni!',

      sendMoney: 'Tuma Pesa',
      sendMoneyDesc: 'Tuma pesa kwa marafiki:\n\n• Uhamisho wa nambari ya simu\n• Muunganisho wa kitabu cha anwani\n• Malipo ya msimbo wa QR\n• Uhamisho wa papo hapo\n• Uthibitishaji salama\n\nKipengele cha kutuma pesa kinakuja hivi karibuni!',

      qrPay: 'Malipo ya QR',
      qrPayDesc: 'Inafungua mchunguzi wa QR...',

      payBills: 'Lipa Bili',
      payBillsDesc: 'Lipa bili zako kwa urahisi:\n\n• Umeme (UMEME)\n• Maji (NWSC)\n• Mtandao na TV\n• Airtime ya simu\n• Huduma za serikali\n\nKipengele cha malipo ya bili kinapatikana!',

      savings: 'Akiba',
      savingsDesc: 'Okoa pesa na fikia malengo yako:\n\n• Unda malengo ya akiba\n• Uhamisho wa kiotomatiki\n• Fuatilia maendeleo\n• Pata riba\n\nKipengele cha akiba kinapatikana!',

      buyDataBundles: 'Nunua Vifurushi vya Data',
      buyDataBundlesDesc: 'Nunua vifurushi vya data kwa mitandao ya MTN, Airtel, na UTL',

      buyAirtime: 'Nunua Airtime',
      buyAirtimeDesc: 'Nunua airtime kwa mitandao ya MTN, Airtel, na UTL',

      requestMoney: 'Omba Pesa',
      requestMoneyDesc: 'Omba pesa kutoka kwa marafiki:\n\n• Tuma maombi ya malipo\n• Muunganisho wa kitabu cha anwani\n• Maombi ya msimbo wa QR\n• Arifa za papo hapo\n• Uthibitishaji salama\n\nKipengele cha kuomba pesa kinakuja hivi karibuni!',

      budgetInsights: 'Maarifa ya Bajeti',
      budgetInsightsDesc: 'Maarifa ya kifedha yanayotumia AI:\n\n• Uchambuzi wa matumizi\n• Mapendekezo ya bajeti\n• Kuboresha akiba\n• Alama ya afya ya kifedha\n\nMaarifa ya bajeti yanapatikana!',

      transactionHistoryAvailable: 'Ona miamala yako yote na:\n\n• Rekodi za kina za miamala\n• Chuja kwa tarehe na aina\n• Hamisha taarifa\n• Utendaji wa kutafuta\n\nHistoria ya miamala inapatikana!',

      // Top up wallet
      topUpWallet: 'Jaza Mkoba',
      topUpWalletDesc: 'Ongeza pesa kwenye mkoba wako wa JiraniPay kupitia:\n\n• Pesa za Simu (MTN, Airtel, UTL)\n• Uhamisho wa Benki (Bure)\n• Maeneo ya Wakala\n• Kadi za Malipo\n\nChagua njia unayopendelea:',

      mobileMoneyTopUp: 'Kujaza kwa Pesa za Simu',
      mobileMoneyTopUpDesc: 'Kujaza kupitia Pesa za Simu:\n\n1. Piga *165*3# (MTN) au *185*9# (Airtel)\n2. Chagua "Lipa Bili"\n3. Ingiza Msimbo wa Mfanyabiashara: 123456\n4. Ingiza nambari yako ya simu\n5. Ingiza kiasi na thibitisha\n\nPesa zitaonekana mara moja!',

      bankTransfer: 'Uhamisho wa Benki',
      bankTransferDesc: 'Hamisha kwenda:\n\nBenki: Stanbic Bank Uganda\nAkaunti: JiraniPay Ltd\nNambari ya Akaunti: *************\nRejea: Nambari yako ya simu\n\nPesa zinaonekana ndani ya dakika 30 wakati wa masaa ya benki.',

      mobileMoneyOption: 'Pesa za Simu',
      bankTransferOption: 'Uhamisho wa Benki'
    }
  },

  // Wallet & Transactions
  wallet: {
    title: 'Mkoba',
    myWallet: 'Mkoba Wangu',
    balance: 'Salio',
    availableBalance: 'Salio Linalotumika',
    walletBalance: 'Salio la Mkoba',
    transactions: 'Miamala',
    transactionHistory: 'Historia ya Miamala',
    recentTransactions: 'Miamala ya Hivi Karibuni',
    sendMoney: 'Tuma Pesa',
    receiveMoney: 'Pokea Pesa',
    topUp: 'Jaza Mkoba',
    topUpWallet: 'Jaza Mkoba',
    withdraw: 'Toa',
    transfer: 'Hamisha',
    deposit: 'Weka',
    payment: 'Malipo',
    refund: 'Rudisha',
    fee: 'Ada',
    total: 'Jumla',
    amount: 'Kiasi',
    recipient: 'Mpokeaji',
    sender: 'Mtumaji',
    reference: 'Rejea',
    description: 'Maelezo',
    date: 'Tarehe',
    time: 'Muda',
    status: 'Hali',
    type: 'Aina',
    category: 'Jamii',
    provider: 'Mtoa Huduma',
    account: 'Akaunti',
    accountNumber: 'Nambari ya Akaunti',
    phoneNumber: 'Nambari ya Simu',
    transactionId: 'Kitambulisho cha Muamala',
    receiptNumber: 'Nambari ya Risiti',

    // Wallet Actions
    viewAll: 'Ona Yote',
    refresh: 'Onyesha Upya',
    settings: 'Mipangilio',
    qrPay: 'Lipa kwa QR',
    scanQR: 'Changanua QR',
    history: 'Historia',

    // Top Up
    selectTopUpMethod: 'Chagua Njia ya Kujaza',
    mobileMoneyTopUp: 'Jaza kwa Pesa za Simu',
    bankTransfer: 'Uhamisho wa Benki',
    enterAmount: 'Ingiza Kiasi',
    enterPhoneNumber: 'Ingiza Nambari ya Simu',
    confirmTopUp: 'Thibitisha Kujaza',
    topUpSuccessful: 'Kujaza Kumefanikiwa!',
    topUpFailed: 'Kujaza Kumeshindwa',

    // Send Money
    selectRecipient: 'Chagua Mpokeaji',
    enterRecipientDetails: 'Ingiza Maelezo ya Mpokeaji',
    recipientName: 'Jina la Mpokeaji',
    recipientPhone: 'Simu ya Mpokeaji',
    transferAmount: 'Kiasi cha Uhamisho',
    transferFee: 'Ada ya Uhamisho',
    totalCost: 'Gharama Jumla',
    confirmTransfer: 'Thibitisha Uhamisho',
    transferSuccessful: 'Uhamisho Umefanikiwa!',
    transferFailed: 'Uhamisho Umeshindwa',

    // Transaction Status
    pending: 'Inasubiri',
    completed: 'Imekamilika',
    failed: 'Imeshindwa',
    cancelled: 'Imeghairiwa',
    processing: 'Inachakatwa',

    // Spending Limits
    spendingLimits: 'Vikomo vya Matumizi',
    dailyLimit: 'Kikomo cha Kila Siku',
    monthlyLimit: 'Kikomo cha Kila Mwezi',
    spentToday: 'Umetumia Leo',
    spentThisMonth: 'Umetumia Mwezi Huu',
    remainingToday: 'Imebaki Leo',
    remainingThisMonth: 'Imebaki Mwezi Huu',

    // Error Messages
    insufficientBalance: 'Salio haitoshi',
    invalidAmount: 'Kiasi si sahihi',
    invalidPhoneNumber: 'Nambari ya simu si sahihi',
    networkError: 'Hitilafu ya mtandao',
    transactionFailed: 'Muamala umeshindwa',
    loadingProfile: 'Inapakia wasifu...',
    loadingWallet: 'Inapakia mkoba...',
    loadingTransactions: 'Inapakia miamala...',
    phoneNotAvailable: 'Simu haipatikani',
    user: 'Mtumiaji',
    creationFailed: 'Uundaji Umeshindwa',
    loginRequired: 'Ingia Inahitajika',
    notAvailable: 'Haipatikani',
    errorLoading: 'Hitilafu ya Kupakia',
    jiranipayWallet: 'Mkoba wa JiraniPay',
    mainAccount: 'Akaunti Kuu',
    send: 'Tuma',
    topUp: 'Jaza',
    qrPay: 'Lipa QR',
    confirmTransaction: 'Thibitisha Muamala',
    transactionSuccessful: 'Muamala Umefanikiwa',
    transactionFailed: 'Muamala Umeshindwa',
    insufficientFunds: 'Pesa hazitoshi',
    dailyLimitExceeded: 'Kikomo cha kila siku kimezidishwa',
    monthlyLimitExceeded: 'Kikomo cha kila mwezi kimezidishwa',
    invalidAmount: 'Kiasi si sahihi',
    minimumAmount: 'Kiasi cha chini',
    maximumAmount: 'Kiasi cha juu',
    transactionLimits: 'Vikomo vya Miamala',
    dailyLimit: 'Kikomo cha Kila Siku',
    monthlyLimit: 'Kikomo cha Kila Mwezi',
    remainingDaily: 'Kilichobaki cha Kila Siku',
    remainingMonthly: 'Kilichobaki cha Kila Mwezi'
  },

  // Currency & Formatting
  currency: {
    ugx: 'Shilingi ya Uganda',
    kes: 'Shilingi ya Kenya',
    tzs: 'Shilingi ya Tanzania',
    rwf: 'Faranga ya Rwanda',
    bif: 'Faranga ya Burundi',
    etb: 'Birr ya Ethiopia',
    usd: 'Dola ya Marekani',
    eur: 'Euro',
    gbp: 'Pauni ya Uingereza',
    selectCurrency: 'Chagua Sarafu',
    preferredCurrency: 'Sarafu Unayopendelea',
    currencySettings: 'Mipangilio ya Sarafu',
    exchangeRate: 'Kiwango cha Ubadilishaji',
    convertedAmount: 'Kiasi Kilichobadilishwa',
    conversionRate: 'Kiwango cha Ubadilishaji',
    lastUpdated: 'Ilisasishwa Mwisho',
    updateRates: 'Sasisha Viwango',
    rateUnavailable: 'Kiwango hakipatikani',
    conversionError: 'Hitilafu ya ubadilishaji',
    currencyUpdated: 'Sarafu imebadilishwa kuwa {currency}',
    updateError: 'Imeshindwa kubadilisha sarafu. Jaribu tena.'
  },

  // Navigation
  navigation: {
    home: 'Nyumbani',
    dashboard: 'Dashibodi',
    wallet: 'Mkoba',
    bills: 'Bili',
    sendMoney: 'Tuma Pesa',
    payBills: 'Lipa Bili',
    savings: 'Akiba',
    analytics: 'Uchambuzi',
    profile: 'Wasifu',
    settings: 'Mipangilio',
    help: 'Msaada na Uongozi',
    notifications: 'Arifa',
    security: 'Usalama',
    about: 'Kuhusu',
    logout: 'Toka',
    qrPay: 'Lipa QR'
  },

  // QR Code
  qr: {
    title: 'QR Lipa na Changanua',
    subtitle: 'Changanua msimbo wa QR kulipa au tengeneza msimbo wa QR kupokea malipo',
    scanQRCode: 'Changanua Msimbo wa QR',
    generateQR: 'Tengeneza QR',
    payAndScan: 'QR Lipa na Changanua'
  },

  // Buttons
  buttons: {
    login: 'Ingia',
    signup: 'Jisajili',
    continue: 'Endelea',
    cancel: 'Ghairi',
    save: 'Hifadhi',
    edit: 'Hariri',
    delete: 'Futa',
    confirm: 'Thibitisha',
    retry: 'Jaribu Tena',
    refresh: 'Onyesha Upya',
    share: 'Shiriki',
    copy: 'Nakili',
    send: 'Tuma',
    receive: 'Pokea',
    topUp: 'Jaza',
    withdraw: 'Toa',
    viewAll: 'Ona Yote',
    viewDetails: 'Ona Maelezo',
    close: 'Funga',
    back: 'Rudi',
    next: 'Ifuatayo',
    done: 'Imemaliza',
    apply: 'Tumia',
    reset: 'Weka Upya',
    update: 'Sasisha',
    enable: 'Washa',
    disable: 'Zima'
  },

  // Error Messages
  errors: {
    networkError: 'Hitilafu ya mtandao. Angalia muunganisho wako wa intaneti.',
    serverError: 'Hitilafu ya seva. Jaribu tena baadaye.',
    invalidInput: 'Ingizo batili. Angalia taarifa zako.',
    authenticationFailed: 'Uthibitishaji umeshindwa. Jaribu tena.',
    insufficientFunds: 'Pesa hazitoshi kwa muamala huu.',
    transactionFailed: 'Muamala umeshindwa. Jaribu tena.',
    limitExceeded: 'Kikomo cha muamala kimezidishwa.',
    invalidAmount: 'Kiasi batili kimeingizwa.',
    invalidPhoneNumber: 'Muundo wa nambari ya simu si sahihi.',
    invalidEmail: 'Muundo wa barua pepe si sahihi.',
    passwordTooWeak: 'Nenosiri ni dhaifu sana. Chagua nenosiri kali zaidi.',
    passwordMismatch: 'Nenosiri hazilingani.',
    accountNotFound: 'Akaunti haijapatikana.',
    accountLocked: 'Akaunti imefungwa kwa muda.',
    sessionExpired: 'Kipindi chako kimeisha. Ingia tena.',
    permissionDenied: 'Ruhusa imekataliwa.',
    fileUploadFailed: 'Kupakia faili kumeshindwa.',
    cameraPermissionDenied: 'Ruhusa ya kamera inahitajika.',
    locationPermissionDenied: 'Ruhusa ya mahali inahitajika.',
    biometricNotAvailable: 'Uthibitishaji wa kibayolojia haupatikani.',
    biometricFailed: 'Uthibitishaji wa kibayolojia umeshindwa.',
    qrCodeInvalid: 'Msimbo wa QR si sahihi.',
    currencyConversionFailed: 'Ubadilishaji wa sarafu umeshindwa.',
    rateNotAvailable: 'Kiwango cha ubadilishaji hakipatikani.',
    serviceUnavailable: 'Huduma haipatikani kwa sasa.',
    maintenanceMode: 'Programu iko chini ya matengenezo. Jaribu tena baadaye.',
    updateRequired: 'Sasisha programu inahitajika. Sasisha ili kuendelea.',
    deviceNotSupported: 'Kifaa hakitumiki.',
    featureNotAvailable: 'Kipengele hakipatikani katika eneo lako.',
    dailyLimitReached: 'Kikomo cha kila siku cha muamala kimefikiwa.',
    monthlyLimitReached: 'Kikomo cha kila mwezi cha muamala kimefikiwa.',
    minimumAmountNotMet: 'Kiwango cha chini cha muamala hakijafikiwa.',
    maximumAmountExceeded: 'Kiwango cha juu cha muamala kimezidishwa.',
    recipientNotFound: 'Mpokeaji hajapatikana.',
    invalidRecipient: 'Taarifa za mpokeaji si sahihi.',
    duplicateTransaction: 'Muamala wa nakala umegunduliwa.',
    transactionTimeout: 'Muda wa muamala umeisha.',
    invalidOTP: 'Msimbo wa OTP si sahihi.',
    otpExpired: 'Msimbo wa OTP umeisha.',
    tooManyAttempts: 'Majaribio mengi yameshindwa. Jaribu tena baadaye.',
    accountSuspended: 'Akaunti imesimamishwa.',
    kycRequired: 'Uthibitishaji wa KYC unahitajika.',
    documentUploadRequired: 'Kupakia hati kunahitajika.',
    verificationPending: 'Uthibitishaji unasubiri.',
    verificationFailed: 'Uthibitishaji umeshindwa.',
    invalidDocument: 'Muundo wa hati si sahihi.',
    documentExpired: 'Hati imeisha.',
    addressVerificationRequired: 'Uthibitishaji wa anwani unahitajika.',
    phoneVerificationRequired: 'Uthibitishaji wa nambari ya simu unahitajika.',
    emailVerificationRequired: 'Uthibitishaji wa barua pepe unahitajika.'
  },

  // Success Messages
  success: {
    loginSuccessful: 'Kuingia kumefanikiwa!',
    registrationSuccessful: 'Usajili umefanikiwa!',
    transactionSuccessful: 'Muamala umekamilika kwa ufanisi!',
    passwordUpdated: 'Nenosiri limesasishwa kwa ufanisi!',
    profileUpdated: 'Wasifu umesasishwa kwa ufanisi!',
    settingsSaved: 'Mipangilio imehifadhiwa kwa ufanisi!',
    languageUpdated: 'Lugha imesasishwa kwa ufanisi!',
    currencyUpdated: 'Sarafu imesasishwa kwa ufanisi!',
    notificationsSent: 'Arifa zimetumwa kwa ufanisi!',
    backupCreated: 'Nakala ya hifadhi imeundwa kwa ufanisi!',
    dataExported: 'Data imehamishwa kwa ufanisi!',
    accountVerified: 'Akaunti imethibitishwa kwa ufanisi!',
    documentUploaded: 'Hati imepakiwa kwa ufanisi!',
    paymentSuccessful: 'Malipo yamekamilika kwa ufanisi!',
    transferSuccessful: 'Uhamishaji umekamilika kwa ufanisi!',
    topUpSuccessful: 'Kujaza kumekamilika kwa ufanisi!',
    withdrawalSuccessful: 'Kutoa kumekamilika kwa ufanisi!',
    savingsGoalCreated: 'Lengo la akiba limeundwa kwa ufanisi!',
    savingsGoalUpdated: 'Lengo la akiba limesasishwa kwa ufanisi!',
    reminderSet: 'Ukumbusho umewekwa kwa ufanisi!',
    subscriptionUpdated: 'Uandikishaji umesasishwa kwa ufanisi!',
    feedbackSubmitted: 'Maoni yamewasilishwa kwa ufanisi!',
    supportTicketCreated: 'Tiketi ya msaada imeundwa kwa ufanisi!',
    passwordReset: 'Nenosiri limewekwa upya kwa ufanisi!',
    emailVerified: 'Barua pepe imethibitishwa kwa ufanisi!',
    phoneVerified: 'Nambari ya simu imethibitishwa kwa ufanisi!',
    biometricEnabled: 'Uthibitishaji wa kibayolojia umewashwa!',
    biometricDisabled: 'Uthibitishaji wa kibayolojia umezimwa!',
    securityUpdated: 'Mipangilio ya usalama imesasishwa kwa ufanisi!',
    privacyUpdated: 'Mipangilio ya faragha imesasishwa kwa ufanisi!',
    preferencesUpdated: 'Mapendeleo yameasishwa kwa ufanisi!',
    cacheCleared: 'Hifadhi muda imefutwa kwa ufanisi!',
    logoutSuccessful: 'Kutoka kumefanikiwa!'
  },

  // Settings
  settings: {
    title: 'Mipangilio',
    languageAndCurrency: 'Lugha na Sarafu',
    language: 'Lugha',
    languageDescription: 'Chagua lugha unayopendelea kwa programu',
    languageInfo: 'Programu itaanza upya ili kutumia mabadiliko ya lugha',
    languageUpdated: 'Lugha imesasishwa kikamilifu',
    languageUpdateError: 'Imeshindwa kusasisha lugha. Jaribu tena.',
    currencyDescription: 'Chagua sarafu unayopendelea kwa miamala',
    currencyInfo: 'Kiasi chote kitaonyeshwa kwa sarafu unayopendelea na ubadilishaji wa wakati halisi',
    security: 'Usalama',
    privacy: 'Faragha',
    notifications: 'Arifa',
    account: 'Akaunti',
    support: 'Msaada',
    about: 'Kuhusu',
    version: 'Toleo',
    buildNumber: 'Nambari ya Ujenzi',
    termsOfService: 'Masharti ya Huduma',
    privacyPolicy: 'Sera ya Faragha',
    contactUs: 'Wasiliana Nasi',
    rateApp: 'Kadiria Programu',
    shareApp: 'Shiriki Programu',
    clearCache: 'Futa Hifadhi Muda',
    exportData: 'Hamisha Data',
    deleteAccount: 'Futa Akaunti',
    signOut: 'Toka'
  },

  // Bills & Payments
  bills: {
    payBill: 'Lipa Bili',
    categories: 'MAKUNDI',
    viewAll: 'ONA YOTE',
    showLess: 'ONYESHA KIDOGO',
    utilities: 'Huduma za Umma',
    airtime: 'Muda wa Simu na Data',
    government: 'Serikali',
    education: 'Elimu',
    insurance: 'Bima',
    entertainment: 'Burudani',

    // Bill Categories
    electricity: 'Umeme',
    water: 'Maji',
    internet: 'Mtandao',
    mobile: 'Simu ya Mkononi',
    schoolFees: 'Ada za Shule',
    financial: 'Huduma za Kifedha',
    postpaid: 'Malipo ya Baadaye',
    solar: 'Nishati ya Jua',
    tv: 'Runinga',
    gaming: 'Michezo',
    governmentServices: 'Huduma za Serikali',
    payTV: 'Runinga ya Malipo',

    // Bill Subtitles
    powerBillsUtilities: 'Bili za umeme na huduma',
    waterBillsUtilities: 'Bili za maji na huduma',
    mobileTopUpData: 'Jaza simu na data',
    bankingFinance: 'Benki na fedha',
    monthlySubscriptions: 'Michango ya kila mwezi',
    solarEnergyPayments: 'Malipo ya nishati ya jua',
    tvSubscriptions: 'Michango ya runinga',
    gamingEntertainment: 'Michezo na burudani',
    officialPayments: 'Malipo rasmi',
    televisionSubscriptions: 'Michango ya runinga',

    // Bill Details
    selectProvider: 'Chagua Mtoa Huduma',
    searchProviders: 'Tafuta watoa huduma...',
    accountNumber: 'Nambari ya Akaunti',
    customerName: 'Jina la Mteja',
    reference: 'Rejea',
    amount: 'Kiasi',
    enterAccountNumber: 'Ingiza nambari ya akaunti',
    enterCustomerName: 'Ingiza jina la mteja',
    enterReference: 'Ingiza rejea (si lazima)',
    enterAmount: 'Ingiza kiasi',

    // Bill Confirmation
    confirmPayment: 'Thibitisha Malipo',
    reviewPaymentDetails: 'Kagua maelezo ya malipo yako',
    paymentDetails: 'Maelezo ya Malipo',
    serviceFee: 'Ada ya Huduma',
    totalAmount: 'Jumla ya Kiasi',
    confirmAndPay: 'Thibitisha na Lipa',

    // Bill Success
    paymentSuccessful: 'Malipo Yamefanikiwa!',
    billPaidSuccessfully: 'Bili yako imelipwa kwa mafanikio',
    transactionId: 'Kitambulisho cha Muamala',
    receiptNumber: 'Nambari ya Risiti',
    downloadReceipt: 'Pakua Risiti',
    shareReceipt: 'Shiriki Risiti',
    payAnotherBill: 'Lipa Bili Nyingine',
    backToHome: 'Rudi Nyumbani',

    // Error Messages
    invalidAccountNumber: 'Nambari ya akaunti si sahihi',
    invalidAmount: 'Kiasi si sahihi',
    paymentFailed: 'Malipo yameshindwa',
    insufficientBalance: 'Salio haitoshi',
    networkError: 'Hitilafu ya mtandao',
    tryAgain: 'Jaribu tena'
  },

  // Help & Support
  support: {
    frequentlyAskedQuestions: 'Maswali Yanayoulizwa Mara kwa Mara',
    contactSupport: 'Wasiliana na Msaada',
    reportIssue: 'Ripoti Tatizo',
    feedbackSuggestions: 'Maoni na Mapendekezo',
    userGuide: 'Mwongozo wa Mtumiaji',
    termsConditions: 'Masharti na Hali',
    howCanWeHelpYou: 'Tunawezaje kukusaidia?',
    findAnswersToCommonQuestionsAboutUsingJiranipayIfY: 'Pata majibu ya maswali ya kawaida kuhusu kutumia JiraniPay. Ikiwa unahitaji msaada zaidi, timu yetu ya msaada iko tayari kukusaidia.',
    stillNeedHelp: 'Bado unahitaji msaada?',
    ourCustomerSupportTeamIsAvailable247ToAssistYou: 'Timu yetu ya msaada wa wateja inapatikana saa 24/7 kukusaidia na maswali au masuala yoyote.',

    // FAQ Categories
    moneyTransfer: 'Uhamisho wa Pesa',
    billPayments: 'Malipo ya Bili',
    walletManagement: 'Usimamizi wa Mkoba',
    qrCodeScanner: 'Msomaji wa Msimbo wa QR',
    accountSecurity: 'Usalama wa Akaunti',

    // FAQ Questions
    howDoISendMoney: 'Ninawezaje kutuma pesa kwa mtumiaji mwingine wa JiraniPay?',
    whatAreTransferLimits: 'Ni vikomo gani vya uhamisho?',
    howLongDoTransfersTake: 'Uhamisho unachukua muda gani?',
    whichBillsCanIPay: 'Ni bili gani ninazoweza kulipa kupitia JiraniPay?',
    howDoIPayUtilityBills: 'Ninawezaje kulipa bili za huduma?',
    canIScheduleRecurringPayments: 'Ninaweza kupanga malipo ya mara kwa mara?',
    howDoIAddMoney: 'Ninawezaje kuongeza pesa kwenye mkoba wangu?',
    isMyMoneySafe: 'Je, pesa zangu ni salama kwenye mkoba wa JiraniPay?',
    canIWithdrawMoney: 'Ninaweza kutoa pesa kutoka mkoba wangu?',
    howDoIUseQRScanner: 'Ninawezaje kutumia msomaji wa msimbo wa QR?',
    whatCanIDoWithQRCodes: 'Ninaweza kufanya nini na misimbo ya QR?',
    howDoIGenerateQRCode: 'Ninawezaje kutengeneza msimbo wangu wa QR?',
    howDoIVerifyAccount: 'Ninawezaje kuthibitisha akaunti yangu?',
    whatSecurityFeatures: 'Ni vipengele gani vya usalama vinavyotolewa na JiraniPay?',
    whatIfISuspectFraud: 'Nifanye nini ikiwa nashtukia udanganyifu?',

    // FAQ Answers
    sendMoneyAnswer: 'Kutuma pesa: 1) Nenda kwenye sehemu ya Kutuma Pesa, 2) Ingiza nambari ya simu ya mpokeaji au soma msimbo wake wa QR, 3) Ingiza kiasi, 4) Ongeza ujumbe (si lazima), 5) Thibitisha kwa PIN yako. Pesa zitahamishwa papo hapo.',
    transferLimitsAnswer: 'Vikomo vya kila siku vinategemea kiwango chako cha uthibitishaji: Msingi (UGX 500,000), Imethibitishwa (UGX 2,000,000), Bora (UGX 10,000,000). Unaweza kuongeza vikomo kwa kukamilisha uthibitishaji wa akaunti.',
    transferTimeAnswer: 'Uhamisho wa JiraniPay hadi JiraniPay ni wa papo hapo. Uhamisho wa benki unachukua siku 1-3 za kazi. Uhamisho wa pesa za simu ni wa papo hapo lakini unaweza kuchukua hadi dakika 30 wakati wa msongamano.',
    billPaymentsAnswer: 'Unaweza kulipa: Umeme (UMEME, KPLC, TANESCO), Bili za maji, Mtandao/Runinga ya Kebo, Ada za shule, Malipo ya bima, Huduma za serikali, na Muda wa anga/data kwa mitandao mikuu yote Afrika Mashariki.',
    payUtilityBillsAnswer: 'Nenda kwenye Malipo ya Bili → Chagua aina ya huduma → Ingiza nambari ya akaunti/mita yako → Thibitisha kiasi → Lipa kwa salio la mkoba au akaunti iliyounganishwa. Utapokea uthibitishaji wa papo hapo na risiti.',
    recurringPaymentsAnswer: 'Ndio! Unapolipa bili, chagua "Weka kama ya Mara kwa Mara" na chagua mzunguko (kila wiki, kila mwezi, kila robo). JiraniPay italipa bili zako kiotomatiki katika tarehe zilizopangwa.',
    addMoneyAnswer: 'Unaweza kuongeza pesa kupitia: 1) Uhamisho wa benki (papo hapo), 2) Pesa za simu (MTN, Airtel, n.k.), 3) Maeneo ya wakala, 4) Kadi za malipo/mikopo. Nenda kwenye Mkoba → Ongeza Pesa na chagua njia unayopendelea.',
    moneySafetyAnswer: 'Ndio! Fedha zako zinalindwa na usimbaji wa kiwango cha benki, zinahifadhiwa katika taasisi za kifedha zilizodhibitiwa, na zinalindwa na bima. Tunatumia uthibitishaji wa hatua nyingi na ufuatiliaji wa udanganyifu wa wakati halisi.',
    withdrawMoneyAnswer: 'Ndio, unaweza kutoa pesa kwenda: Akaunti yako ya benki iliyounganishwa (bure), Akaunti ya pesa za simu (ada ndogo), au tembelea eneo lolote la wakala wa JiraniPay kwa kutoa pesa taslimu.',
    qrScannerAnswer: 'Gusa ikoni ya msomaji wa QR → Elekeza kamera yako kwenye msimbo wowote wa QR wa JiraniPay → Programu itakutambua na kuchakata malipo au maelezo ya mawasiliano kiotomatiki. Bora kwa malipo ya haraka kwa wafanyabiashara!',
    qrCodeUsesAnswer: 'Misimbo ya QR inakuruhusu: Kulipa wafanyabiashara papo hapo, Kuongeza anwani za mawasiliano haraka, Kupokea malipo (tengeneza msimbo wako wa QR), Kupata ofa maalum, na Kujiunga na malipo ya kikundi au mzunguko wa akiba.',
    generateQRAnswer: 'Nenda kwenye Pokea Pesa → Gusa "Tengeneza Msimbo wa QR" → Weka kiasi (si lazima) → Shiriki msimbo wa QR. Wengine wanaweza kuusoma ili kukutumia pesa papo hapo bila kuandika nambari yako ya simu.',
    verifyAccountAnswer: 'Nenda kwenye Wasifu → Uthibitishaji wa Akaunti → Pakia: Kitambulisho halali (Kitambulisho cha Kitaifa, Paspoti, au Leseni ya Udereva), Uthibitisho wa anwani (bili ya huduma au taarifa ya benki), na piga picha ya uso. Uthibitishaji kwa kawaida unachukua masaa 24-48.',
    securityFeaturesAnswer: 'Tunatoa: Kuingia kwa kibayolojia (kidole/uso), Uthibitishaji wa hatua mbili, PIN za muamala, Ufuatiliaji wa udanganyifu wa wakati halisi, Tahadhari za akaunti, na uwezo wa kuganda akaunti yako papo hapo ikiwa inahitajika.',
    fraudAnswer: 'Papo hapo: 1) Ganda akaunti yako kwenye programu, 2) Wasiliana na msaada kupitia programu au simu ya msaada, 3) Badilisha PIN na nenosiri lako, 4) Ripoti tukio hilo. Tuna ufuatiliaji wa udanganyifu wa saa 24/7 na tutachunguza haraka.',

    // AI Chat Screen
    jiranipayAiAssistant: 'Msaidizi wa AI wa JiraniPay',
    startingAiAssistant: 'Kuanzisha Msaidizi wa AI...',
    aiIsTyping: 'AI inaandika...',
    quickHelp: 'Msaada wa Haraka',
    quickActions: 'Vitendo vya Haraka',
    chooseWhatYoudLikeHelpWith: 'Chagua unachotaka kusaidiwa nacho',
    tipYouCanAlsoTypeYourQuestionsNaturally: 'Kidokezo: Unaweza pia kuandika maswali yako kwa kawaida',
    pleaseLogInToUseAiChat: 'Tafadhali ingia ili kutumia mazungumzo ya AI',
    failedToInitializeChat: 'Imeshindwa kuanzisha mazungumzo',
    failedToStartChatSession: 'Imeshindwa kuanza kipindi cha mazungumzo',
    failedToSendMessagePleaseTryAgain: 'Imeshindwa kutuma ujumbe. Tafadhali jaribu tena.',
    failedToSendMessage: 'Imeshindwa kutuma ujumbe',
    clearChatHistory: 'Futa Historia ya Mazungumzo',
    areYouSureYouWantToClearAllChatMessagesThisActionC: 'Una uhakika unataka kufuta ujumbe wote wa mazungumzo? Kitendo hiki hakiwezi kutendua.',
    clear: 'Futa',
    aiIsThinking: '🤖 AI inafikiria...',
    readyToHelpWithJiraniPay: '✨ Tayari kusaidia na JiraniPay',

    // Contact Support Screen
    wereHereToHelpYou247: 'Tuko hapa kukusaidia saa 24/7',
    customerSupport: 'Msaada wa Wateja',
    ourDedicatedSupportTeamIsAvailable247ToAssistYouWi: 'Timu yetu ya msaada inapatikana saa 24/7 kukusaidia na maswali au masuala yoyote.',
    createTicket: 'Tengeneza Tiketi',
    submitADetailedSupportRequest: 'Wasilisha ombi la msaada la kina',
    aiAssistant: 'Msaidizi wa AI',
    getInstantHelpFromAi: 'Pata msaada wa haraka kutoka AI',
    contactMethods: 'Njia za Mawasiliano',
    chooseTheBestWayToReachUsBasedOnYourNeeds: 'Chagua njia bora ya kutufikia kulingana na mahitaji yako',
    whatCanWeHelpYouWith: 'Tunaweza kukusaidia na nini?',
    browseCommonSupportCategoriesToFindTheRightAssista: 'Vinjari makundi ya msaada ya kawaida kupata msaada unaofaa',
    emergencySupport: 'Msaada wa Dharura',
    securityEmergency: 'Dharura ya Usalama',
    ifYouSuspectFraudOrUnauthorizedAccessToYourAccount: 'Ikiwa unashtukia udanganyifu au ufikiaji usioruhusiwa wa akaunti yako, wasiliana nasi mara moja.',
    supportHours: 'Masaa ya Msaada',
    phoneSupport: 'Msaada wa Simu',
    alwaysAvailable247: 'Saa 24/7 - Inapatikana Kila Wakati',
    emailSupport: 'Msaada wa Barua Pepe',
    mondayFriday800Am600Pm: 'Jumatatu-Ijumaa 8:00 AM - 6:00 PM',
    technicalSupport: 'Msaada wa Kiufundi',
    mondayFriday800Am800Pm: 'Jumatatu-Ijumaa 8:00 AM - 8:00 PM',
    additionalResources: 'Rasilimali za Ziada',
    securityFaq: 'Maswali ya Usalama',
    findAnswersToCommonSecurityQuestions: 'Pata majibu ya maswali ya kawaida ya usalama',
    securityTips: 'Vidokezo vya Usalama',
    learnHowToKeepYourAccountSecure: 'Jifunze jinsi ya kuweka akaunti yako salama',
    callSupport: 'Piga Simu Msaada',
    doYouWantToCallPhonenumber: 'Unataka kupiga simu {phoneNumber}?',
    whatsappSupport: 'Msaada wa WhatsApp',
    doYouWantToMessageUsOnWhatsapp: 'Unataka kututumia ujumbe kwenye WhatsApp?',
    whatsappNotAvailable: 'WhatsApp Haipatikani',
    whatsappIsNotInstalledOnYourDevicePleaseInstallWha: 'WhatsApp haijasanikishwa kwenye kifaa chako. Tafadhali sakinisha WhatsApp au tumia njia nyingine ya mawasiliano.',
    doYouWantToSendAnEmailToEmail: 'Unataka kutuma barua pepe kwa {email}?',
    commonIssuesInThisCategory: 'Masuala ya kawaida katika kundi hili:',
    callInstead: 'Piga Simu Badala Yake'
  },

  // Analytics and Insights
  analytics: {
    title: 'Uchambuzi',
    overview: 'Muhtasari',
    thisWeek: 'Wiki Hii',
    thisMonth: 'Mwezi Huu',
    thisYear: 'Mwaka Huu',
    totalTransactions: 'Jumla ya Miamala',
    totalAmount: 'Jumla ya Kiasi',
    averageTransaction: 'Wastani wa Muamala',
    topCategories: 'Makundi Makuu',
    recentActivity: 'Shughuli za Hivi Karibuni',
    noDataAvailable: 'Hakuna data inayopatikana',
    failedToLoadAnalytics: 'Imeshindwa kupakia uchambuzi',
  },

  // Analytics Export
  analyticsExport: {
    title: 'Hamisha Uchambuzi',
    exportFormat: 'Muundo wa Kuhamisha',
    dateRange: 'Muda wa Tarehe',
    selectFormat: 'Chagua Muundo',
    selectDateRange: 'Chagua Muda wa Tarehe',
    exportData: 'Hamisha Data',
    exportSuccessful: 'Kuhamisha kumefanikiwa',
    exportFailed: 'Kuhamisha kumeshindikana',
    pdf: 'PDF',
    csv: 'CSV',
    excel: 'Excel',
    last7Days: 'Siku 7 za Mwisho',
    last30Days: 'Siku 30 za Mwisho',
    last90Days: 'Siku 90 za Mwisho',
    customRange: 'Muda Maalum',
  },

  // Savings Analytics
  savingsAnalytics: {
    title: 'Uchambuzi wa Akiba',
    totalSavings: 'Jumla ya Akiba',
    monthlyGrowth: 'Ukuaji wa Kila Mwezi',
    savingsGoals: 'Malengo ya Akiba',
    interestEarned: 'Riba Iliyopatikana',
    savingsRate: 'Kiwango cha Akiba',
    goalProgress: 'Maendeleo ya Lengo',
    emergencyFund: 'Fedha za Dharura',
    recommendedAmount: 'Kiasi Kinachopendekezwa',
    onTrackToGoal: 'Unaendelea vizuri kuelekea lengo',
    behindGoal: 'Umechelewa kidogo',
    goalAchieved: 'Lengo limefikwa',
  },

  // Investment Analytics
  investmentAnalytics: {
    title: 'Uchambuzi wa Uwekezaji',
    portfolioValue: 'Thamani ya Mkoba',
    totalReturn: 'Mapato ya Jumla',
    returnPercentage: 'Asilimia ya Mapato',
    volatility: 'Mabadiliko',
    sharpeRatio: 'Uwiano wa Sharpe',
    maxDrawdown: 'Upungufu Mkuu',
    portfolioAllocation: 'Ugawaji wa Mkoba',
    assetAllocation: 'Ugawaji wa Mali',
    sectorAllocation: 'Ugawaji wa Sekta',
    riskAnalysis: 'Uchambuzi wa Hatari',
    performanceMetrics: 'Vipimo vya Utendaji',
    marketData: 'Data ya Soko',
  },

  // Budget Insights
  budgetInsights: {
    title: 'Maarifa ya Bajeti',
    aiPoweredRecommendations: 'Mapendekezo ya Akili Bandia',
    budgetScore: 'Alama ya Bajeti',
    excellent: 'Bora Sana',
    good: 'Nzuri',
    fair: 'Wastani',
    needsImprovement: 'Inahitaji Kuboresha',
    excellentDescription: 'Unafanya vizuri sana! Endelea hivyo.',
    goodDescription: 'Maendeleo mazuri! Marekebisho machache yanaweza kuboresha alama yako.',
    improvementDescription: 'Kuna nafasi ya kuboresha. Hebu tufanye kazi pamoja.',
    aiRecommendations: '🤖 Mapendekezo ya Akili Bandia',
    quickWins: '⚡ Ushindi wa Haraka',
    easyChangesWithImmediateImpact: 'Mabadiliko rahisi yenye athari ya haraka',
    personalizedTips: '💡 Vidokezo vya Kibinafsi',
    basedOnSpendingPatterns: 'Kulingana na mifumo yako ya matumizi',
    impact: 'Athari',
    actionSteps: 'Hatua za Kitendo',
    generatingAIInsights: 'Inaunda maarifa ya akili bandia...',
  },

  // Support and Help System
  support: {
    contactSupport: 'Wasiliana na Msaada',
    customerSupport: 'Msaada wa Wateja',
    liveAIChat: 'Mazungumzo ya Moja kwa Moja ya AI',
    instantAIAssistant: 'Msaidizi wa Haraka wa AI',
    getImmediateHelpFromAI: 'Pata msaada wa haraka kutoka kwa msaidizi wetu wa AI',
    aiAssistant: 'Msaidizi wa AI',
    callSupport: 'Piga Simu Msaada',
    twentyFourSevenCustomerService: 'Huduma ya Wateja 24/7',
    speakDirectlyWithTeam: 'Zungumza moja kwa moja na timu yetu ya msaada',
    whatsAppSupport: 'Msaada wa WhatsApp',
    chatOnWhatsApp: 'Zungumza kwenye WhatsApp',
    messageUsOnWhatsApp: 'Tututumie ujumbe kwenye WhatsApp kwa msaada wa haraka',
    emailSupport: 'Msaada wa Barua Pepe',
    generalInquiries: 'Maswali ya Jumla',
    sendUsYourQuestions: 'Tututumie maswali na maoni yako',
    twentyFourSeven: '24/7',
    instant: 'Haraka',
    immediate: 'Mara Moja',
    monFri8AM8PM: 'Jumatatu-Ijumaa 8AM-8PM',
    monFri8AM6PM: 'Jumatatu-Ijumaa 8AM-6PM',
    fiveToFifteenMinutes: 'Dakika 5-15',
    twoToFourHours: 'Masaa 2-4',
    call: 'Piga Simu',
    sendEmail: 'Tuma Barua Pepe',
    openWhatsApp: 'Fungua WhatsApp',
    callInstead: 'Piga Simu Badala Yake',
    response: 'Jibu',
    contactMethods: 'Njia za Mawasiliano',
    getInstantHelpFromAi: 'Pata msaada wa haraka kutoka kwa AI',
  },

  // Create Ticket
  createTicket: {
    title: 'Unda Tiketi ya Msaada',
    accountIssues: 'Matatizo ya Akaunti',
    transactionProblems: 'Matatizo ya Miamala',
    walletBalance: 'Salio la Mkoba',
    securityConcerns: 'Masuala ya Usalama',
    billPayments: 'Malipo ya Bili',
    technicalSupport: 'Msaada wa Kiufundi',
    low: 'Chini',
    medium: 'Wastani',
    high: 'Juu',
    urgent: 'Dharura',
    generalQuestions: 'Maswali ya jumla',
    standardIssues: 'Matatizo ya kawaida',
    urgentProblems: 'Matatizo ya dharura',
    criticalIssues: 'Matatizo makubwa',
    subject: 'Mada',
    description: 'Maelezo',
    category: 'Kundi',
    priority: 'Kipaumbele',
    attachments: 'Viambatisho',
    missingInformation: 'Taarifa Zinazokosekana',
    pleaseEnterSubject: 'Tafadhali ingiza mada ya tiketi yako.',
    pleaseDescribeIssue: 'Tafadhali elezea tatizo lako.',
    pleaseSelectCategory: 'Tafadhali chagua kundi.',
    pleaseLogInToCreateTicket: 'Tafadhali ingia ili kuunda tiketi ya msaada.',
    permissionRequired: 'Ruhusa Inahitajika',
    grantCameraRollPermissions: 'Tafadhali ruhusu ufikiaji wa picha ili kuambatisha mifano.',
    failedToPickImage: 'Imeshindwa kuchagua picha',
    failedToPickDocument: 'Imeshindwa kuchagua hati',
    success: 'Imefanikiwa',
    ticketCreatedSuccessfully: 'Tiketi Imeundwa Kwa Mafanikio',
    ticketCreatedMessage: 'Tiketi yako ya msaada {ticketId} imeundwa.\\n\\nMuda wa kujibu: {responseTime}\\n\\nUnaweza kufuatilia tiketi yako katika sehemu ya Msaada.',
    viewTickets: 'Ona Tiketi',
    failedToCreateTicket: 'Imeshindwa kuunda tiketi ya msaada. Tafadhali jaribu tena.',
    subjectPlaceholder: 'Maelezo mafupi ya tatizo lako',
    descriptionPlaceholder: 'Tafadhali toa maelezo kamili kuhusu tatizo lako...',
    selectCategory: 'Chagua kundi',
    selectPriority: 'Chagua kiwango cha kipaumbele',
    submitTicket: 'Wasilisha Tiketi',
    addAttachment: 'Ongeza Kiambatisho',
    removeAttachment: 'Ondoa',
  },

  // Security FAQ
  securityFAQ: {
    title: 'Maswali ya Usalama',
    searchSecurityQuestions: 'Tafuta maswali ya usalama...',
    questionsCount: 'Maswali {count}',
    accountSecurity: 'Usalama wa Akaunti',
    biometricAuthentication: 'Uthibitishaji wa Kibayolojia',
    pinPasswordManagement: 'Usimamizi wa PIN na Nenosiri',
    transactionSecurity: 'Usalama wa Miamala',
    suspiciousActivity: 'Shughuli za Kutilia Shaka',
    twoFactorAuthentication: 'Uthibitishaji wa Hatua Mbili',
  },

  // Security Tips
  securityTips: {
    title: 'Vidokezo vya Usalama',
    authentication: 'Uthibitishaji',
    deviceSecurity: 'Usalama wa Kifaa',
    useStrongAuthentication: 'Tumia Uthibitishaji Thabiti',
    secureYourDevice: 'Linda Kifaa Chako',
    enableBiometricAndStrongPIN: 'Washa uthibitishaji wa kibayolojia na tumia PIN thabiti',
    keepDeviceLockedAndUpdated: 'Weka kifaa chako kimefungwa na kimesasishwa',
  },

  // Settings and Configuration
  settings: {
    failedToLoadSettings: 'Imeshindwa kupakia mipangilio. Tafadhali jaribu tena.',
    languageUpdatedSuccessfully: 'Lugha imesasishwa kwa mafanikio',
    failedToUpdateLanguage: 'Imeshindwa kusasisha lugha. Tafadhali jaribu tena.',
    currencyUpdatedTo: 'Sarafu imesasishwa kuwa {currency}',
    failedToUpdateCurrency: 'Imeshindwa kusasisha sarafu. Tafadhali jaribu tena.',
    failedToLoadNotificationPreferences: 'Imeshindwa kupakia mapendeleo ya arifa. Tafadhali jaribu tena.',
    failedToUpdatePreference: 'Imeshindwa kusasisha pendeleo. Tafadhali jaribu tena.',
    failedToUpdateQuietHours: 'Imeshindwa kusasisha masaa ya kimya. Tafadhali jaribu tena.',
    resetPreferences: 'Weka Upya Mapendeleo',
    resetPreferencesConfirmation: 'Una uhakika unataka kuweka upya mapendeleo yote ya arifa kuwa ya msingi?',
    reset: 'Weka Upya',
    preferencesResetToDefaults: 'Mapendeleo yamewekwa upya kuwa ya msingi',
    failedToResetPreferences: 'Imeshindwa kuweka upya mapendeleo',
    failedToLoadPrivacySettings: 'Imeshindwa kupakia mipangilio ya faragha',
    revokeConsent: 'Batilisha Idhini',
    revokeConsentConfirmation: 'Una uhakika unataka kuzima {consentType}? Hii itasimamisha ukusanyaji wa data husika na inaweza kuathiri utendaji wa programu.',
    disable: 'Zima',
    deleteAccount: 'Futa Akaunti',
    deleteAccountConfirmation: 'Una uhakika unataka kufuta akaunti yako? Kitendo hiki hakiwezi kutendua na data yako yote itafutwa kabisa.',
    delete: 'Futa',
    fiveMinutes: 'Dakika 5',
    fifteenMinutes: 'Dakika 15',
    thirtyMinutes: 'Dakika 30',
    oneHour: 'Saa 1',
    twoHours: 'Masaa 2',
    highSecurityFrequentAuth: 'Usalama wa juu - uthibitishaji wa mara kwa mara',
    balancedSecurityConvenience: 'Usalama na urahisi uliosawiazana',
    standardSecurityRecommended: 'Usalama wa kawaida (inashauriwa)',
    extendedSessionConvenience: 'Kipindi kirefu kwa urahisi',
    lowSecurityMaxConvenience: 'Usalama wa chini - urahisi mkuu',
  },

  // Budget Components
  budget: {
    overBudget: 'Umezidi Bajeti',
    onTrack: 'Unaendelea Vizuri',
    underBudget: 'Chini ya Bajeti',
    since: 'Tangu {date}',
  },

  // Additional Alert Messages
  readyToStartSaving: 'Uko tayari kuanza kuokoa?\\n\\nHatua za Kitendo:\\n{steps}',
    readyToOptimizeBills: 'Uko tayari kuboresha bili zako?\\n\\nHatua za Kitendo:\\n{steps}',
    readyToBuildEmergencyFund: 'Uko tayari kujenga fedha za dharura?\\n\\nHatua za Kitendo:\\n{steps}',
    readyToImplementRecommendation: 'Uko tayari kutekeleza pendekezo hili?\\n\\nHatua za Kitendo:\\n{steps}',
    setReminder: 'Weka Ukumbusho',
    autoSavings: 'Akiba za Otomatiki',
    startSaving: 'Anza Kuokoa',
    startNow: 'Anza Sasa',
    createFund: 'Unda Fedha',
    notNow: 'Sio Sasa',

  // Currency and Financial Terms
  currency: {
    ugx: 'Shilingi za Uganda',
    kes: 'Shilingi za Kenya',
    tzs: 'Shilingi za Tanzania',
    rwf: 'Faranga za Rwanda',
    bif: 'Faranga za Burundi',
    etb: 'Birr za Ethiopia',
    sdg: 'Pauni za Sudan',
    sos: 'Shilingi za Somalia',
    usd: 'Dola za Marekani',
    eur: 'Euro',
  },

  // Time and Date
  time: {
    morning: 'asubuhi',
    afternoon: 'mchana',
    evening: 'jioni',
    night: 'usiku',
    today: 'leo',
    yesterday: 'jana',
    tomorrow: 'kesho',
    thisWeek: 'wiki hii',
    lastWeek: 'wiki iliyopita',
    thisMonth: 'mwezi huu',
    lastMonth: 'mwezi uliopita',
    thisYear: 'mwaka huu',
    lastYear: 'mwaka uliopita',
  },

  // Validation Messages
  validation: {
    required: 'Sehemu hii inahitajika',
    invalidEmail: 'Barua pepe si sahihi',
    invalidPhone: 'Nambari ya simu si sahihi',
    passwordTooShort: 'Nenosiri ni fupi sana',
    passwordsDoNotMatch: 'Manenosiri hayalingani',
    invalidAmount: 'Kiasi si sahihi',
    amountTooLow: 'Kiasi ni kidogo sana',
    amountTooHigh: 'Kiasi ni kikubwa sana',
    insufficientFunds: 'Fedha hazitoshi',
  },

  // Success Messages
  successMessages: {
    transactionCompleted: 'Muamala umekamilika kwa mafanikio',
    paymentSent: 'Malipo yametumwa kwa mafanikio',
    moneyReceived: 'Fedha zimepokelewa kwa mafanikio',
    accountCreated: 'Akaunti imeundwa kwa mafanikio',
    profileUpdated: 'Wasifu umesasishwa kwa mafanikio',
    passwordChanged: 'Nenosiri limebadilishwa kwa mafanikio',
    settingsSaved: 'Mipangilio imehifadhiwa kwa mafanikio',
  },

  // Error Messages
  errorMessages: {
    networkError: 'Hitilafu ya mtandao. Tafadhali jaribu tena.',
    serverError: 'Hitilafu ya seva. Tafadhali jaribu tena baadaye.',
    authenticationFailed: 'Uthibitishaji umeshindikana',
    transactionFailed: 'Muamala umeshindikana',
    insufficientBalance: 'Salio halitoshi',
    invalidCredentials: 'Taarifa za kuingia si sahihi',
    accountLocked: 'Akaunti imefungwa',
    sessionExpired: 'Kipindi kimemalizika',
    permissionDenied: 'Ruhusa imekataliwa',
    fileUploadFailed: 'Kupakia faili kumeshindikana',
  }
};
