import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import * as LocalAuthentication from 'expo-local-authentication';

import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import qrPaymentService from '../services/qrPaymentService';
import sendMoneyService from '../services/sendMoneyService';

const { width } = Dimensions.get('window');

/**
 * QR Payment Confirmation Screen
 * Handles secure payment confirmation for QR-based transactions
 */
const QRPaymentConfirmationScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { formatAmount } = useCurrencyContext();
  const styles = createStyles(theme);

  // Route parameters
  const { recipient, amount, purpose, qrData, securityRequirements } = route.params;

  // State
  const [processing, setProcessing] = useState(false);
  const [walletBalance, setWalletBalance] = useState(null);
  const [feeCalculation, setFeeCalculation] = useState(null);
  const [securityPassed, setSecurityPassed] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);

  useEffect(() => {
    loadPaymentDetails();
    checkBiometricAvailability();
  }, []);

  /**
   * Load payment details and validate
   */
  const loadPaymentDetails = async () => {
    try {
      // Calculate fees
      const fees = sendMoneyService.calculateTransferFee(amount);
      setFeeCalculation(fees);

      // Get wallet balance
      const balanceCheck = await qrPaymentService.validateWalletBalance(
        qrData.user?.id || 'current-user', 
        amount
      );
      
      if (balanceCheck.success) {
        setWalletBalance(balanceCheck.balance);
      } else {
        Alert.alert('Insufficient Balance', balanceCheck.error, [
          { text: 'Top Up', onPress: () => navigation.navigate('TopUp') },
          { text: 'Cancel', onPress: () => navigation.goBack() }
        ]);
      }
    } catch (error) {
      console.error('Load payment details error:', error);
      Alert.alert('Error', 'Failed to load payment details');
    }
  };

  /**
   * Check biometric availability
   */
  const checkBiometricAvailability = async () => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      setBiometricAvailable(hasHardware && isEnrolled);
    } catch (error) {
      console.error('Biometric check error:', error);
      setBiometricAvailable(false);
    }
  };

  /**
   * Handle security authentication
   */
  const handleSecurityAuthentication = async () => {
    try {
      if (securityRequirements.requiresBiometric && biometricAvailable) {
        const result = await LocalAuthentication.authenticateAsync({
          promptMessage: 'Authenticate to confirm payment',
          fallbackLabel: 'Use PIN',
        });

        if (result.success) {
          setSecurityPassed(true);
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          return true;
        } else {
          Alert.alert('Authentication Failed', 'Please try again');
          return false;
        }
      } else if (securityRequirements.requiresPIN) {
        // In production, implement PIN entry
        Alert.alert('PIN Required', 'PIN authentication would be required here');
        setSecurityPassed(true);
        return true;
      } else {
        setSecurityPassed(true);
        return true;
      }
    } catch (error) {
      console.error('Security authentication error:', error);
      Alert.alert('Authentication Error', 'Failed to authenticate. Please try again.');
      return false;
    }
  };

  /**
   * Process the QR payment
   */
  const processPayment = async () => {
    try {
      setProcessing(true);

      // Security check
      if (!securityPassed) {
        const authSuccess = await handleSecurityAuthentication();
        if (!authSuccess) {
          setProcessing(false);
          return;
        }
      }

      // Process payment through QR payment service
      const result = await qrPaymentService.processQRPayment(qrData, {
        amount: amount,
        purpose: purpose,
      });

      if (result.success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        
        // Navigate to success screen
        navigation.replace('PaymentSuccess', {
          transaction: result.transaction,
          recipient: recipient,
          amount: amount,
          reference: result.reference,
          qrReference: result.qrReference,
          paymentMethod: 'QR Payment',
        });
      } else {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        Alert.alert('Payment Failed', result.error || 'Payment processing failed');
      }
    } catch (error) {
      console.error('Process payment error:', error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Payment Error', 'An error occurred while processing the payment');
    } finally {
      setProcessing(false);
    }
  };

  /**
   * Render payment summary
   */
  const renderPaymentSummary = () => (
    <View style={styles.summaryContainer}>
      <Text style={styles.summaryTitle}>Payment Summary</Text>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>To:</Text>
        <Text style={styles.summaryValue}>{recipient.name}</Text>
      </View>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Phone:</Text>
        <Text style={styles.summaryValue}>{recipient.phoneNumber}</Text>
      </View>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Amount:</Text>
        <Text style={styles.summaryAmount}>{formatAmount(amount, 'UGX')}</Text>
      </View>
      
      {feeCalculation && (
        <>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Fee:</Text>
            <Text style={styles.summaryValue}>
              {feeCalculation.freeTransfer ? 'FREE' : formatAmount(feeCalculation.fee, 'UGX')}
            </Text>
          </View>
          
          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total:</Text>
            <Text style={styles.totalAmount}>{formatAmount(feeCalculation.total, 'UGX')}</Text>
          </View>
        </>
      )}
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Purpose:</Text>
        <Text style={styles.summaryValue}>{purpose}</Text>
      </View>
    </View>
  );

  /**
   * Render security requirements
   */
  const renderSecurityInfo = () => (
    <View style={styles.securityContainer}>
      <View style={styles.securityHeader}>
        <Ionicons name="shield-checkmark" size={20} color={theme.colors.primary} />
        <Text style={styles.securityTitle}>Security</Text>
      </View>
      
      {securityRequirements.requiresBiometric && (
        <View style={styles.securityItem}>
          <Ionicons 
            name={securityPassed ? "checkmark-circle" : "finger-print"} 
            size={16} 
            color={securityPassed ? theme.colors.success : theme.colors.text} 
          />
          <Text style={styles.securityText}>
            {securityPassed ? 'Biometric authentication completed' : 'Biometric authentication required'}
          </Text>
        </View>
      )}
      
      {securityRequirements.requiresPIN && (
        <View style={styles.securityItem}>
          <Ionicons 
            name={securityPassed ? "checkmark-circle" : "keypad"} 
            size={16} 
            color={securityPassed ? theme.colors.success : theme.colors.text} 
          />
          <Text style={styles.securityText}>
            {securityPassed ? 'PIN authentication completed' : 'PIN authentication required'}
          </Text>
        </View>
      )}
    </View>
  );

  /**
   * Render QR info
   */
  const renderQRInfo = () => (
    <View style={styles.qrInfoContainer}>
      <View style={styles.qrHeader}>
        <Ionicons name="qr-code" size={20} color={theme.colors.primary} />
        <Text style={styles.qrTitle}>QR Payment Details</Text>
      </View>
      
      <View style={styles.qrItem}>
        <Text style={styles.qrLabel}>QR Reference:</Text>
        <Text style={styles.qrValue}>{qrData.reference}</Text>
      </View>
      
      <View style={styles.qrItem}>
        <Text style={styles.qrLabel}>QR Type:</Text>
        <Text style={styles.qrValue}>{qrData.type.replace('_', ' ').toUpperCase()}</Text>
      </View>
      
      {qrData.expires && (
        <View style={styles.qrItem}>
          <Text style={styles.qrLabel}>Expires:</Text>
          <Text style={styles.qrValue}>
            {new Date(qrData.expires).toLocaleString()}
          </Text>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Confirm QR Payment</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Payment Summary */}
        {renderPaymentSummary()}
        
        {/* Security Info */}
        {renderSecurityInfo()}
        
        {/* QR Info */}
        {renderQRInfo()}
        
        {/* Wallet Balance */}
        {walletBalance !== null && (
          <View style={styles.balanceContainer}>
            <Text style={styles.balanceLabel}>Available Balance:</Text>
            <Text style={styles.balanceAmount}>{formatAmount(walletBalance, 'UGX')}</Text>
          </View>
        )}
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[styles.button, styles.cancelButton]}
          onPress={() => navigation.goBack()}
          disabled={processing}
        >
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.button, styles.confirmButton, processing && styles.disabledButton]}
          onPress={processPayment}
          disabled={processing}
        >
          {processing ? (
            <ActivityIndicator color={theme.colors.white} size="small" />
          ) : (
            <>
              <Ionicons name="card" size={20} color={theme.colors.white} />
              <Text style={styles.confirmButtonText}>Pay Now</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  summaryContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  summaryValue: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  summaryAmount: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    marginTop: 8,
    paddingTop: 12,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.primary,
  },
  securityContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  securityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  securityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  securityText: {
    fontSize: 14,
    color: theme.colors.text,
    marginLeft: 8,
  },
  qrInfoContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  qrHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  qrTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  qrItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  qrLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  qrValue: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  balanceContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  balanceAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.success,
  },
  actionContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  confirmButton: {
    backgroundColor: theme.colors.primary,
    flexDirection: 'row',
    gap: 8,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default QRPaymentConfirmationScreen;
