/**
 * Verification and Security Screens i18n Implementation Test
 * 
 * Tests to verify that AccountVerificationScreen.js, SecuritySettingsScreen.js, 
 * and TwoFactorAuthScreen.js have been properly internationalized
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Verification and Security Screens i18n Implementation\n');

// Read all screen files
const accountVerificationPath = path.join(__dirname, '../screens/AccountVerificationScreen.js');
const securitySettingsPath = path.join(__dirname, '../screens/SecuritySettingsScreen.js');
const twoFactorAuthPath = path.join(__dirname, '../screens/TwoFactorAuthScreen.js');

const accountVerificationContent = fs.readFileSync(accountVerificationPath, 'utf8');
const securitySettingsContent = fs.readFileSync(securitySettingsPath, 'utf8');
const twoFactorAuthContent = fs.readFileSync(twoFactorAuthPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

const screens = [
  { name: 'AccountVerificationScreen', content: accountVerificationContent },
  { name: 'SecuritySettingsScreen', content: securitySettingsContent },
  { name: 'TwoFactorAuthScreen', content: twoFactorAuthContent }
];

let totalTests = 0;
let passedTests = 0;

// Test 1: Check that useLanguage hook is imported in all screens
console.log('✅ Test 1: useLanguage hook import');
screens.forEach(screen => {
  const hasUseLanguageImport = screen.content.includes("import { useLanguage } from '../contexts/LanguageContext'");
  console.log(`   ${screen.name}: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);
  totalTests++;
  if (hasUseLanguageImport) passedTests++;
});

// Test 2: Check that t function is destructured in all screens
console.log('\n✅ Test 2: t function destructuring');
screens.forEach(screen => {
  const hasTFunction = screen.content.includes('const { t }') || screen.content.includes('const { t,');
  console.log(`   ${screen.name}: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);
  totalTests++;
  if (hasTFunction) passedTests++;
});

// Test 3: Check for hardcoded Alert.alert strings
console.log('\n✅ Test 3: Alert.alert internationalization');
const hardcodedAlertStrings = [
  'Verification Code Sent',
  'Failed to send verification code',
  'Invalid verification code. Please try again.',
  'Biometric authentication is not available on this device'
];

screens.forEach(screen => {
  let hardcodedAlertsFound = [];
  hardcodedAlertStrings.forEach(str => {
    const alertPattern = new RegExp(`Alert\\.alert\\([^)]*['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
    if (alertPattern.test(screen.content)) {
      hardcodedAlertsFound.push(str);
    }
  });
  
  const alertPattern = /Alert\.alert\([^)]*t\(/g;
  const alertMatches = screen.content.match(alertPattern);
  const totalAlerts = (screen.content.match(/Alert\.alert\(/g) || []).length;
  const translatedAlerts = alertMatches ? alertMatches.length : 0;
  
  const isFullyTranslated = hardcodedAlertsFound.length === 0 && translatedAlerts === totalAlerts;
  console.log(`   ${screen.name}: ${isFullyTranslated ? '✅ PASS' : '❌ FAIL'} (${translatedAlerts}/${totalAlerts} alerts translated)`);
  totalTests++;
  if (isFullyTranslated) passedTests++;
});

// Test 4: Check for hardcoded Text component strings
console.log('\n✅ Test 4: Hardcoded Text component strings');
const hardcodedTextStrings = [
  'Basic Account',
  'Standard Account', 
  'Premium Account',
  'Basic Information',
  'Phone Verification',
  'Email Verification',
  'Identity Verification',
  'Address Verification',
  'SMS Text Message',
  'Send to',
  'Phone number required',
  'Email address required',
  'Sending...',
  'Resend Code'
];

screens.forEach(screen => {
  let hardcodedTextFound = [];
  hardcodedTextStrings.forEach(str => {
    const textPattern = new RegExp(`['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
    if (textPattern.test(screen.content) && !screen.content.includes(`t('`)) {
      hardcodedTextFound.push(str);
    }
  });
  
  const hasHardcodedText = hardcodedTextFound.length === 0;
  console.log(`   ${screen.name}: ${hasHardcodedText ? '✅ PASS' : '❌ FAIL'}`);
  if (hardcodedTextFound.length > 0) {
    hardcodedTextFound.forEach(str => console.log(`     - "${str}"`));
  }
  totalTests++;
  if (hasHardcodedText) passedTests++;
});

// Test 5: Check for proper translation key usage
console.log('\n✅ Test 5: Translation key usage');
const expectedTranslationKeys = [
  // AccountVerificationScreen
  't(\'verification.basicAccount\')',
  't(\'verification.standardAccount\')',
  't(\'verification.premiumAccount\')',
  't(\'verification.basicInformation\')',
  't(\'verification.phoneVerification\')',
  't(\'verification.emailVerification\')',
  't(\'verification.identityVerification\')',
  't(\'verification.addressVerification\')',
  
  // SecuritySettingsScreen
  't(\'securitySettings\')',
  't(\'security.biometricNotAvailableMessage\')',
  
  // TwoFactorAuthScreen
  't(\'twoFactor.verificationCodeSent\')',
  't(\'twoFactor.smsTextMessage\')',
  't(\'twoFactor.email\')',
  't(\'twoFactor.sendToPhone\')',
  't(\'twoFactor.sendToEmail\')',
  't(\'twoFactor.resendCode\')'
];

let totalKeysFound = 0;
expectedTranslationKeys.forEach(key => {
  const foundInAnyScreen = screens.some(screen => screen.content.includes(key));
  if (foundInAnyScreen) totalKeysFound++;
});

const keyUsageScore = totalKeysFound / expectedTranslationKeys.length;
console.log(`   Translation keys found: ${totalKeysFound}/${expectedTranslationKeys.length} ${keyUsageScore === 1 ? '✅ PASS' : '❌ PARTIAL'}`);
totalTests++;
if (keyUsageScore === 1) passedTests++;

// Test 6: Check that required translation keys exist in en.js
console.log('\n✅ Test 6: Translation keys in en.js');
const requiredSections = [
  'verification: {',
  'security: {',
  'twoFactor: {'
];

const requiredKeys = [
  'basicAccount:',
  'standardAccount:',
  'premiumAccount:',
  'basicInformation:',
  'phoneVerification:',
  'emailVerification:',
  'identityVerification:',
  'addressVerification:',
  'biometricNotAvailableMessage:',
  'verificationCodeSent:',
  'smsTextMessage:',
  'sendToPhone:',
  'sendToEmail:',
  'resendCode:'
];

let sectionsFound = 0;
requiredSections.forEach(section => {
  if (enTranslationsContent.includes(section)) {
    sectionsFound++;
  }
});

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

const translationScore = (sectionsFound / requiredSections.length) * (keysInTranslations / requiredKeys.length);
console.log(`   Required sections: ${sectionsFound}/${requiredSections.length}`);
console.log(`   Required keys: ${keysInTranslations}/${requiredKeys.length}`);
console.log(`   Overall translation completeness: ${translationScore === 1 ? '✅ PASS' : '❌ PARTIAL'}`);
totalTests++;
if (translationScore === 1) passedTests++;

// Test 7: Check for string interpolation usage
console.log('\n✅ Test 7: String interpolation');
const interpolationPatterns = [
  't(\'twoFactor.sendToPhone\', { phoneNumber',
  't(\'twoFactor.sendToEmail\', { email',
  't(\'twoFactor.enterCodeSentTo\', { method'
];

let interpolationFound = 0;
interpolationPatterns.forEach(pattern => {
  const foundInAnyScreen = screens.some(screen => screen.content.includes(pattern));
  if (foundInAnyScreen) interpolationFound++;
});

const interpolationScore = interpolationFound / interpolationPatterns.length;
console.log(`   String interpolation patterns: ${interpolationFound}/${interpolationPatterns.length} ${interpolationScore === 1 ? '✅ PASS' : '❌ PARTIAL'}`);
totalTests++;
if (interpolationScore === 1) passedTests++;

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 Verification and Security Screens i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
  console.log('   AccountVerificationScreen.js: ✅ COMPLETE');
  console.log('   SecuritySettingsScreen.js: ✅ COMPLETE');
  console.log('   TwoFactorAuthScreen.js: ✅ COMPLETE');
} else {
  console.log('\n⚠️  Verification and Security Screens i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test verification flows with different languages');
console.log('2. Verify security settings work correctly in multiple languages');
console.log('3. Test two-factor authentication setup in different languages');
console.log('4. Proceed to Error Handling Message Standardization');
