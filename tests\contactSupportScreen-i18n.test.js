/**
 * ContactSupportScreen i18n Implementation Test
 * 
 * Tests to verify that ContactSupportScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing ContactSupportScreen.js i18n Implementation\n');

// Read the ContactSupportScreen.js file
const contactSupportScreenPath = path.join(__dirname, '../screens/ContactSupportScreen.js');
const contactSupportScreenContent = fs.readFileSync(contactSupportScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = contactSupportScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = contactSupportScreenContent.includes('const { t }') || contactSupportScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded Alert.alert strings replacement
console.log('\n✅ Test 3: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = contactSupportScreenContent.match(alertPattern);
const totalAlerts = (contactSupportScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 4: Check for hardcoded button text in alerts
console.log('\n✅ Test 4: Alert button text internationalization');
const buttonTextPatterns = [
  't(\'common.cancel\')',
  't(\'support.call\')',
  't(\'support.sendEmail\')',
  't(\'support.openWhatsApp\')',
  't(\'support.callInstead\')',
  't(\'common.ok\')'
];

let buttonTextFound = 0;
buttonTextPatterns.forEach(pattern => {
  if (contactSupportScreenContent.includes(pattern)) {
    buttonTextFound++;
  }
});

console.log(`   Translated button text: ${buttonTextFound}/${buttonTextPatterns.length} ${buttonTextFound === buttonTextPatterns.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 5: Check for support options internationalization
console.log('\n✅ Test 5: Support options internationalization');
const supportOptionKeys = [
  't(\'support.liveAIChat\')',
  't(\'support.instantAIAssistant\')',
  't(\'support.getImmediateHelpFromAI\')',
  't(\'support.aiAssistant\')',
  't(\'support.callSupport\')',
  't(\'support.twentyFourSevenCustomerService\')',
  't(\'support.speakDirectlyWithTeam\')',
  't(\'support.whatsAppSupport\')',
  't(\'support.chatOnWhatsApp\')',
  't(\'support.messageUsOnWhatsApp\')',
  't(\'support.emailSupport\')',
  't(\'support.generalInquiries\')',
  't(\'support.sendUsYourQuestions\')'
];

let supportKeysFound = 0;
supportOptionKeys.forEach(key => {
  if (contactSupportScreenContent.includes(key)) {
    supportKeysFound++;
  }
});

console.log(`   Support option keys: ${supportKeysFound}/${supportOptionKeys.length} ${supportKeysFound === supportOptionKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check for availability and response time internationalization
console.log('\n✅ Test 6: Availability and response time internationalization');
const availabilityKeys = [
  't(\'support.twentyFourSeven\')',
  't(\'support.instant\')',
  't(\'support.immediate\')',
  't(\'support.monFri8AM8PM\')',
  't(\'support.monFri8AM6PM\')',
  't(\'support.fiveToFifteenMinutes\')',
  't(\'support.twoToFourHours\')'
];

let availabilityKeysFound = 0;
availabilityKeys.forEach(key => {
  if (contactSupportScreenContent.includes(key)) {
    availabilityKeysFound++;
  }
});

console.log(`   Availability/response keys: ${availabilityKeysFound}/${availabilityKeys.length} ${availabilityKeysFound === availabilityKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for "Response:" text internationalization
console.log('\n✅ Test 7: Response text internationalization');
const responseTextPattern = /t\('support\.response'\)/g;
const responseTextMatches = contactSupportScreenContent.match(responseTextPattern);
const responseTextInternationalized = responseTextMatches && responseTextMatches.length > 0;

console.log(`   Response text internationalized: ${responseTextInternationalized ? '✅ PASS' : '❌ FAIL'}`);

// Test 8: Check that required translation keys exist in en.js
console.log('\n✅ Test 8: Translation keys in en.js');
const requiredKeys = [
  'contactSupport:',
  'customerSupport:',
  'liveAIChat:',
  'instantAIAssistant:',
  'getImmediateHelpFromAI:',
  'aiAssistant:',
  'callSupport:',
  'twentyFourSevenCustomerService:',
  'speakDirectlyWithTeam:',
  'whatsAppSupport:',
  'chatOnWhatsApp:',
  'messageUsOnWhatsApp:',
  'emailSupport:',
  'generalInquiries:',
  'sendUsYourQuestions:',
  'twentyFourSeven:',
  'instant:',
  'immediate:',
  'monFri8AM8PM:',
  'monFri8AM6PM:',
  'fiveToFifteenMinutes:',
  'twoToFourHours:',
  'call:',
  'sendEmail:',
  'openWhatsApp:',
  'callInstead:',
  'response:',
  'contactMethods:',
  'getInstantHelpFromAi:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 9: Check support section exists in en.js
console.log('\n✅ Test 9: Support section in en.js');
const supportSection = enTranslationsContent.includes('support: {');
console.log(`   Support section exists: ${supportSection ? '✅ PASS' : '❌ FAIL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 9;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (buttonTextFound === buttonTextPatterns.length) passedTests++;
if (supportKeysFound === supportOptionKeys.length) passedTests++;
if (availabilityKeysFound === availabilityKeys.length) passedTests++;
if (responseTextInternationalized) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (supportSection) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 ContactSupportScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
  console.log('   Support categories, contact methods, and form labels are internationalized!');
} else {
  console.log('\n⚠️  ContactSupportScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test contact support with different languages');
console.log('2. Verify support options display correctly');
console.log('3. Test contact method functionality in multiple languages');
console.log('4. Proceed to CreateTicketScreen.js implementation');
