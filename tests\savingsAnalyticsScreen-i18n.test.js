/**
 * SavingsAnalyticsScreen i18n Implementation Test
 * 
 * Tests to verify that SavingsAnalyticsScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing SavingsAnalyticsScreen.js i18n Implementation\n');

// Read the SavingsAnalyticsScreen.js file
const savingsAnalyticsScreenPath = path.join(__dirname, '../screens/SavingsAnalyticsScreen.js');
const savingsAnalyticsScreenContent = fs.readFileSync(savingsAnalyticsScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = savingsAnalyticsScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = savingsAnalyticsScreenContent.includes('const { t }') || savingsAnalyticsScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded Alert.alert strings replacement
console.log('\n✅ Test 3: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = savingsAnalyticsScreenContent.match(alertPattern);
const totalAlerts = (savingsAnalyticsScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 4: Check for hardcoded Text component strings
console.log('\n✅ Test 4: Hardcoded Text component strings');
const hardcodedTextStrings = [
  'Savings Analytics',
  'Key Metrics',
  'Total Savings',
  'Interest Earned',
  'Avg Monthly Deposit',
  'Goal Completion',
  'Account Breakdown',
  'Monthly Trends',
  'Smart Insights',
  'Deposits',
  'Withdrawals',
  'Loading analytics...',
  'No Analytics Available',
  'Create Savings Account'
];

let hardcodedTextFound = [];
hardcodedTextStrings.forEach(str => {
  const textPattern = new RegExp(`<Text[^>]*>\\s*${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*</Text>`, 'g');
  const directTextPattern = new RegExp(`['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (textPattern.test(savingsAnalyticsScreenContent) || 
      (directTextPattern.test(savingsAnalyticsScreenContent) && !savingsAnalyticsScreenContent.includes(`t('savingsAnalytics.`))) {
    hardcodedTextFound.push(str);
  }
});

if (hardcodedTextFound.length === 0) {
  console.log('   All hardcoded Text strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Text strings still found: ❌ FAIL');
  hardcodedTextFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 5: Check for proper translation key usage
console.log('\n✅ Test 5: Translation key usage');
const translationKeys = [
  't(\'savingsAnalytics.title\')',
  't(\'savingsAnalytics.keyMetrics\')',
  't(\'savingsAnalytics.totalSavings\')',
  't(\'savingsAnalytics.interestEarned\')',
  't(\'savingsAnalytics.avgMonthlyDeposit\')',
  't(\'savingsAnalytics.goalCompletion\')',
  't(\'savingsAnalytics.accountBreakdown\')',
  't(\'savingsAnalytics.monthlyTrends\')',
  't(\'savingsAnalytics.smartInsights\')',
  't(\'savingsAnalytics.deposits\')',
  't(\'savingsAnalytics.withdrawals\')',
  't(\'savingsAnalytics.loadingAnalytics\')',
  't(\'savingsAnalytics.noAnalyticsAvailable\')',
  't(\'savingsAnalytics.createSavingsAccount\')'
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (savingsAnalyticsScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check for insight message internationalization
console.log('\n✅ Test 6: Insight message internationalization');
const insightKeys = [
  't(\'savingsAnalytics.excellentProgress\')',
  't(\'savingsAnalytics.goalAlert\')',
  't(\'savingsAnalytics.emergencyFundRecommendation\')',
  't(\'savingsAnalytics.buildEmergencyFund\')',
  't(\'savingsAnalytics.earningInterest\')'
];

let insightKeysFound = 0;
insightKeys.forEach(key => {
  if (savingsAnalyticsScreenContent.includes(key)) {
    insightKeysFound++;
  }
});

console.log(`   Insight translation keys: ${insightKeysFound}/${insightKeys.length} ${insightKeysFound === insightKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for string interpolation usage
console.log('\n✅ Test 7: String interpolation');
const interpolationPatterns = [
  't(\'savingsAnalytics.goalProgressMessage\', { percentage:',
  't(\'savingsAnalytics.interestEarnedMessage\', { amount:'
];

let interpolationFound = 0;
interpolationPatterns.forEach(pattern => {
  if (savingsAnalyticsScreenContent.includes(pattern)) {
    interpolationFound++;
  }
});

console.log(`   String interpolation patterns: ${interpolationFound}/${interpolationPatterns.length} ${interpolationFound === interpolationPatterns.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 8: Check that required translation keys exist in en.js
console.log('\n✅ Test 8: Translation keys in en.js');
const requiredKeys = [
  'title:',
  'keyMetrics:',
  'totalSavings:',
  'interestEarned:',
  'avgMonthlyDeposit:',
  'goalCompletion:',
  'accountBreakdown:',
  'monthlyTrends:',
  'smartInsights:',
  'deposits:',
  'withdrawals:',
  'excellentProgress:',
  'goalProgressMessage:',
  'goalAlert:',
  'emergencyFundRecommendation:',
  'buildEmergencyFund:',
  'earningInterest:',
  'interestEarnedMessage:',
  'loadingAnalytics:',
  'noAnalyticsAvailable:',
  'createSavingsAccount:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 9: Check savingsAnalytics section exists in en.js
console.log('\n✅ Test 9: SavingsAnalytics section in en.js');
const savingsAnalyticsSection = enTranslationsContent.includes('savingsAnalytics: {');
console.log(`   SavingsAnalytics section exists: ${savingsAnalyticsSection ? '✅ PASS' : '❌ FAIL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 9;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (hardcodedTextFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (insightKeysFound === insightKeys.length) passedTests++;
if (interpolationFound === interpolationPatterns.length) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (savingsAnalyticsSection) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 SavingsAnalyticsScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
  console.log('   Savings insights, goal tracking, and performance metrics are internationalized!');
} else {
  console.log('\n⚠️  SavingsAnalyticsScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test savings analytics with different languages');
console.log('2. Verify goal tracking displays correctly');
console.log('3. Test dynamic value interpolation (amounts, percentages)');
console.log('4. Proceed to InvestmentAnalyticsScreen.js implementation');
