/**
 * Language Loader with Performance Optimization
 * 
 * Implements lazy loading, caching, and performance optimization
 * for JiraniPay multi-language support
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

class LanguageLoader {
  constructor() {
    this.cache = new Map();
    this.loadingPromises = new Map();
    this.preloadedLanguages = new Set(['en']); // English is always preloaded
  }

  /**
   * Lazy load translation file
   * @param {string} languageCode - Language code (en, sw, fr, ar)
   * @returns {Promise<Object>} Translation object
   */
  async loadLanguage(languageCode) {
    // Return from cache if already loaded
    if (this.cache.has(languageCode)) {
      return this.cache.get(languageCode);
    }

    // Return existing loading promise if already loading
    if (this.loadingPromises.has(languageCode)) {
      return this.loadingPromises.get(languageCode);
    }

    // Start loading process
    const loadingPromise = this._loadLanguageFile(languageCode);
    this.loadingPromises.set(languageCode, loadingPromise);

    try {
      const translations = await loadingPromise;
      this.cache.set(languageCode, translations);
      this.loadingPromises.delete(languageCode);
      return translations;
    } catch (error) {
      this.loadingPromises.delete(languageCode);
      throw error;
    }
  }

  /**
   * Internal method to load language file
   * @param {string} languageCode 
   * @returns {Promise<Object>}
   */
  async _loadLanguageFile(languageCode) {
    try {
      // Check AsyncStorage cache first
      const cachedTranslations = await this._getCachedTranslations(languageCode);
      if (cachedTranslations) {
        return cachedTranslations;
      }

      // Dynamic import based on language code
      let translations;
      switch (languageCode) {
        case 'en':
          translations = (await import('../locales/en.js')).default;
          break;
        case 'sw':
          translations = (await import('../locales/sw.js')).default;
          break;
        case 'fr':
          translations = (await import('../locales/fr.js')).default;
          break;
        case 'ar':
          translations = (await import('../locales/ar.js')).default;
          break;
        default:
          throw new Error(`Unsupported language: ${languageCode}`);
      }

      // Cache in AsyncStorage for faster subsequent loads
      await this._cacheTranslations(languageCode, translations);
      
      return translations;
    } catch (error) {
      console.error(`Failed to load language ${languageCode}:`, error);
      
      // Fallback to English if available
      if (languageCode !== 'en' && this.cache.has('en')) {
        console.warn(`Falling back to English for ${languageCode}`);
        return this.cache.get('en');
      }
      
      throw error;
    }
  }

  /**
   * Get cached translations from AsyncStorage
   * @param {string} languageCode 
   * @returns {Promise<Object|null>}
   */
  async _getCachedTranslations(languageCode) {
    try {
      const cacheKey = `translations_${languageCode}`;
      const cached = await AsyncStorage.getItem(cacheKey);
      
      if (cached) {
        const { translations, timestamp } = JSON.parse(cached);
        
        // Check if cache is still valid (24 hours)
        const cacheAge = Date.now() - timestamp;
        const maxCacheAge = 24 * 60 * 60 * 1000; // 24 hours
        
        if (cacheAge < maxCacheAge) {
          return translations;
        } else {
          // Remove expired cache
          await AsyncStorage.removeItem(cacheKey);
        }
      }
    } catch (error) {
      console.warn(`Failed to get cached translations for ${languageCode}:`, error);
    }
    
    return null;
  }

  /**
   * Cache translations in AsyncStorage
   * @param {string} languageCode 
   * @param {Object} translations 
   */
  async _cacheTranslations(languageCode, translations) {
    try {
      const cacheKey = `translations_${languageCode}`;
      const cacheData = {
        translations,
        timestamp: Date.now()
      };
      
      await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      console.warn(`Failed to cache translations for ${languageCode}:`, error);
    }
  }

  /**
   * Preload multiple languages for better performance
   * @param {string[]} languageCodes 
   */
  async preloadLanguages(languageCodes) {
    const preloadPromises = languageCodes.map(async (code) => {
      try {
        await this.loadLanguage(code);
        this.preloadedLanguages.add(code);
        console.log(`Preloaded language: ${code}`);
      } catch (error) {
        console.warn(`Failed to preload language ${code}:`, error);
      }
    });

    await Promise.allSettled(preloadPromises);
  }

  /**
   * Clear language cache
   * @param {string} languageCode - Optional, clears all if not specified
   */
  async clearCache(languageCode = null) {
    if (languageCode) {
      this.cache.delete(languageCode);
      await AsyncStorage.removeItem(`translations_${languageCode}`);
    } else {
      this.cache.clear();
      const keys = await AsyncStorage.getAllKeys();
      const translationKeys = keys.filter(key => key.startsWith('translations_'));
      await AsyncStorage.multiRemove(translationKeys);
    }
  }

  /**
   * Get cache statistics
   * @returns {Object}
   */
  getCacheStats() {
    return {
      cachedLanguages: Array.from(this.cache.keys()),
      preloadedLanguages: Array.from(this.preloadedLanguages),
      cacheSize: this.cache.size,
      loadingInProgress: Array.from(this.loadingPromises.keys())
    };
  }

  /**
   * Check if language is supported
   * @param {string} languageCode 
   * @returns {boolean}
   */
  isLanguageSupported(languageCode) {
    return ['en', 'sw', 'fr', 'ar'].includes(languageCode);
  }

  /**
   * Get available languages with metadata
   * @returns {Array}
   */
  getAvailableLanguages() {
    return [
      {
        code: 'en',
        name: 'English',
        nativeName: 'English',
        rtl: false,
        region: 'Global',
        countries: ['Uganda', 'Kenya', 'Tanzania', 'Rwanda', 'Burundi']
      },
      {
        code: 'sw',
        name: 'Swahili',
        nativeName: 'Kiswahili',
        rtl: false,
        region: 'East Africa',
        countries: ['Tanzania', 'Kenya', 'Uganda']
      },
      {
        code: 'fr',
        name: 'French',
        nativeName: 'Français',
        rtl: false,
        region: 'Central/East Africa',
        countries: ['Rwanda', 'Burundi', 'DRC']
      },
      {
        code: 'ar',
        name: 'Arabic',
        nativeName: 'العربية',
        rtl: true,
        region: 'North/East Africa',
        countries: ['Sudan', 'Somalia']
      }
    ];
  }

  /**
   * Optimize bundle size by removing unused translations
   * @param {string[]} usedKeys - Array of translation keys actually used
   * @param {string} languageCode 
   */
  optimizeTranslations(usedKeys, languageCode) {
    const translations = this.cache.get(languageCode);
    if (!translations) return;

    // Create optimized translation object with only used keys
    const optimized = {};
    
    usedKeys.forEach(key => {
      const keyPath = key.split('.');
      let source = translations;
      let target = optimized;
      
      for (let i = 0; i < keyPath.length - 1; i++) {
        const segment = keyPath[i];
        if (!target[segment]) target[segment] = {};
        source = source[segment];
        target = target[segment];
      }
      
      const finalKey = keyPath[keyPath.length - 1];
      if (source && source[finalKey]) {
        target[finalKey] = source[finalKey];
      }
    });

    // Update cache with optimized version
    this.cache.set(`${languageCode}_optimized`, optimized);
    
    return optimized;
  }

  /**
   * Measure translation lookup performance
   * @param {string} languageCode 
   * @param {string} key 
   * @returns {Object}
   */
  measureLookupPerformance(languageCode, key) {
    const startTime = performance.now();
    
    const translations = this.cache.get(languageCode);
    if (!translations) {
      return { found: false, time: performance.now() - startTime };
    }

    const keyPath = key.split('.');
    let current = translations;
    
    for (const segment of keyPath) {
      if (current && typeof current === 'object' && segment in current) {
        current = current[segment];
      } else {
        return { found: false, time: performance.now() - startTime };
      }
    }

    return { 
      found: true, 
      value: current, 
      time: performance.now() - startTime 
    };
  }
}

// Export singleton instance
export default new LanguageLoader();
