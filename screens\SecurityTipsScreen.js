import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SecurityTipsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);
  const [expandedTip, setExpandedTip] = useState(null);

  const securityTips = [
    {
      id: '1',
      category: t('securityTips.authentication'),
      icon: 'finger-print',
      color: Colors.primary.main,
      title: t('securityTips.useStrongAuthentication'),
      summary: t('securityTips.enableBiometricAndStrongPIN'),
      details: [
        'Enable Face ID or fingerprint authentication when available',
        'Use a 6-digit PIN that\'s not easily guessable (avoid 123456, 000000)',
        'Don\'t use your birthday or phone number as your PIN',
        'Enable two-factor authentication for extra security',
        'Change your PIN regularly, especially if you suspect it\'s been compromised'
      ]
    },
    {
      id: '2',
      category: t('securityTips.deviceSecurity'),
      icon: 'phone-portrait',
      color: Colors.status.success,
      title: t('securityTips.secureYourDevice'),
      summary: t('securityTips.keepDeviceLockedAndUpdated'),
      details: [
        'Always lock your phone with a PIN, password, or biometric',
        'Keep your device\'s operating system updated',
        'Don\'t leave your phone unattended in public places',
        'Use automatic screen lock with a short timeout',
        'Install apps only from official app stores'
      ]
    },
    {
      id: '3',
      category: 'Network Safety',
      icon: 'wifi',
      color: Colors.accent.gold,
      title: 'Safe Network Usage',
      summary: 'Be careful when using public Wi-Fi networks',
      details: [
        'Avoid using public Wi-Fi for financial transactions',
        'Use your mobile data instead of public Wi-Fi when possible',
        'If you must use public Wi-Fi, ensure the network is legitimate',
        'Never access JiraniPay on shared or public computers',
        'Log out completely when using any shared device'
      ]
    },
    {
      id: '4',
      category: 'Account Protection',
      icon: 'shield-checkmark',
      color: Colors.secondary.forest,
      title: 'Protect Your Account',
      summary: 'Keep your account information secure and private',
      details: [
        'Never share your PIN, password, or verification codes',
        'Log out of the app when not in use',
        'Monitor your account regularly for suspicious activity',
        'Report any unauthorized transactions immediately',
        'Keep your contact information updated for security alerts'
      ]
    },
    {
      id: '5',
      category: 'Phishing Protection',
      icon: 'mail',
      color: Colors.status.error,
      title: 'Avoid Phishing Scams',
      summary: 'Recognize and avoid fraudulent communications',
      details: [
        'JiraniPay will never ask for your PIN via SMS or email',
        'Always verify the sender of suspicious messages',
        'Don\'t click links in suspicious emails or SMS messages',
        'Type the JiraniPay URL directly into your browser',
        'Report phishing attempts to our support team'
      ]
    },
    {
      id: '6',
      category: 'Transaction Safety',
      icon: 'card',
      color: Colors.primary.main,
      title: 'Safe Transactions',
      summary: 'Best practices for secure money transfers',
      details: [
        'Always verify recipient details before sending money',
        'Double-check transaction amounts before confirming',
        'Use QR codes for accurate recipient information',
        'Keep transaction receipts for your records',
        'Set up transaction limits to minimize potential losses'
      ]
    }
  ];

  const emergencyContacts = [
    {
      title: 'JiraniPay Support',
      number: '+************',
      description: 'For account issues and security concerns'
    },
    {
      title: 'Emergency Hotline',
      number: '+************',
      description: 'For immediate security threats'
    }
  ];

  const toggleTip = (tipId) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setExpandedTip(expandedTip === tipId ? null : tipId);
  };

  const callNumber = (phoneNumber) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Linking.openURL(`tel:${phoneNumber}`);
  };

  const renderSecurityTip = (tip) => {
    const isExpanded = expandedTip === tip.id;
    
    return (
      <TouchableOpacity
        key={tip.id}
        style={[styles.tipCard, isExpanded && styles.expandedTipCard]}
        onPress={() => toggleTip(tip.id)}
        activeOpacity={0.7}
      >
        <View style={styles.tipHeader}>
          <View style={styles.tipLeft}>
            <View style={[styles.tipIcon, { backgroundColor: tip.color + '20' }]}>
              <Ionicons name={tip.icon} size={24} color={tip.color} />
            </View>
            <View style={styles.tipHeaderText}>
              <Text style={styles.tipCategory}>{tip.category}</Text>
              <Text style={styles.tipTitle}>{tip.title}</Text>
              <Text style={styles.tipSummary}>{tip.summary}</Text>
            </View>
          </View>
          <Ionicons 
            name={isExpanded ? 'chevron-up' : 'chevron-down'} 
            size={20} 
            color={Colors.neutral.warmGray} 
          />
        </View>
        
        {isExpanded && (
          <View style={styles.tipDetails}>
            {tip.details.map((detail, index) => (
              <View key={index} style={styles.tipDetailItem}>
                <View style={styles.bulletPoint} />
                <Text style={styles.tipDetailText}>{detail}</Text>
              </View>
            ))}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderEmergencyContact = (contact, index) => (
    <TouchableOpacity
      key={index}
      style={styles.contactCard}
      onPress={() => callNumber(contact.number)}
      activeOpacity={0.7}
    >
      <View style={styles.contactLeft}>
        <View style={styles.contactIcon}>
          <Ionicons name="call" size={24} color={Colors.status.error} />
        </View>
        <View style={styles.contactInfo}>
          <Text style={styles.contactTitle}>{contact.title}</Text>
          <Text style={styles.contactNumber}>{contact.number}</Text>
          <Text style={styles.contactDescription}>{contact.description}</Text>
        </View>
      </View>
      <Ionicons name="call" size={20} color={Colors.status.error} />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>{t('securityTips')}</Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={styles.headerSubtitle}>
          {t('learnHowToKeepYourAccountSecure')}
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Security Score */}
        <View style={styles.section}>
          <View style={styles.scoreCard}>
            <View style={styles.scoreIcon}>
              <Ionicons name="shield-checkmark" size={32} color={Colors.status.success} />
            </View>
            <View style={styles.scoreText}>
              <Text style={styles.scoreTitle}>{t('securityScore')}</Text>
              <Text style={styles.scoreValue}>{t('85100')}</Text>
              <Text style={styles.scoreDescription}>
                {t('goodSecurityFollowTheseTipsToImproveYourScore')}
              </Text>
            </View>
          </View>
        </View>

        {/* Security Tips */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('securityBestPractices')}</Text>
          <Text style={styles.sectionDescription}>
            {t('tapAnyTipToLearnMoreAboutKeepingYourAccountSecure')}
          </Text>

          {securityTips.map(renderSecurityTip)}
        </View>

        {/* Emergency Contacts */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('emergencyContacts')}</Text>
          <Text style={styles.sectionDescription}>
            {t('contactUsImmediatelyIfYouSuspectAnySecurityIssues')}
          </Text>

          {emergencyContacts.map(renderEmergencyContact)}
        </View>

        {/* Additional Resources */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('additionalResources')}</Text>
          
          <TouchableOpacity
            style={styles.resourceCard}
            activeOpacity={0.7}
            onPress={() => navigation.navigate('SecurityPolicy')}
          >
            <Ionicons name="document-text" size={24} color={Colors.primary.main} />
            <View style={styles.resourceText}>
              <Text style={styles.resourceTitle}>{t('securityPolicy')}</Text>
              <Text style={styles.resourceDescription}>
                {t('readOurCompleteSecurityAndPrivacyPolicy')}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.resourceCard}
            activeOpacity={0.7}
            onPress={() => navigation.navigate('SecurityFAQ')}
          >
            <Ionicons name="help-circle" size={24} color={Colors.accent.gold} />
            <View style={styles.resourceText}>
              <Text style={styles.resourceTitle}>{t('securityFaq')}</Text>
              <Text style={styles.resourceDescription}>
                {t('frequentlyAskedQuestionsAboutAccountSecurity')}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.resourceCard}
            activeOpacity={0.7}
            onPress={() => navigation.navigate('ContactSupport')}
          >
            <Ionicons name="chatbubbles" size={24} color={Colors.status.success} />
            <View style={styles.resourceText}>
              <Text style={styles.resourceTitle}>{t('support.contactSupport')}</Text>
              <Text style={styles.resourceDescription}>
                {t('phone************EmailCustomercarejiranipaycom')}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  placeholder: {
    width: 40,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
    marginTop: -15,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  scoreCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 20,
    borderRadius: 16,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  scoreIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.status.success + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  scoreText: {
    flex: 1,
  },
  scoreTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  scoreValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.status.success,
    marginBottom: 4,
  },
  scoreDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 15,
    lineHeight: 20,
  },
  tipCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  expandedTipCard: {
    borderWidth: 1,
    borderColor: Colors.primary.main + '30',
  },
  tipHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  tipLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  tipIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  tipHeaderText: {
    flex: 1,
  },
  tipCategory: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.primary.main,
    marginBottom: 2,
    textTransform: 'uppercase',
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  tipSummary: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  tipDetails: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.lightGray,
  },
  tipDetailItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.primary.main,
    marginTop: 6,
    marginRight: 12,
  },
  tipDetailText: {
    flex: 1,
    fontSize: 14,
    color: Colors.neutral.charcoal,
    lineHeight: 20,
  },
  contactCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  contactLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  contactIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.status.error + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contactInfo: {
    flex: 1,
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  contactNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.status.error,
    marginBottom: 4,
  },
  contactDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  resourceCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  resourceText: {
    flex: 1,
    marginLeft: 12,
  },
  resourceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  resourceDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
  },
});

export default SecurityTipsScreen;
