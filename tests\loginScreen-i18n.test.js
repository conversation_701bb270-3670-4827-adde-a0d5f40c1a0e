/**
 * LoginScreen i18n Implementation Test
 * 
 * Tests to verify that LoginScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing LoginScreen.js i18n Implementation\n');

// Read the LoginScreen.js file
const loginScreenPath = path.join(__dirname, '../screens/LoginScreen.js');
const loginScreenContent = fs.readFileSync(loginScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = loginScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = loginScreenContent.includes('const { t,') || loginScreenContent.includes('const { t }');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded strings that should have been replaced
console.log('\n✅ Test 3: Hardcoded strings replacement');
const hardcodedStrings = [
  'Enter the 6-digit code sent to',
  'Try Again',
  'Resend OTP',
  'Go Back',
  'Network error. Please check your connection and try again.'
];

let hardcodedFound = [];
hardcodedStrings.forEach(str => {
  // Check if the string appears in user-facing code (not in comments or console logs)
  const regex = new RegExp(`(?<!//.*|console\\..*|/\\*[\\s\\S]*?)${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(?![\\s\\S]*?\\*/)`, 'g');
  const matches = loginScreenContent.match(regex);
  if (matches && matches.length > 0) {
    // Additional check: exclude if it's in a comment line
    const lines = loginScreenContent.split('\n');
    let foundInUserCode = false;
    lines.forEach(line => {
      if (line.includes(str) && !line.trim().startsWith('//') && !line.trim().startsWith('*') && !line.includes('console.')) {
        foundInUserCode = true;
      }
    });
    if (foundInUserCode) {
      hardcodedFound.push(str);
    }
  }
});

if (hardcodedFound.length === 0) {
  console.log('   All hardcoded strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded strings still found: ❌ FAIL');
  hardcodedFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 4: Check for proper translation key usage
console.log('\n✅ Test 4: Translation key usage');
const translationKeys = [
  't(\'auth.otpSentTo\'',
  't(\'auth.tryAgain\')',
  't(\'auth.resendOTP\')',
  't(\'auth.goBack\')',
  't(\'auth.networkError\')'
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (loginScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 5: Check that required translation keys exist in en.js
console.log('\n✅ Test 5: Translation keys in en.js');
const requiredKeys = [
  'otpSentTo:',
  'tryAgain:',
  'resendOTP:',
  'goBack:',
  'networkError:',
  'otpExpired:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check for Alert.alert with proper translation keys
console.log('\n✅ Test 6: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = loginScreenContent.match(alertPattern);
const totalAlerts = (loginScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for string interpolation usage
console.log('\n✅ Test 7: String interpolation');
const hasStringInterpolation = loginScreenContent.includes('t(\'auth.otpSentTo\', { phoneNumber:');
console.log(`   String interpolation used: ${hasStringInterpolation ? '✅ PASS' : '❌ FAIL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 7;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (hardcodedFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (hasStringInterpolation) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 LoginScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
} else {
  console.log('\n⚠️  LoginScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test language switching functionality');
console.log('2. Verify OTP flow with different languages');
console.log('3. Test error messages in multiple languages');
console.log('4. Proceed to RegisterScreen.js implementation');
