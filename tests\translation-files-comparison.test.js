/**
 * Comprehensive Translation Files Comparison
 * 
 * Compares English and Swahili translation files to identify:
 * 1. Structural differences
 * 2. Missing keys in either file
 * 3. Key naming inconsistencies
 * 4. Root-level vs nested key mismatches
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 COMPREHENSIVE TRANSLATION FILES COMPARISON\n');

// Load both translation files
const enPath = path.join(__dirname, '../locales/en.js');
const swPath = path.join(__dirname, '../locales/sw.js');

let enTranslations, swTranslations;

try {
  delete require.cache[require.resolve('../locales/en.js')];
  delete require.cache[require.resolve('../locales/sw.js')];
  
  enTranslations = require('../locales/en.js').default;
  swTranslations = require('../locales/sw.js').default;
  
  console.log('✅ Both translation files loaded successfully');
} catch (error) {
  console.log('❌ Error loading translation files:', error.message);
  process.exit(1);
}

// Helper function to get all keys from an object (flattened)
const getAllKeys = (obj, prefix = '') => {
  const keys = [];
  
  Object.keys(obj).forEach(key => {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys.push(fullKey); // Add the section key itself
      keys.push(...getAllKeys(obj[key], fullKey)); // Add nested keys
    } else {
      keys.push(fullKey);
    }
  });
  
  return keys;
};

// Helper function to get root-level keys only
const getRootKeys = (obj) => {
  return Object.keys(obj).filter(key => typeof obj[key] === 'string');
};

// Helper function to get section keys only
const getSectionKeys = (obj) => {
  return Object.keys(obj).filter(key => typeof obj[key] === 'object' && obj[key] !== null);
};

console.log('📊 BASIC STATISTICS:');
console.log('===================');

const enAllKeys = getAllKeys(enTranslations);
const swAllKeys = getAllKeys(swTranslations);
const enRootKeys = getRootKeys(enTranslations);
const swRootKeys = getRootKeys(swTranslations);
const enSectionKeys = getSectionKeys(enTranslations);
const swSectionKeys = getSectionKeys(swTranslations);

console.log(`English total keys: ${enAllKeys.length}`);
console.log(`Swahili total keys: ${swAllKeys.length}`);
console.log(`English root-level keys: ${enRootKeys.length}`);
console.log(`Swahili root-level keys: ${swRootKeys.length}`);
console.log(`English sections: ${enSectionKeys.length}`);
console.log(`Swahili sections: ${swSectionKeys.length}`);

console.log('\n🔍 STRUCTURAL ANALYSIS:');
console.log('=======================');

// Check for root-level key mismatches
const enRootSet = new Set(enRootKeys);
const swRootSet = new Set(swRootKeys);

const rootOnlyInSwahili = swRootKeys.filter(key => !enRootSet.has(key));
const rootOnlyInEnglish = enRootKeys.filter(key => !swRootSet.has(key));

console.log(`\n📋 ROOT-LEVEL KEYS COMPARISON:`);
if (rootOnlyInSwahili.length > 0) {
  console.log(`❌ Keys only in Swahili (${rootOnlyInSwahili.length}):`);
  rootOnlyInSwahili.slice(0, 20).forEach(key => {
    console.log(`   - ${key}: "${swTranslations[key]}"`);
  });
  if (rootOnlyInSwahili.length > 20) {
    console.log(`   ... and ${rootOnlyInSwahili.length - 20} more`);
  }
} else {
  console.log('✅ No root keys only in Swahili');
}

if (rootOnlyInEnglish.length > 0) {
  console.log(`❌ Keys only in English (${rootOnlyInEnglish.length}):`);
  rootOnlyInEnglish.slice(0, 20).forEach(key => {
    console.log(`   - ${key}: "${enTranslations[key]}"`);
  });
  if (rootOnlyInEnglish.length > 20) {
    console.log(`   ... and ${rootOnlyInEnglish.length - 20} more`);
  }
} else {
  console.log('✅ No root keys only in English');
}

// Check for section mismatches
const enSectionSet = new Set(enSectionKeys);
const swSectionSet = new Set(swSectionKeys);

const sectionsOnlyInSwahili = swSectionKeys.filter(key => !enSectionSet.has(key));
const sectionsOnlyInEnglish = enSectionKeys.filter(key => !swSectionSet.has(key));

console.log(`\n📂 SECTIONS COMPARISON:`);
if (sectionsOnlyInSwahili.length > 0) {
  console.log(`❌ Sections only in Swahili (${sectionsOnlyInSwahili.length}):`);
  sectionsOnlyInSwahili.forEach(section => {
    console.log(`   - ${section} (${Object.keys(swTranslations[section]).length} keys)`);
  });
} else {
  console.log('✅ No sections only in Swahili');
}

if (sectionsOnlyInEnglish.length > 0) {
  console.log(`❌ Sections only in English (${sectionsOnlyInEnglish.length}):`);
  sectionsOnlyInEnglish.forEach(section => {
    console.log(`   - ${section} (${Object.keys(enTranslations[section]).length} keys)`);
  });
} else {
  console.log('✅ No sections only in English');
}

// Check for missing keys in common sections
console.log(`\n🔑 MISSING KEYS IN COMMON SECTIONS:`);
const commonSections = enSectionKeys.filter(section => swSectionSet.has(section));

let totalMissingInSwahili = 0;
let totalMissingInEnglish = 0;

commonSections.forEach(section => {
  const enSectionKeys = Object.keys(enTranslations[section]);
  const swSectionKeys = Object.keys(swTranslations[section]);
  
  const missingInSwahili = enSectionKeys.filter(key => !swSectionKeys.includes(key));
  const missingInEnglish = swSectionKeys.filter(key => !enSectionKeys.includes(key));
  
  if (missingInSwahili.length > 0 || missingInEnglish.length > 0) {
    console.log(`\n📁 Section: ${section}`);
    
    if (missingInSwahili.length > 0) {
      console.log(`   ❌ Missing in Swahili (${missingInSwahili.length}):`);
      missingInSwahili.slice(0, 10).forEach(key => {
        console.log(`      - ${key}`);
      });
      if (missingInSwahili.length > 10) {
        console.log(`      ... and ${missingInSwahili.length - 10} more`);
      }
      totalMissingInSwahili += missingInSwahili.length;
    }
    
    if (missingInEnglish.length > 0) {
      console.log(`   ❌ Missing in English (${missingInEnglish.length}):`);
      missingInEnglish.slice(0, 10).forEach(key => {
        console.log(`      - ${key}`);
      });
      if (missingInEnglish.length > 10) {
        console.log(`      ... and ${missingInEnglish.length - 10} more`);
      }
      totalMissingInEnglish += missingInEnglish.length;
    }
  }
});

console.log('\n📊 CRITICAL ISSUES SUMMARY:');
console.log('===========================');
console.log(`Root keys only in Swahili: ${rootOnlyInSwahili.length}`);
console.log(`Root keys only in English: ${rootOnlyInEnglish.length}`);
console.log(`Sections only in Swahili: ${sectionsOnlyInSwahili.length}`);
console.log(`Sections only in English: ${sectionsOnlyInEnglish.length}`);
console.log(`Keys missing in Swahili: ${totalMissingInSwahili}`);
console.log(`Keys missing in English: ${totalMissingInEnglish}`);

const totalIssues = rootOnlyInSwahili.length + rootOnlyInEnglish.length + 
                   sectionsOnlyInSwahili.length + sectionsOnlyInEnglish.length +
                   totalMissingInSwahili + totalMissingInEnglish;

console.log(`Total structural issues: ${totalIssues}`);

console.log('\n🎯 DIAGNOSIS:');
console.log('=============');

if (totalIssues === 0) {
  console.log('✅ TRANSLATION FILES: PERFECTLY SYNCHRONIZED');
  console.log('   Both files have identical structure and keys');
  console.log('   Issue must be in app code or i18n configuration');
} else {
  console.log('❌ TRANSLATION FILES: STRUCTURAL MISMATCHES FOUND');
  console.log('   Files have different structures - this will cause translation failures');
  
  if (rootOnlyInSwahili.length > 0) {
    console.log('   🚨 CRITICAL: Swahili has root-level keys that English lacks');
    console.log('   This will cause fallback failures when switching languages');
  }
  
  if (totalMissingInSwahili > 0 || totalMissingInEnglish > 0) {
    console.log('   ⚠️  WARNING: Missing keys in common sections');
    console.log('   Some translations will fall back to key names');
  }
}

console.log('\n🛠️  RECOMMENDED FIXES:');
console.log('======================');

if (rootOnlyInSwahili.length > 0) {
  console.log('1. Add missing root-level keys to English file');
  console.log('2. OR move root-level keys in Swahili to appropriate sections');
}

if (totalMissingInSwahili > 0) {
  console.log('3. Add missing Swahili translations for English keys');
}

if (totalMissingInEnglish > 0) {
  console.log('4. Add missing English translations for Swahili keys');
}

if (totalIssues > 0) {
  console.log('5. Ensure both files have identical structure before testing');
}

console.log('\n🔍 NEXT STEPS:');
console.log('==============');
console.log('1. Fix structural mismatches identified above');
console.log('2. Verify both files can be imported without errors');
console.log('3. Test translation system with identical file structures');
console.log('4. If issues persist, investigate app code and i18n configuration');
