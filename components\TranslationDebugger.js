import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useLanguage } from '../contexts/LanguageContext';

/**
 * Translation Debugger Component
 * 
 * Add this component to any screen to debug translation issues
 * Shows the actual values returned by the t() function
 */
const TranslationDebugger = () => {
  const { t, currentLanguage } = useLanguage();

  const testKeys = [
    'editProfile',
    'fullName', 
    'emailAddress',
    'phoneNumber',
    'accountVerification',
    'privacyAndData'
  ];

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔍 Translation Debug ({currentLanguage})</Text>
      {testKeys.map(key => {
        const result = t(key);
        const isWorking = result !== key;
        
        return (
          <View key={key} style={styles.row}>
            <Text style={styles.key}>{key}:</Text>
            <Text style={[styles.result, isWorking ? styles.success : styles.error]}>
              "{result}" {isWorking ? '✅' : '❌'}
            </Text>
          </View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    margin: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  row: {
    flexDirection: 'row',
    marginBottom: 5,
    alignItems: 'center',
  },
  key: {
    fontSize: 12,
    fontWeight: 'bold',
    width: 120,
    color: '#333',
  },
  result: {
    fontSize: 12,
    flex: 1,
  },
  success: {
    color: 'green',
  },
  error: {
    color: 'red',
  },
});

export default TranslationDebugger;
