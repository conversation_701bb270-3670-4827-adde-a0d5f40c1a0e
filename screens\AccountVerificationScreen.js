import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import authService from '../services/authService';
import profileManagementService from '../services/profileManagementService';

const AccountVerificationScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [user, setUser] = useState(null);
  const [verificationLevel, setVerificationLevel] = useState(null);
  const [completionStatus, setCompletionStatus] = useState(null);

  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  useEffect(() => {
    loadVerificationData();
  }, []);

  const loadVerificationData = async () => {
    try {
      setLoading(true);
      
      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        Alert.alert(t('error'), t('pleaseLogInToContinue'));
        navigation.goBack();
        return;
      }
      
      setUser(currentUser);

      // Get current verification level
      const levelResult = await profileManagementService.getKYCVerificationLevel(currentUser.id);
      if (levelResult.success) {
        setVerificationLevel(levelResult.data);
      }

      // Get profile completion status
      const statusResult = await profileManagementService.getProfileCompletionStatus(currentUser.id);
      if (statusResult.success) {
        setCompletionStatus(statusResult.data);
      }

    } catch (error) {
      console.error('❌ Error loading verification data:', error);
      Alert.alert(t('error'), t('failedToLoadVerificationData'));
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadVerificationData();
    setRefreshing(false);
  };

  const getVerificationLevelInfo = (level) => {
    const levels = {
      basic: {
        title: t('verification.basicAccount'),
        color: Colors.status.warning,
        icon: 'person-outline',
        description: t('verification.basicAccountDescription')
      },
      standard: {
        title: t('verification.standardAccount'),
        color: Colors.primary.main,
        icon: 'shield-outline',
        description: t('verification.standardAccountDescription')
      },
      premium: {
        title: t('verification.premiumAccount'),
        color: Colors.status.success,
        icon: 'diamond-outline',
        description: t('verification.premiumAccountDescription')
      }
    };
    return levels[level] || levels.basic;
  };

  const formatCurrency = (amount) => {
    return `UGX ${amount?.toLocaleString() || '0'}`;
  };

  const renderVerificationLevel = () => {
    if (!verificationLevel) return null;

    const levelInfo = getVerificationLevelInfo(verificationLevel.verification_level);

    return (
      <View style={styles.levelCard}>
        <View style={styles.levelHeader}>
          <View style={[styles.levelIcon, { backgroundColor: levelInfo.color }]}>
            <Ionicons name={levelInfo.icon} size={24} color={Colors.neutral.white} />
          </View>
          <View style={styles.levelInfo}>
            <Text style={styles.levelTitle}>{levelInfo.title}</Text>
            <Text style={styles.levelDescription}>{levelInfo.description}</Text>
          </View>
        </View>

        <View style={styles.limitsContainer}>
          <Text style={styles.limitsTitle}>{t('currentLimits')}</Text>
          <View style={styles.limitRow}>
            <Text style={styles.limitLabel}>{t('dailyLimit')}</Text>
            <Text style={styles.limitValue}>{formatCurrency(verificationLevel.daily_limit)}</Text>
          </View>
          <View style={styles.limitRow}>
            <Text style={styles.limitLabel}>{t('monthlyLimit')}</Text>
            <Text style={styles.limitValue}>{formatCurrency(verificationLevel.monthly_limit)}</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderVerificationSteps = () => {
    const steps = [
      {
        id: 'basic_info',
        title: t('verification.basicInformation'),
        description: t('verification.basicInformationDescription'),
        icon: 'person-outline',
        completed: completionStatus?.completedSteps?.some(step => step.step_name === 'basic_info'),
        action: () => navigation.navigate('EditProfile')
      },
      {
        id: 'phone_verification',
        title: t('verification.phoneVerification'),
        description: t('verification.phoneVerificationDescription'),
        icon: 'call-outline',
        completed: completionStatus?.completedSteps?.some(step => step.step_name === 'phone_verification'),
        action: () => Alert.alert(t('info'), t('phoneVerificationIsCompletedDuringRegistration'))
      },
      {
        id: 'email_verification',
        title: t('verification.emailVerification'),
        description: t('verification.emailVerificationDescription'),
        icon: 'mail-outline',
        completed: completionStatus?.completedSteps?.some(step => step.step_name === 'email_verification'),
        action: () => navigation.navigate('EmailVerification')
      },
      {
        id: 'kyc_basic',
        title: t('verification.identityVerification'),
        description: t('verification.identityVerificationDescription'),
        icon: 'card-outline',
        completed: completionStatus?.completedSteps?.some(step => step.step_name === 'kyc_basic'),
        action: () => navigation.navigate('DocumentUpload', { documentType: 'national_id' })
      },
      {
        id: 'kyc_standard',
        title: t('verification.addressVerification'),
        description: t('verification.addressVerificationDescription'),
        icon: 'home-outline',
        completed: completionStatus?.completedSteps?.some(step => step.step_name === 'kyc_standard'),
        action: () => navigation.navigate('DocumentUpload', { documentType: 'utility_bill' })
      }
    ];

    return (
      <View style={styles.stepsContainer}>
        <Text style={styles.sectionTitle}>{t('verificationSteps')}</Text>
        {steps.map((step, index) => (
          <TouchableOpacity
            key={step.id}
            style={styles.stepItem}
            onPress={step.action}
            activeOpacity={0.7}
          >
            <View style={styles.stepLeft}>
              <View style={[
                styles.stepIcon,
                { backgroundColor: step.completed ? Colors.status.success : Colors.neutral.lightGray }
              ]}>
                <Ionicons 
                  name={step.completed ? 'checkmark' : step.icon} 
                  size={20} 
                  color={step.completed ? Colors.neutral.white : Colors.neutral.warmGray} 
                />
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>{step.title}</Text>
                <Text style={styles.stepDescription}>{step.description}</Text>
              </View>
            </View>
            <View style={styles.stepRight}>
              {step.completed ? (
                <Text style={styles.completedText}>{t('completed')}</Text>
              ) : (
                <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary.main} />
        <Text style={styles.loadingText}>{t('loadingVerificationData')}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton onPress={() => navigation.goBack()} />
        <Text style={styles.headerTitle}>{t('accountVerification')}</Text>
        <TouchableOpacity onPress={() => navigation.navigate('VerificationLimits')}>
          <Ionicons name="information-circle-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderVerificationLevel()}
        {renderVerificationSteps()}

        {/* Quick Actions */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('VerificationStatus')}
            activeOpacity={0.7}
          >
            <Ionicons name="document-text-outline" size={20} color={Colors.primary.main} />
            <Text style={styles.actionButtonText}>{t('viewStatus')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('VerificationLimits')}
            activeOpacity={0.7}
          >
            <Ionicons name="bar-chart-outline" size={20} color={Colors.primary.main} />
            <Text style={styles.actionButtonText}>{t('viewLimits')}</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const createStyles = (theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  levelCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  levelHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  levelIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  levelInfo: {
    flex: 1,
  },
  levelTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  levelDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  limitsContainer: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: 16,
  },
  limitsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  limitRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  limitLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  limitValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  stepsContainer: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  stepLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  stepIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  stepRight: {
    alignItems: 'center',
  },
  completedText: {
    fontSize: 12,
    color: Colors.status.success,
    fontWeight: '600',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 6,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.primary.main,
    marginLeft: 8,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default AccountVerificationScreen;
