/**
 * BillConfirmationScreen i18n Implementation Test
 * 
 * Tests to verify that BillConfirmationScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing BillConfirmationScreen.js i18n Implementation\n');

// Read the BillConfirmationScreen.js file
const billConfirmationScreenPath = path.join(__dirname, '../screens/BillConfirmationScreen.js');
const billConfirmationScreenContent = fs.readFileSync(billConfirmationScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = billConfirmationScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = billConfirmationScreenContent.includes('const { t }') || billConfirmationScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded Alert.alert strings replacement
console.log('\n✅ Test 3: Hardcoded Alert.alert strings replacement');
const hardcodedAlertStrings = [
  'Payment Failed',
  'Unable to process payment. Please try again.'
];

let hardcodedAlertsFound = [];
hardcodedAlertStrings.forEach(str => {
  // Check if the string appears in Alert.alert calls (not in translation keys)
  const alertPattern = new RegExp(`Alert\\.alert\\([^)]*['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (alertPattern.test(billConfirmationScreenContent)) {
    hardcodedAlertsFound.push(str);
  }
});

if (hardcodedAlertsFound.length === 0) {
  console.log('   All hardcoded Alert.alert strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Alert.alert strings still found: ❌ FAIL');
  hardcodedAlertsFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 4: Check for hardcoded Text component strings
console.log('\n✅ Test 4: Hardcoded Text component strings');
const hardcodedTextStrings = [
  'Pay Bill',
  'Confirm Payment',
  'Please review your payment details',
  'ACCOUNT DETAILS',
  'Provider',
  'Category',
  'Service Type',
  'Account',
  'Customer',
  'Reference',
  'PAYMENT DETAILS',
  'Amount',
  'Service Charge',
  'VAT',
  'Total',
  'Payment will be processed immediately',
  'Your payment is secure and encrypted',
  'Processing...'
];

let hardcodedTextFound = [];
hardcodedTextStrings.forEach(str => {
  // Check if the string appears in Text components (not in translation keys)
  const textPattern = new RegExp(`<Text[^>]*>\\s*${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*</Text>`, 'g');
  const directTextPattern = new RegExp(`['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (textPattern.test(billConfirmationScreenContent) || 
      (directTextPattern.test(billConfirmationScreenContent) && !billConfirmationScreenContent.includes(`t('bills.`))) {
    hardcodedTextFound.push(str);
  }
});

if (hardcodedTextFound.length === 0) {
  console.log('   All hardcoded Text strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Text strings still found: ❌ FAIL');
  hardcodedTextFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 5: Check for proper translation key usage
console.log('\n✅ Test 5: Translation key usage');
const translationKeys = [
  't(\'bills.paymentFailed\')',
  't(\'bills.unableToProcessPayment\')',
  't(\'bills.payBill\')',
  't(\'bills.confirmPayment\')',
  't(\'bills.reviewPaymentDetails\')',
  't(\'bills.accountDetails\')',
  't(\'bills.provider\')',
  't(\'bills.category\')',
  't(\'bills.serviceType\')',
  't(\'bills.account\')',
  't(\'bills.customer\')',
  't(\'bills.reference\')',
  't(\'bills.paymentDetails\')',
  't(\'bills.amount\')',
  't(\'bills.serviceCharge\')',
  't(\'bills.vat\')',
  't(\'bills.total\')',
  't(\'bills.paymentWillBeProcessedImmediately\')',
  't(\'bills.paymentIsSecureAndEncrypted\')',
  't(\'bills.processing\')'
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (billConfirmationScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check that required translation keys exist in en.js
console.log('\n✅ Test 6: Translation keys in en.js');
const requiredKeys = [
  'paymentFailed:',
  'unableToProcessPayment:',
  'payBill:',
  'confirmPayment:',
  'reviewPaymentDetails:',
  'accountDetails:',
  'provider:',
  'category:',
  'serviceType:',
  'account:',
  'customer:',
  'reference:',
  'paymentDetails:',
  'amount:',
  'serviceCharge:',
  'vat:',
  'total:',
  'paymentWillBeProcessedImmediately:',
  'paymentIsSecureAndEncrypted:',
  'processing:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for Alert.alert with proper translation keys
console.log('\n✅ Test 7: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = billConfirmationScreenContent.match(alertPattern);
const totalAlerts = (billConfirmationScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 8: Check for bills-specific translation usage
console.log('\n✅ Test 8: Bills-specific translations');
const billsTranslations = [
  't(\'bills.electricity\')',
  't(\'bills.airtime\')',
  't(\'bills.financial\')',
  't(\'bills.postpaid\')',
  't(\'bills.solar\')',
  't(\'bills.tv\')',
  't(\'bills.governmentServices\')'
];

let billsKeysFound = 0;
billsTranslations.forEach(key => {
  if (billConfirmationScreenContent.includes(key)) {
    billsKeysFound++;
  }
});

console.log(`   Bills translation keys found: ${billsKeysFound}/${billsTranslations.length} ${billsKeysFound >= 0 ? '✅ PASS' : '❌ PARTIAL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 8;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (hardcodedAlertsFound.length === 0) passedTests++;
if (hardcodedTextFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (billsKeysFound >= 0) passedTests++; // This test is more lenient since these keys might not be used in confirmation screen

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 BillConfirmationScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
} else {
  console.log('\n⚠️  BillConfirmationScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test bill confirmation flow with different languages');
console.log('2. Verify payment details display correctly');
console.log('3. Test confirmation button and processing states');
console.log('4. Proceed to Verification and Security Screen Translations');
