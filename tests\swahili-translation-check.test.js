/**
 * Swahili Translation Quality Check
 * 
 * Checks for common translation issues:
 * - Missing translations (English text in Swahili file)
 * - Raw camelCase keys appearing as values
 * - Inconsistent formatting
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Swahili Translation Quality Check\n');

// Load translation files
const enPath = path.join(__dirname, '../locales/en.js');
const swPath = path.join(__dirname, '../locales/sw.js');

const enContent = fs.readFileSync(enPath, 'utf8');
const swContent = fs.readFileSync(swPath, 'utf8');

// Extract all translation keys and values
const extractTranslations = (content) => {
  const translations = {};
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    const match = line.match(/^\s*([a-zA-Z][a-zA-Z0-9]*)\s*:\s*'([^']*)'.*$/);
    if (match) {
      const [, key, value] = match;
      translations[key] = value;
    }
  }
  
  return translations;
};

const enTranslations = extractTranslations(enContent);
const swTranslations = extractTranslations(swContent);

console.log('📊 Translation Statistics:');
console.log(`   English keys: ${Object.keys(enTranslations).length}`);
console.log(`   Swahili keys: ${Object.keys(swTranslations).length}`);

// Check for missing keys
const missingKeys = Object.keys(enTranslations).filter(key => !swTranslations[key]);
const extraKeys = Object.keys(swTranslations).filter(key => !enTranslations[key]);

console.log('\n🔍 Missing Translation Keys:');
if (missingKeys.length > 0) {
  console.log(`   Found ${missingKeys.length} missing keys:`);
  missingKeys.slice(0, 10).forEach(key => {
    console.log(`   - ${key}: "${enTranslations[key]}"`);
  });
  if (missingKeys.length > 10) {
    console.log(`   ... and ${missingKeys.length - 10} more`);
  }
} else {
  console.log('   ✅ No missing keys found');
}

console.log('\n➕ Extra Keys in Swahili:');
if (extraKeys.length > 0) {
  console.log(`   Found ${extraKeys.length} extra keys:`);
  extraKeys.slice(0, 10).forEach(key => {
    console.log(`   + ${key}: "${swTranslations[key]}"`);
  });
  if (extraKeys.length > 10) {
    console.log(`   ... and ${extraKeys.length - 10} more`);
  }
} else {
  console.log('   ✅ No extra keys found');
}

// Check for potential issues in Swahili translations
console.log('\n⚠️  Potential Translation Issues:');

let issueCount = 0;

// Check for camelCase values (might indicate raw keys)
const camelCasePattern = /^[a-z]+[A-Z][a-zA-Z]*$/;
const camelCaseIssues = [];

Object.entries(swTranslations).forEach(([key, value]) => {
  if (camelCasePattern.test(value)) {
    camelCaseIssues.push({ key, value });
  }
});

if (camelCaseIssues.length > 0) {
  console.log(`   🔤 CamelCase values (${camelCaseIssues.length}):`);
  camelCaseIssues.slice(0, 5).forEach(({ key, value }) => {
    console.log(`      ${key}: "${value}" (might be raw key)`);
  });
  issueCount += camelCaseIssues.length;
}

// Check for English words in Swahili translations
const englishWords = ['Account', 'Verification', 'Profile', 'Settings', 'Privacy', 'Data', 'Security', 'Login', 'Password'];
const englishWordIssues = [];

Object.entries(swTranslations).forEach(([key, value]) => {
  englishWords.forEach(word => {
    if (value.includes(word)) {
      englishWordIssues.push({ key, value, word });
    }
  });
});

if (englishWordIssues.length > 0) {
  console.log(`   🔤 English words found (${englishWordIssues.length}):`);
  englishWordIssues.slice(0, 5).forEach(({ key, value, word }) => {
    console.log(`      ${key}: "${value}" (contains "${word}")`);
  });
  issueCount += englishWordIssues.length;
}

// Check for empty or very short translations
const shortTranslations = [];
Object.entries(swTranslations).forEach(([key, value]) => {
  if (value.length < 2) {
    shortTranslations.push({ key, value });
  }
});

if (shortTranslations.length > 0) {
  console.log(`   📏 Very short translations (${shortTranslations.length}):`);
  shortTranslations.slice(0, 5).forEach(({ key, value }) => {
    console.log(`      ${key}: "${value}"`);
  });
  issueCount += shortTranslations.length;
}

// Check for specific problematic patterns
const problematicPatterns = [
  { pattern: /^[A-Z][a-z]+[A-Z]/, name: 'PascalCase' },
  { pattern: /^[a-z]+_[a-z]+/, name: 'snake_case' },
  { pattern: /^\w+\.\w+/, name: 'dot.notation' }
];

problematicPatterns.forEach(({ pattern, name }) => {
  const matches = [];
  Object.entries(swTranslations).forEach(([key, value]) => {
    if (pattern.test(value)) {
      matches.push({ key, value });
    }
  });
  
  if (matches.length > 0) {
    console.log(`   🔍 ${name} patterns (${matches.length}):`);
    matches.slice(0, 3).forEach(({ key, value }) => {
      console.log(`      ${key}: "${value}"`);
    });
    issueCount += matches.length;
  }
});

if (issueCount === 0) {
  console.log('   ✅ No obvious translation issues found');
}

// Check for specific verification-related keys
console.log('\n🔐 Account Verification Keys Check:');
const verificationKeys = [
  'accountVerification',
  'verificationSteps', 
  'phoneVerification',
  'emailVerification',
  'identityVerification',
  'biometricVerification'
];

verificationKeys.forEach(key => {
  if (swTranslations[key]) {
    console.log(`   ✅ ${key}: "${swTranslations[key]}"`);
  } else {
    console.log(`   ❌ ${key}: MISSING`);
  }
});

// Check for profile-related keys
console.log('\n👤 Profile & Privacy Keys Check:');
const profileKeys = [
  'editProfile',
  'privacyAndData',
  'dataConsent',
  'privacyPolicy',
  'deleteAccount',
  'exportData'
];

profileKeys.forEach(key => {
  if (swTranslations[key]) {
    console.log(`   ✅ ${key}: "${swTranslations[key]}"`);
  } else {
    console.log(`   ❌ ${key}: MISSING`);
  }
});

// Summary
console.log('\n📋 SUMMARY:');
console.log('===========');
console.log(`Translation Coverage: ${Math.round((Object.keys(swTranslations).length / Object.keys(enTranslations).length) * 100)}%`);
console.log(`Missing Keys: ${missingKeys.length}`);
console.log(`Potential Issues: ${issueCount}`);

if (missingKeys.length === 0 && issueCount < 5) {
  console.log('\n🎉 SWAHILI TRANSLATIONS: ✅ EXCELLENT QUALITY');
  console.log('   Ready for production use!');
} else if (missingKeys.length < 10 && issueCount < 20) {
  console.log('\n✅ SWAHILI TRANSLATIONS: 🔄 GOOD QUALITY');
  console.log('   Minor issues to address.');
} else {
  console.log('\n⚠️  SWAHILI TRANSLATIONS: 🔄 NEEDS IMPROVEMENT');
  console.log('   Several issues need attention.');
}

console.log('\n🎯 RECOMMENDATIONS:');
console.log('===================');
if (missingKeys.length > 0) {
  console.log('• Add missing translation keys');
}
if (camelCaseIssues.length > 0) {
  console.log('• Fix camelCase values that should be proper translations');
}
if (englishWordIssues.length > 0) {
  console.log('• Replace English words with proper Swahili translations');
}
if (issueCount === 0 && missingKeys.length === 0) {
  console.log('• Translations are ready for production! 🚀');
}
