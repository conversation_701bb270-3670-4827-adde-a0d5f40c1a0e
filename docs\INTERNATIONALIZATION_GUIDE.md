# JiraniPay Internationalization Guide

## Overview

JiraniPay supports four languages optimized for East African markets:
- **English (en)**: Regional lingua franca
- **Swahili (sw)**: Tanzania, Kenya, Uganda
- **French (fr)**: Rwanda, Burundi, DRC
- **Arabic (ar)**: Sudan, Somalia (with RTL support)

## Translation Statistics

| Language | Lines | Sections | Completeness | RTL Support |
|----------|-------|----------|--------------|-------------|
| English  | 2,040 | 36       | 100% (Base)  | No          |
| Swahili  | 1,525 | 34       | 94%          | No          |
| French   | 702   | 23       | 64%          | No          |
| Arabic   | 727   | 24       | 67%          | Yes         |

## Translation Key Structure

### Naming Convention
```javascript
// Pattern: [section].[subsection].[specific]
t('common.loading')                    // ✅ Good
t('analytics.export.exportSuccessful') // ✅ Good
t('support.createTicket.success')      // ✅ Good
t('Loading...')                        // ❌ Bad - hardcoded
```

### Section Organization
```javascript
{
  // Core UI elements used across the app
  common: {
    continue: 'Continue',
    cancel: 'Cancel',
    loading: 'Loading...'
  },
  
  // Feature-specific translations
  analytics: {
    title: 'Analytics',
    export: {
      title: 'Export Analytics',
      exportSuccessful: 'Export successful'
    }
  },
  
  // Component-specific translations
  budget: {
    overBudget: 'Over Budget',
    onTrack: 'On Track'
  }
}
```

## String Interpolation

### Basic Interpolation
```javascript
// Translation file
{
  currencyUpdatedTo: 'Currency updated to {currency}',
  questionsCount: '{count} Questions'
}

// Usage in components
t('settings.currencyUpdatedTo', { currency: 'USD' })
t('securityFAQ.questionsCount', { count: 15 })
```

### Complex Interpolation
```javascript
// Translation file
{
  ticketCreatedMessage: 'Your support ticket {ticketId} has been created.\\n\\nEstimated response time: {responseTime}\\n\\nYou can track your ticket in the Support section.'
}

// Usage
t('createTicket.ticketCreatedMessage', { 
  ticketId: 'TK-12345', 
  responseTime: '2-4 hours' 
})
```

## RTL (Right-to-Left) Support

### Arabic Language Configuration
```javascript
// ar.js
export default {
  // RTL Configuration
  rtl: true,
  direction: 'rtl',
  
  common: {
    continue: 'متابعة',
    cancel: 'إلغاء'
  }
}
```

### RTL-Aware Styling
```javascript
// Component with RTL support
const styles = StyleSheet.create({
  container: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    textAlign: I18nManager.isRTL ? 'right' : 'left'
  }
});
```

## Currency Formatting

### East African Currencies
```javascript
currency: {
  // East African currencies
  ugx: 'Uganda Shilling',      // Uganda
  kes: 'Kenyan Shilling',      // Kenya  
  tzs: 'Tanzanian Shilling',   // Tanzania
  rwf: 'Rwandan Franc',        // Rwanda
  bif: 'Burundian Franc',      // Burundi
  etb: 'Ethiopian Birr',       // Ethiopia
  sdg: 'Sudanese Pound',       // Sudan
  sos: 'Somali Shilling',      // Somalia
  
  // International
  usd: 'US Dollar',
  eur: 'Euro'
}
```

## Component Usage Patterns

### Basic Translation
```javascript
import { useLanguage } from '../contexts/LanguageContext';

const MyComponent = () => {
  const { t } = useLanguage();
  
  return (
    <Text>{t('common.loading')}</Text>
  );
};
```

### ResponsiveText for Length Variations
```javascript
import ResponsiveText from '../components/ResponsiveText';

const MyComponent = () => {
  return (
    <ResponsiveText 
      translationKey="analytics.title"
      maxLength={20}
      adjustsFontSizeToFit={true}
    />
  );
};
```

### Alert.alert Internationalization
```javascript
// ✅ Good - Internationalized
Alert.alert(
  t('common.error'),
  t('settings.failedToLoadSettings'),
  [{ text: t('common.ok') }]
);

// ❌ Bad - Hardcoded
Alert.alert(
  'Error',
  'Failed to load settings',
  [{ text: 'OK' }]
);
```

## Performance Optimization

### Lazy Loading
```javascript
import languageLoader from '../utils/languageLoader';

// Preload languages for better performance
await languageLoader.preloadLanguages(['sw', 'fr']);

// Load language on demand
const translations = await languageLoader.loadLanguage('ar');
```

### Bundle Size Optimization
```javascript
// Only include used translation keys in production
const usedKeys = ['common.loading', 'analytics.title'];
const optimized = languageLoader.optimizeTranslations(usedKeys, 'sw');
```

## Testing Guidelines

### Translation Completeness Test
```bash
node tests/multiLanguage-comprehensive.test.js
```

### Manual Testing Checklist
- [ ] All screens display correctly in each language
- [ ] Text doesn't overflow containers
- [ ] Currency formatting works for all East African currencies
- [ ] Alert messages are translated
- [ ] Form validation messages are localized
- [ ] Arabic text displays right-to-left correctly
- [ ] String interpolation works with dynamic values

## Cultural Considerations

### Swahili (Tanzania, Kenya, Uganda)
- Use formal register for financial terms
- Prefer Swahili financial terminology over English loanwords
- Consider regional variations (Tanzanian vs Kenyan Swahili)

### French (Rwanda, Burundi, DRC)
- Use formal business French
- Handle gender agreements in dynamic content
- Consider francophone African context

### Arabic (Sudan, Somalia)
- Ensure proper RTL text flow
- Use Modern Standard Arabic for formal content
- Consider cultural sensitivity for financial terms
- Handle Arabic numerals vs Hindu-Arabic numerals

## Common Issues and Solutions

### Text Overflow
```javascript
// Problem: Long German text overflows
<Text>Kontoeinstellungen</Text>

// Solution: Use ResponsiveText
<ResponsiveText 
  translationKey="settings.account"
  maxLines={2}
  adjustsFontSizeToFit={true}
/>
```

### Missing Translations
```javascript
// Fallback mechanism
const getText = (key) => {
  return t(key) || t(`en.${key}`) || key;
};
```

### RTL Layout Issues
```javascript
// Use I18nManager for RTL-aware layouts
import { I18nManager } from 'react-native';

const styles = StyleSheet.create({
  row: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row'
  }
});
```

## Deployment Checklist

- [ ] All translation files are complete (>90%)
- [ ] String interpolation tested with all languages
- [ ] RTL support verified for Arabic
- [ ] Currency formatting tested for all East African currencies
- [ ] Performance optimization implemented
- [ ] Bundle size impact minimized
- [ ] Cultural appropriateness reviewed
- [ ] Professional terminology verified
- [ ] Error handling tested in all languages
- [ ] Form validation messages localized

## Support and Maintenance

### Adding New Translation Keys
1. Add to `en.js` first (baseline)
2. Update all other language files
3. Test string interpolation if applicable
4. Verify cultural appropriateness
5. Update documentation

### Updating Existing Translations
1. Review cultural context
2. Test with real users if possible
3. Verify technical accuracy for financial terms
4. Update all affected languages consistently

### Performance Monitoring
```javascript
// Monitor translation lookup performance
const stats = languageLoader.getCacheStats();
console.log('Cache stats:', stats);

// Measure lookup time
const perf = languageLoader.measureLookupPerformance('sw', 'analytics.title');
console.log('Lookup time:', perf.time, 'ms');
```

## Resources

- [React Native Internationalization](https://reactnative.dev/docs/localization)
- [East African Language Resources](https://example.com/resources)
- [RTL Layout Guidelines](https://example.com/rtl-guide)
- [Financial Terminology Standards](https://example.com/financial-terms)
