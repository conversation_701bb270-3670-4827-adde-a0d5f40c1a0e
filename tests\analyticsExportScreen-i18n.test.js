/**
 * AnalyticsExportScreen i18n Implementation Test
 * 
 * Tests to verify that AnalyticsExportScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing AnalyticsExportScreen.js i18n Implementation\n');

// Read the AnalyticsExportScreen.js file
const analyticsExportScreenPath = path.join(__dirname, '../screens/AnalyticsExportScreen.js');
const analyticsExportScreenContent = fs.readFileSync(analyticsExportScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = analyticsExportScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = analyticsExportScreenContent.includes('const { t }') || analyticsExportScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded Alert.alert strings replacement
console.log('\n✅ Test 3: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = analyticsExportScreenContent.match(alertPattern);
const totalAlerts = (analyticsExportScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 4: Check for hardcoded strings in arrays and objects
console.log('\n✅ Test 4: Hardcoded strings in data structures');
const hardcodedStrings = [
  'Export Analytics',
  'PDF Report',
  'Excel/CSV',
  'Comprehensive report with charts and insights',
  'Raw data for further analysis',
  'Complete Analytics Report',
  'Spending Trends Analysis',
  'Include Charts',
  'Include Insights',
  'Include Raw Data',
  'Export Report',
  'Exporting...',
  'Export Successful',
  'Export Failed'
];

let hardcodedFound = [];
hardcodedStrings.forEach(str => {
  const pattern = new RegExp(`['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (pattern.test(analyticsExportScreenContent) && !analyticsExportScreenContent.includes(`t('analyticsExport.`)) {
    hardcodedFound.push(str);
  }
});

if (hardcodedFound.length === 0) {
  console.log('   All hardcoded strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded strings still found: ❌ FAIL');
  hardcodedFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 5: Check for proper translation key usage
console.log('\n✅ Test 5: Translation key usage');
const translationKeys = [
  't(\'analyticsExport.title\')',
  't(\'analyticsExport.pdfReport\')',
  't(\'analyticsExport.excelCsv\')',
  't(\'analyticsExport.completeAnalyticsReport\')',
  't(\'analyticsExport.spendingTrendsAnalysis\')',
  't(\'analyticsExport.includeCharts\')',
  't(\'analyticsExport.includeInsights\')',
  't(\'analyticsExport.includeRawData\')',
  't(\'analyticsExport.exportReport\')',
  't(\'analyticsExport.exporting\')',
  't(\'analyticsExport.exportSuccessful\')',
  't(\'analyticsExport.exportFailed\')'
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (analyticsExportScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check that required translation keys exist in en.js
console.log('\n✅ Test 6: Translation keys in en.js');
const requiredKeys = [
  'title:',
  'pdfReport:',
  'pdfDescription:',
  'excelCsv:',
  'excelDescription:',
  'completeAnalyticsReport:',
  'spendingTrendsAnalysis:',
  'categorySpendingReport:',
  'monthlyComparisonReport:',
  'savingsProgressReport:',
  'investmentPerformanceReport:',
  'includeCharts:',
  'includeChartsDescription:',
  'includeInsights:',
  'includeInsightsDescription:',
  'includeRawData:',
  'includeRawDataDescription:',
  'exportReport:',
  'exporting:',
  'exportSuccessful:',
  'exportSuccessMessage:',
  'exportFailed:',
  'exportFailedMessage:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for string interpolation usage
console.log('\n✅ Test 7: String interpolation');
const interpolationPattern = /t\('analyticsExport\.exportSuccessMessage',\s*\{\s*title:/g;
const interpolationMatches = analyticsExportScreenContent.match(interpolationPattern);
const interpolationCount = interpolationMatches ? interpolationMatches.length : 0;

console.log(`   String interpolation usage: ${interpolationCount} instances ${interpolationCount > 0 ? '✅ PASS' : '❌ FAIL'}`);

// Test 8: Check analyticsExport section exists in en.js
console.log('\n✅ Test 8: AnalyticsExport section in en.js');
const analyticsExportSection = enTranslationsContent.includes('analyticsExport: {');
console.log(`   AnalyticsExport section exists: ${analyticsExportSection ? '✅ PASS' : '❌ FAIL'}`);

// Test 9: Check for export formats and chart titles internationalization
console.log('\n✅ Test 9: Export formats and chart titles');
const exportFormatsPattern = /exportFormats\s*=\s*\[[\s\S]*?t\('analyticsExport\./g;
const chartTitlesPattern = /chartTitles\s*=\s*\{[\s\S]*?t\('analyticsExport\./g;

const exportFormatsInternationalized = exportFormatsPattern.test(analyticsExportScreenContent);
const chartTitlesInternationalized = chartTitlesPattern.test(analyticsExportScreenContent);

console.log(`   Export formats internationalized: ${exportFormatsInternationalized ? '✅ PASS' : '❌ FAIL'}`);
console.log(`   Chart titles internationalized: ${chartTitlesInternationalized ? '✅ PASS' : '❌ FAIL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 11; // Including the two sub-tests in Test 9
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (hardcodedFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (interpolationCount > 0) passedTests++;
if (analyticsExportSection) passedTests++;
if (exportFormatsInternationalized) passedTests++;
if (chartTitlesInternationalized) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 AnalyticsExportScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
  console.log('   Export options, file formats, and date range selectors are internationalized!');
} else {
  console.log('\n⚠️  AnalyticsExportScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test export functionality with different languages');
console.log('2. Verify file format options display correctly');
console.log('3. Test dynamic value interpolation in export messages');
console.log('4. Proceed to SavingsAnalyticsScreen.js implementation');
