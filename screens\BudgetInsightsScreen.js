import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import analyticsService from '../services/analyticsService';
import currencyService from '../services/currencyService';

const BudgetInsightsScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const { convertAndFormat } = useCurrencyContext();
  const styles = createStyles(theme);
  const [loading, setLoading] = useState(true);
  const [aiRecommendations, setAiRecommendations] = useState([]);
  const [budgetScore, setBudgetScore] = useState(0);
  const [quickWins, setQuickWins] = useState([]);
  const [personalizedTips, setPersonalizedTips] = useState([]);

  useEffect(() => {
    loadBudgetInsights();
  }, []);

  const loadBudgetInsights = async () => {
    try {
      setLoading(true);
      
      // Ensure analytics service is initialized
      if (!analyticsService.isInitialized) {
        await analyticsService.initialize();
      }
      
      // Get AI-powered budget insights
      const insights = await generateAIRecommendations();
      const score = calculateBudgetScore();
      const wins = generateQuickWins();
      const tips = generatePersonalizedTips();
      
      setAiRecommendations(insights);
      setBudgetScore(score);
      setQuickWins(wins);
      setPersonalizedTips(tips);
      
    } catch (error) {
      console.error('❌ Error loading budget insights:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateAIRecommendations = async () => {
    const spending = analyticsService.getSpendingByCategory('month');
    const budgetData = analyticsService.getBudgetInsights();
    const totalSpent = spending.reduce((sum, cat) => sum + cat.amount, 0);
    const totalIncome = budgetData.totalIncome;

    const recommendations = [];

    // If no spending data (new user), return empty state recommendations
    if (totalSpent === 0 || spending.length === 0) {
      return [{
        id: 'getting_started',
        title: '🚀 Welcome to JiraniPay!',
        priority: 'high',
        impact: 'High',
        description: 'Start using JiraniPay to track your spending and get personalized financial insights.',
        actionSteps: [
          'Make your first transaction to begin tracking',
          'Top up your wallet to get started',
          'Pay bills or send money to see insights',
          'Check back here for AI-powered recommendations'
        ],
        category: 'onboarding',
        color: Colors.primary.main,
        icon: 'rocket-outline'
      }];
    }

    // AI Recommendation 1: Savings Rate Optimization
    if (budgetData.savingsRate < 20) {
      const targetSavings = totalIncome * 0.2;
      const currentSavings = totalIncome - totalSpent;
      const additionalSavingsNeeded = targetSavings - currentSavings;
      
      recommendations.push({
        id: 'savings_optimization',
        title: '💰 Boost Your Savings Rate',
        priority: 'high',
        impact: 'High',
        description: `You're currently saving ${budgetData.savingsRate.toFixed(1)}%. To reach the recommended 20%, you need to save an additional ${formatCurrency(additionalSavingsNeeded)} monthly.`,
        actionSteps: [
          'Set up automatic transfers to savings',
          'Reduce one discretionary expense category by 15%',
          'Use the 50/30/20 budgeting rule',
          'Track daily expenses for better awareness'
        ],
        category: 'savings',
        color: Colors.status.success,
        icon: 'trending-up-outline'
      });
    }

    // AI Recommendation 2: Top Spending Category Optimization
    if (spending.length > 0) {
      const topCategory = spending[0];
      const categoryPercentage = (topCategory.amount / totalSpent) * 100;
      
      if (categoryPercentage > 30) {
        recommendations.push({
          id: 'category_optimization',
          title: `🎯 Optimize ${topCategory.name}`,
          priority: 'medium',
          impact: 'Medium',
          description: `${topCategory.name} represents ${categoryPercentage.toFixed(1)}% of your spending (${formatCurrency(topCategory.amount)}). This seems high for this category.`,
          actionSteps: [
            'Review all transactions in this category',
            'Look for subscription services you don\'t use',
            'Compare prices and find alternatives',
            'Set a monthly limit for this category'
          ],
          category: topCategory.category,
          color: topCategory.color,
          icon: topCategory.icon
        });
      }
    }

    // AI Recommendation 3: Bills & Utilities Optimization
    const billsSpending = spending.find(cat => cat.category === 'bills');
    const entertainmentSpending = spending.find(cat => cat.category === 'entertainment');
    const totalBillsAmount = (billsSpending?.amount || 0) + (entertainmentSpending?.amount || 0);

    // Only add this recommendation once and ensure it meets the threshold
    if (totalBillsAmount > 100000 && !recommendations.find(r => r.id === 'bills_optimization')) {
      const potentialSavings = totalBillsAmount * 0.15; // Estimate 15% savings potential
      recommendations.push({
        id: 'bills_optimization',
        title: '📋 Optimize Bills & Utilities',
        priority: 'medium',
        impact: 'Medium',
        description: `You're spending ${formatCurrency(totalBillsAmount)} monthly on bills and subscriptions. You could potentially save ${formatCurrency(potentialSavings)} by optimizing these expenses.`,
        actionSteps: [
          'Audit all active subscriptions and cancel unused ones',
          'Set up automatic bill payment reminders',
          'Compare utility providers for better rates',
          'Track bill payment history and due dates',
          'Set spending limits for subscription services'
        ],
        category: 'bills',
        color: Colors.accent.amber,
        icon: 'receipt-outline'
      });
    }

    // AI Recommendation 4: Emergency Fund Building
    const emergencyFundTarget = totalSpent * 3; // 3 months of expenses
    recommendations.push({
      id: 'emergency_fund',
      title: '🛡️ Build Emergency Fund',
      priority: 'high',
      impact: 'High',
      description: `Build an emergency fund of ${formatCurrency(emergencyFundTarget)} (3 months of expenses) for financial security.`,
      actionSteps: [
        'Start with a goal of UGX 100,000',
        'Save UGX 25,000 weekly until you reach the target',
        'Keep emergency funds in a separate account',
        'Only use for true emergencies'
      ],
      category: 'savings',
      color: Colors.secondary.lake,
      icon: 'shield-checkmark-outline'
    });

    return recommendations;
  };

  const calculateBudgetScore = () => {
    const budgetData = analyticsService.getBudgetInsights();

    // If no spending data, return 0 for new users
    if (!budgetData || budgetData.totalSpent === 0) {
      return 0;
    }

    let score = 0;

    // Savings rate (40 points max)
    if (budgetData.savingsRate >= 20) score += 40;
    else if (budgetData.savingsRate >= 15) score += 30;
    else if (budgetData.savingsRate >= 10) score += 20;
    else if (budgetData.savingsRate >= 5) score += 10;

    // Budget adherence (30 points max)
    const overBudgetCategories = budgetData.budgetAnalysis.filter(cat => cat.status === 'over');
    if (overBudgetCategories.length === 0) score += 30;
    else if (overBudgetCategories.length <= 2) score += 20;
    else if (overBudgetCategories.length <= 4) score += 10;

    // Essential vs non-essential spending (30 points max)
    const essentialSpending = budgetData.budgetAnalysis
      .filter(cat => cat.priority === 'high' && cat.category !== 'savings')
      .reduce((sum, cat) => sum + cat.actualAmount, 0);
    const essentialPercentage = budgetData.totalSpent > 0 ? (essentialSpending / budgetData.totalSpent) * 100 : 0;

    if (essentialPercentage <= 60) score += 30;
    else if (essentialPercentage <= 70) score += 20;
    else if (essentialPercentage <= 80) score += 10;

    return Math.min(score, 100);
  };

  const generateQuickWins = () => {
    const spending = analyticsService.getSpendingByCategory('month');
    const wins = [];

    // If no spending data, return onboarding quick wins
    if (!spending || spending.length === 0) {
      return [{
        title: 'Start Your Financial Journey',
        description: 'Make your first transaction to unlock personalized quick wins',
        potentialSavings: 0,
        effort: 'Low',
        timeframe: 'Today',
        icon: 'rocket-outline',
        color: Colors.primary.main
      }];
    }

    // Quick Win 1: Subscription Audit
    const entertainmentSpending = spending.find(cat => cat.category === 'entertainment');
    if (entertainmentSpending && entertainmentSpending.amount > 50000) {
      wins.push({
        title: 'Audit Subscriptions',
        description: 'Cancel unused streaming services and subscriptions',
        potentialSavings: 25000,
        effort: 'Low',
        timeframe: 'This week',
        icon: 'tv-outline',
        color: Colors.accent.coral
      });
    }

    // Quick Win 2: Transport Optimization
    const transportSpending = spending.find(cat => cat.category === 'transport');
    if (transportSpending && transportSpending.amount > 100000) {
      wins.push({
        title: 'Optimize Transport',
        description: 'Use public transport or carpool 2 days a week',
        potentialSavings: 40000,
        effort: 'Medium',
        timeframe: 'This month',
        icon: 'car-outline',
        color: Colors.secondary.savanna
      });
    }

    // Quick Win 3: Food Budget
    const foodSpending = spending.find(cat => cat.category === 'food');
    if (foodSpending && foodSpending.amount > 200000) {
      wins.push({
        title: 'Meal Planning',
        description: 'Cook at home 4 days a week instead of eating out',
        potentialSavings: 60000,
        effort: 'Medium',
        timeframe: 'This month',
        icon: 'restaurant-outline',
        color: Colors.accent.gold
      });
    }

    return wins;
  };

  const generatePersonalizedTips = () => {
    const budgetData = analyticsService.getBudgetInsights();
    const spending = analyticsService.getSpendingByCategory('month');
    const tips = [];

    // If no spending data, return onboarding tips
    if (!spending || spending.length === 0 || budgetData.totalSpent === 0) {
      return [{
        title: 'Welcome to Smart Finance',
        description: 'Start by making transactions to get personalized tips based on your spending patterns.',
        category: 'onboarding',
        icon: 'bulb-outline',
        color: Colors.primary.main
      }, {
        title: 'Set Up Your Wallet',
        description: 'Top up your JiraniPay wallet to begin tracking your financial journey.',
        category: 'setup',
        icon: 'wallet-outline',
        color: Colors.secondary.savanna
      }];
    }

    // Tip based on savings rate
    if (budgetData.savingsRate < 10) {
      tips.push({
        title: 'Start Small with Savings',
        description: 'Begin by saving just UGX 5,000 per week. Small consistent amounts build the habit.',
        category: 'savings',
        icon: 'wallet-outline',
        color: Colors.status.success
      });
    }

    // Tip based on spending patterns
    const shoppingSpending = spending.find(cat => cat.category === 'shopping');
    if (shoppingSpending && shoppingSpending.amount > 150000) {
      tips.push({
        title: 'Shopping Smart',
        description: 'Use the 24-hour rule: wait a day before making non-essential purchases over UGX 50,000.',
        category: 'shopping',
        icon: 'bag-outline',
        color: Colors.primary.main
      });
    }

    // General financial wellness tip
    tips.push({
      title: 'Track Daily Expenses',
      description: 'Spend 2 minutes each evening logging your expenses. Awareness is the first step to control.',
      category: 'general',
      icon: 'analytics-outline',
      color: Colors.secondary.lake
    });

    return tips;
  };

  const formatCurrency = (amount) => {
    try {
      return convertAndFormat(amount);
    } catch (error) {
      return convertAndFormat(amount);
    }
  };

  const getScoreColor = (score) => {
    if (score >= 80) return Colors.status.success;
    if (score >= 60) return Colors.accent.amber;
    return Colors.status.error;
  };

  const getScoreLabel = (score) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Needs Improvement';
  };

  const getPriorityColor = (priority) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.status.error; // Red for high priority
      case 'medium':
        return Colors.accent.amber; // Amber for medium priority
      case 'low':
        return Colors.status.success; // Green for low priority
      default:
        return Colors.neutral.warmGray; // Default gray
    }
  };

  const handleSetReminder = (recommendation) => {
    console.log('🔔 Setting up reminder for:', recommendation.id);

    // Navigate to AutomaticSavingsScreen with recommendation data
    navigation.navigate('AutomaticSavings', {
      recommendation,
      setupType: 'reminder' // This will open the reminder modal
    });
  };

  const handleSetAutomaticSavings = (recommendation) => {
    console.log('🤖 Setting up automatic savings for:', recommendation.id);

    // Navigate to AutomaticSavingsScreen with recommendation data
    navigation.navigate('AutomaticSavings', {
      recommendation,
      setupType: 'automatic' // This will open the automatic plan modal
    });
  };

  const handleSetBillsReminder = (recommendation) => {
    console.log('📋 Setting up bills reminder for:', recommendation.id);

    // Navigate to AutomaticSavingsScreen with bills-specific reminder data
    navigation.navigate('AutomaticSavings', {
      recommendation: {
        ...recommendation,
        title: '📋 Bills Audit Reminder',
        description: 'Regular reminder to review and optimize your bills and subscriptions'
      },
      setupType: 'reminder'
    });
  };

  const handleImplementRecommendation = (recommendation) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    if (recommendation.id === 'savings_optimization') {
      Alert.alert(
        recommendation.title,
        `Ready to start saving?\n\nAction Steps:\n${recommendation.actionSteps.map((step, index) => `${index + 1}. ${step}`).join('\n')}`,
        [
          { text: 'Not Now', style: 'cancel' },
          { text: 'Set Reminder', onPress: () => handleSetReminder(recommendation) },
          { text: 'Auto Savings', onPress: () => handleSetAutomaticSavings(recommendation) },
          {
            text: 'Start Saving',
            onPress: () => {
              console.log('Navigating to Savings for:', recommendation.id);
              navigation.navigate('Savings', { source: 'budgetInsights' });
            }
          }
        ]
      );
    } else if (recommendation.id === 'bills_optimization') {
      Alert.alert(
        recommendation.title,
        `Ready to optimize your bills?\n\nAction Steps:\n${recommendation.actionSteps.map((step, index) => `${index + 1}. ${step}`).join('\n')}`,
        [
          { text: 'Not Now', style: 'cancel' },
          { text: 'Set Reminder', onPress: () => handleSetBillsReminder(recommendation) },
          {
            text: 'Start Now',
            onPress: () => {
              console.log('Navigating to Bills Optimization for:', recommendation.id);
              navigation.navigate('BillsOptimization', {
                source: 'budgetInsights',
                recommendation
              });
            }
          }
        ]
      );
    } else if (recommendation.id === 'emergency_fund') {
      Alert.alert(
        recommendation.title,
        `Ready to build your emergency fund?\n\nAction Steps:\n${recommendation.actionSteps.map((step, index) => `${index + 1}. ${step}`).join('\n')}`,
        [
          { text: 'Not Now', style: 'cancel' },
          { text: 'Set Reminder', onPress: () => handleSetReminder(recommendation) },
          { text: 'Auto Savings', onPress: () => handleSetAutomaticSavings(recommendation) },
          {
            text: 'Create Fund',
            onPress: () => {
              console.log('Navigating to Savings for emergency fund');
              navigation.navigate('Savings', {
                source: 'budgetInsights',
                createGoal: 'Emergency Fund',
                targetAmount: 500000
              });
            }
          }
        ]
      );
    } else {
      Alert.alert(
        recommendation.title,
        `Ready to implement this recommendation?\n\nAction Steps:\n${recommendation.actionSteps.map((step, index) => `${index + 1}. ${step}`).join('\n')}`,
        [
          { text: 'Not Now', style: 'cancel' },
          { text: 'Set Reminder', onPress: () => handleSetReminder(recommendation) },
          { text: 'Start Now', onPress: () => console.log('Implementing:', recommendation.id) }
        ]
      );
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Generating AI insights...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>Budget Insights</Text>
          <Text style={styles.headerSubtitle}>AI-Powered Recommendations</Text>
        </View>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Budget Score */}
        <View style={styles.scoreContainer}>
          <View style={styles.scoreCircle}>
            <Text style={[styles.scoreNumber, { color: getScoreColor(budgetScore) }]}>
              {budgetScore}
            </Text>
            <Text style={styles.scoreLabel}>Budget Score</Text>
          </View>
          <View style={styles.scoreDetails}>
            <Text style={styles.scoreStatus}>{getScoreLabel(budgetScore)}</Text>
            <Text style={styles.scoreDescription}>
              {budgetScore >= 80 ? 'You\'re doing great! Keep it up.' :
               budgetScore >= 60 ? 'Good progress! A few tweaks can improve your score.' :
               'There\'s room for improvement. Let\'s work on it together.'}
            </Text>
          </View>
        </View>

        {/* AI Recommendations */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🤖 AI Recommendations</Text>
          {aiRecommendations.map((recommendation) => (
            <TouchableOpacity
              key={recommendation.id}
              style={styles.recommendationCard}
              onPress={() => handleImplementRecommendation(recommendation)}
            >
              <View style={styles.recommendationHeader}>
                <View style={[styles.recommendationIcon, { backgroundColor: recommendation.color + '20' }]}>
                  <Ionicons name={recommendation.icon} size={24} color={recommendation.color} />
                </View>
                <View style={styles.recommendationMeta}>
                  <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(recommendation.priority) }]}>
                    <Text style={styles.priorityText}>{recommendation.priority.toUpperCase()}</Text>
                  </View>
                  <Text style={styles.impactText}>Impact: {recommendation.impact}</Text>
                </View>
              </View>
              <Text style={styles.recommendationTitle}>{recommendation.title}</Text>
              <Text style={styles.recommendationDescription}>{recommendation.description}</Text>
              <View style={styles.actionStepsPreview}>
                <Text style={styles.actionStepsTitle}>Action Steps:</Text>
                <Text style={styles.actionStepsText}>
                  {recommendation.actionSteps.slice(0, 2).map((step, index) => `${index + 1}. ${step}`).join('\n')}
                  {recommendation.actionSteps.length > 2 && '\n+ more...'}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Quick Wins */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>⚡ Quick Wins</Text>
          <Text style={styles.sectionSubtitle}>Easy changes with immediate impact</Text>
          {quickWins.map((win, index) => (
            <View key={index} style={styles.quickWinCard}>
              <View style={styles.quickWinHeader}>
                <View style={[styles.quickWinIcon, { backgroundColor: win.color + '20' }]}>
                  <Ionicons name={win.icon} size={20} color={win.color} />
                </View>
                <View style={styles.quickWinMeta}>
                  <Text style={styles.quickWinSavings}>Save {formatCurrency(win.potentialSavings)}</Text>
                  <Text style={styles.quickWinTimeframe}>{win.timeframe}</Text>
                </View>
              </View>
              <Text style={styles.quickWinTitle}>{win.title}</Text>
              <Text style={styles.quickWinDescription}>{win.description}</Text>
              <View style={styles.quickWinFooter}>
                <Text style={styles.quickWinEffort}>Effort: {win.effort}</Text>
                <TouchableOpacity style={styles.quickWinButton}>
                  <Text style={styles.quickWinButtonText}>Start</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>

        {/* Personalized Tips */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💡 Personalized Tips</Text>
          <Text style={styles.sectionSubtitle}>Based on your spending patterns</Text>
          {personalizedTips.map((tip, index) => (
            <View key={index} style={styles.tipCard}>
              <View style={styles.tipHeader}>
                <View style={[styles.tipIcon, { backgroundColor: tip.color + '20' }]}>
                  <Ionicons name={tip.icon} size={18} color={tip.color} />
                </View>
                <Text style={styles.tipTitle}>{tip.title}</Text>
              </View>
              <Text style={styles.tipDescription}>{tip.description}</Text>
            </View>
          ))}
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.neutral.appBackground,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  backButton: {
    padding: 8,
  },
  headerCenter: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  headerSubtitle: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
    marginTop: 12,
  },
  scoreContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.neutral.white,
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  scoreCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.neutral.creamLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  scoreNumber: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 10,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  scoreDetails: {
    flex: 1,
  },
  scoreStatus: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  scoreDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
  },
  section: {
    backgroundColor: Colors.neutral.white,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 16,
  },
  recommendationCard: {
    backgroundColor: Colors.neutral.creamLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  recommendationIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recommendationMeta: {
    alignItems: 'flex-end',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
    marginBottom: 4,
  },
  priorityText: {
    fontSize: 10,
    color: Colors.neutral.white,
    fontWeight: 'bold',
  },
  impactText: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  recommendationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  recommendationDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
    marginBottom: 12,
  },
  actionStepsPreview: {
    backgroundColor: Colors.neutral.white,
    padding: 12,
    borderRadius: 8,
  },
  actionStepsTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  actionStepsText: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    lineHeight: 16,
  },
  // Quick Wins Styles
  quickWinCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.status.success,
  },
  quickWinHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  quickWinIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickWinMeta: {
    alignItems: 'flex-end',
  },
  quickWinSavings: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.status.success,
  },
  quickWinTimeframe: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  quickWinTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 6,
  },
  quickWinDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 18,
    marginBottom: 12,
  },
  quickWinFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  quickWinEffort: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  quickWinButton: {
    backgroundColor: Colors.primary.main,
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 16,
  },
  quickWinButtonText: {
    fontSize: 12,
    color: Colors.neutral.white,
    fontWeight: '600',
  },
  // Personalized Tips Styles
  tipCard: {
    backgroundColor: Colors.neutral.creamLight,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  tipHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  tipIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  tipDescription: {
    fontSize: 13,
    color: Colors.neutral.warmGray,
    lineHeight: 18,
    marginLeft: 32,
  },
  bottomSpacing: {
    height: 20,
  },
});

export default BudgetInsightsScreen;
