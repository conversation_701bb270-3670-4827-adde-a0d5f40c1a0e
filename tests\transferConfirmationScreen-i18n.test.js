/**
 * TransferConfirmationScreen i18n Implementation Test
 * 
 * Tests to verify that TransferConfirmationScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing TransferConfirmationScreen.js i18n Implementation\n');

// Read the TransferConfirmationScreen.js file
const transferConfirmationScreenPath = path.join(__dirname, '../screens/TransferConfirmationScreen.js');
const transferConfirmationScreenContent = fs.readFileSync(transferConfirmationScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = transferConfirmationScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = transferConfirmationScreenContent.includes('const { t }') || transferConfirmationScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded Alert.alert strings replacement
console.log('\n✅ Test 3: Hardcoded Alert.alert strings replacement');
const hardcodedAlertStrings = [
  'Authentication Failed',
  'Biometric authentication failed. Please try PIN authentication.',
  'Authentication failed. Please try again.',
  'Invalid PIN',
  'Please enter a 4-digit PIN',
  'PIN Verification',
  'PIN verification service temporarily unavailable',
  'PIN verification failed. Please try again.',
  'Transfer Failed',
  'Transfer could not be completed',
  'Transfer failed. Please try again.'
];

let hardcodedAlertsFound = [];
hardcodedAlertStrings.forEach(str => {
  // Check if the string appears in Alert.alert calls (not in translation keys)
  const alertPattern = new RegExp(`Alert\\.alert\\([^)]*['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (alertPattern.test(transferConfirmationScreenContent)) {
    hardcodedAlertsFound.push(str);
  }
});

if (hardcodedAlertsFound.length === 0) {
  console.log('   All hardcoded Alert.alert strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Alert.alert strings still found: ❌ FAIL');
  hardcodedAlertsFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 4: Check for hardcoded Text component strings
console.log('\n✅ Test 4: Hardcoded Text component strings');
const hardcodedTextStrings = [
  'Transfer Summary',
  'Transfer Details',
  'Amount',
  'Transfer Fee',
  'Purpose',
  'Total Amount',
  'Free Transfer!',
  'Confirm Transfer',
  'Choose your preferred authentication method to complete this transfer',
  'PIN Authentication',
  'Enter your 4-digit PIN',
  'Enter PIN',
  'Verify PIN'
];

let hardcodedTextFound = [];
hardcodedTextStrings.forEach(str => {
  // Check if the string appears in Text components (not in translation keys)
  const textPattern = new RegExp(`<Text[^>]*>\\s*${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*</Text>`, 'g');
  const directTextPattern = new RegExp(`['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (textPattern.test(transferConfirmationScreenContent) || 
      (directTextPattern.test(transferConfirmationScreenContent) && !transferConfirmationScreenContent.includes(`t('transfer.`))) {
    hardcodedTextFound.push(str);
  }
});

if (hardcodedTextFound.length === 0) {
  console.log('   All hardcoded Text strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Text strings still found: ❌ FAIL');
  hardcodedTextFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 5: Check for proper translation key usage
console.log('\n✅ Test 5: Translation key usage');
const translationKeys = [
  't(\'transfer.authenticationFailed\')',
  't(\'transfer.biometricAuthFailedMessage\')',
  't(\'transfer.authenticationFailedTryAgain\')',
  't(\'transfer.invalidPIN\')',
  't(\'transfer.pleaseEnter4DigitPIN\')',
  't(\'transfer.pinVerification\')',
  't(\'transfer.pinVerificationUnavailable\')',
  't(\'transfer.pinVerificationFailed\')',
  't(\'transfer.transferFailed\')',
  't(\'transfer.transferCouldNotBeCompleted\')',
  't(\'transfer.transferFailedTryAgain\')',
  't(\'transfer.transferSummary\')',
  't(\'transfer.transferDetails\')',
  't(\'transfer.amount\')',
  't(\'transfer.transferFee\')',
  't(\'transfer.purpose\')',
  't(\'transfer.totalAmount\')',
  't(\'transfer.freeTransfer\')',
  't(\'transfer.confirmTransfer\')',
  't(\'transfer.chooseAuthenticationMethod\')',
  't(\'transfer.pinAuthentication\')',
  't(\'transfer.enterYour4DigitPIN\')',
  't(\'transfer.enterPIN\')',
  't(\'transfer.verifyPIN\')'
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (transferConfirmationScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check that required translation keys exist in en.js
console.log('\n✅ Test 6: Translation keys in en.js');
const requiredKeys = [
  'authenticationFailed:',
  'biometricAuthFailedMessage:',
  'authenticationFailedTryAgain:',
  'invalidPIN:',
  'pleaseEnter4DigitPIN:',
  'pinVerification:',
  'pinVerificationUnavailable:',
  'pinVerificationFailed:',
  'transferFailed:',
  'transferCouldNotBeCompleted:',
  'transferFailedTryAgain:',
  'transferSummary:',
  'transferDetails:',
  'amount:',
  'transferFee:',
  'purpose:',
  'totalAmount:',
  'freeTransfer:',
  'confirmTransfer:',
  'chooseAuthenticationMethod:',
  'pinAuthentication:',
  'enterYour4DigitPIN:',
  'enterPIN:',
  'verifyPIN:',
  'enterPINPlaceholder:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for Alert.alert with proper translation keys
console.log('\n✅ Test 7: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = transferConfirmationScreenContent.match(alertPattern);
const totalAlerts = (transferConfirmationScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 8: Check for placeholder text internationalization
console.log('\n✅ Test 8: Placeholder text internationalization');
const placeholderPattern = /placeholder=\{t\(/g;
const placeholderMatches = transferConfirmationScreenContent.match(placeholderPattern);
const totalPlaceholders = (transferConfirmationScreenContent.match(/placeholder=/g) || []).length;
const translatedPlaceholders = placeholderMatches ? placeholderMatches.length : 0;

console.log(`   Translated placeholders: ${translatedPlaceholders}/${totalPlaceholders} ${translatedPlaceholders === totalPlaceholders ? '✅ PASS' : '❌ PARTIAL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 8;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (hardcodedAlertsFound.length === 0) passedTests++;
if (hardcodedTextFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (translatedPlaceholders === totalPlaceholders) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 TransferConfirmationScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
} else {
  console.log('\n⚠️  TransferConfirmationScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test transfer confirmation flow with different languages');
console.log('2. Verify PIN authentication and biometric auth flows');
console.log('3. Test amount display and fee calculations');
console.log('4. Proceed to BillConfirmationScreen.js implementation');
