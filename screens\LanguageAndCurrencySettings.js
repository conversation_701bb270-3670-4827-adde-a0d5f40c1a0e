import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { Colors } from '../constants/Colors';

import ResponsiveText from '../components/ResponsiveText';
import CurrencySelector from '../components/CurrencySelector';
import LanguageSelector from '../components/LanguageSelector';

import currencyService from '../services/currencyService';
import authService from '../services/authService';

/**
 * Language and Currency Settings Screen
 * Dedicated screen for managing language and currency preferences
 * Provides detailed options and immediate preview of changes
 */
const LanguageAndCurrencySettings = ({ navigation }) => {
  const { theme } = useTheme();
  const { t, currentLanguage, changeLanguage } = useLanguage();
  const styles = createStyles(theme);

  const [loading, setLoading] = useState(false);
  const [currentCurrency, setCurrentCurrency] = useState('UGX');
  const [availableCurrencies, setAvailableCurrencies] = useState([]);
  const [user, setUser] = useState(null);

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Get current user
      const currentUser = authService.getCurrentUser();
      setUser(currentUser);

      // Load current currency preference
      const userCurrency = await currencyService.getUserPreferredCurrency();
      setCurrentCurrency(userCurrency);

      // Load available currencies - East Africa only
      const eastAfricanCurrencies = currencyService.getEastAfricanCurrencies();
      setAvailableCurrencies(eastAfricanCurrencies);

    } catch (error) {
      console.error('❌ Error loading settings data:', error);
      Alert.alert(
        t('common.error'),
        t('settings.failedToLoadSettings'),
        [{ text: t('common.ok') }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageChange = async (languageCode) => {
    try {
      setLoading(true);
      
      await changeLanguage(languageCode);
      
      if (Haptics.notificationAsync) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
      
      Alert.alert(
        t('common.success'),
        t('settings.languageUpdatedSuccessfully'),
        [{ text: t('common.ok') }]
      );
    } catch (error) {
      console.error('❌ Error changing language:', error);
      Alert.alert(
        t('common.error'),
        t('settings.failedToUpdateLanguage'),
        [{ text: t('common.ok') }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCurrencyChange = async (currencyCode, currencyInfo) => {
    try {
      setLoading(true);
      
      // Update currency service
      const result = await currencyService.setUserPreferredCurrency(currencyCode);
      
      if (result.success) {
        setCurrentCurrency(currencyCode);
        
        if (Haptics.notificationAsync) {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }
        
        Alert.alert(
          t('common.success'),
          t('settings.currencyUpdatedTo', { currency: currencyInfo?.name || currencyCode }),
          [{ text: t('common.ok') }]
        );
      } else {
        throw new Error(result.error || 'Failed to update currency');
      }
    } catch (error) {
      console.error('❌ Error changing currency:', error);
      Alert.alert(
        t('common.error'),
        t('settings.failedToUpdateCurrency'),
        [{ text: t('common.ok') }]
      );
    } finally {
      setLoading(false);
    }
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <Ionicons name="arrow-back" size={24} color={theme.colors.textPrimary} />
      </TouchableOpacity>
      <ResponsiveText style={styles.headerTitle}>
        Language & Currency
      </ResponsiveText>
      <View style={styles.headerSpacer} />
    </View>
  );

  const renderLanguageSection = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <View style={styles.sectionIconContainer}>
          <Ionicons name="language" size={24} color={theme.colors.primary} />
        </View>
        <View style={styles.sectionHeaderText}>
          <ResponsiveText style={styles.sectionTitle}>
            Language
          </ResponsiveText>
          <ResponsiveText style={styles.sectionSubtitle}>
            Choose your preferred language for the app
          </ResponsiveText>
        </View>
      </View>

      <View style={styles.selectorContainer}>
        <LanguageSelector
          currentLanguage={currentLanguage}
          onLanguageChange={handleLanguageChange}
          style={styles.fullWidthSelector}
        />
      </View>

      <View style={styles.infoBox}>
        <Ionicons name="information-circle" size={16} color={theme.colors.primary} />
        <ResponsiveText style={styles.infoText}>
          The app will restart to apply language changes
        </ResponsiveText>
      </View>
    </View>
  );

  const renderCurrencySection = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <View style={styles.sectionIconContainer}>
          <Ionicons name="card" size={24} color={theme.colors.secondary} />
        </View>
        <View style={styles.sectionHeaderText}>
          <ResponsiveText style={styles.sectionTitle}>
            Preferred Currency
          </ResponsiveText>
          <ResponsiveText style={styles.sectionSubtitle}>
            Choose your preferred currency for transactions
          </ResponsiveText>
        </View>
      </View>

      <View style={styles.selectorContainer}>
        <CurrencySelector
          selectedCurrency={currentCurrency}
          onCurrencyChange={handleCurrencyChange}
          style={styles.fullWidthSelector}
          compact={false}
          showConversion={true}
        />
      </View>

      <View style={styles.infoBox}>
        <Ionicons name="information-circle" size={16} color={theme.colors.secondary} />
        <ResponsiveText style={styles.infoText}>
          All amounts will be displayed in your preferred currency with real-time conversion
        </ResponsiveText>
      </View>
    </View>
  );

  const renderExchangeRatesSection = () => (
    <View style={styles.section}>
      <ResponsiveText style={styles.sectionTitle}>
        Exchange Rates
      </ResponsiveText>
      
      <View style={styles.exchangeRatesContainer}>
        {availableCurrencies.slice(0, 5).map((currency) => {
          const rate = currencyService.getExchangeRateBetween('UGX', currency.code);
          return (
            <View key={currency.code} style={styles.exchangeRateItem}>
              <View style={styles.exchangeRateLeft}>
                <Text style={styles.currencyFlag}>{currency.flag}</Text>
                <ResponsiveText style={styles.currencyCode}>{currency.code}</ResponsiveText>
              </View>
              <ResponsiveText style={styles.exchangeRateValue}>
                {rate ? `1 UGX = ${rate.toFixed(4)} ${currency.code}` : 'N/A'}
              </ResponsiveText>
            </View>
          );
        })}
      </View>
      
      <ResponsiveText style={styles.lastUpdatedText}>
        Last Updated: {new Date().toLocaleString()}
      </ResponsiveText>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        {renderHeader()}
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <ResponsiveText style={styles.loadingText}>
            Loading...
          </ResponsiveText>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderLanguageSection()}
        {renderCurrencySection()}
        {renderExchangeRatesSection()}
        
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  section: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  sectionHeaderText: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  selectorContainer: {
    marginBottom: 12,
  },
  fullWidthSelector: {
    width: '100%',
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: theme.colors.primaryLight,
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  infoText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: 8,
    flex: 1,
  },
  exchangeRatesContainer: {
    marginTop: 12,
  },
  exchangeRateItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  exchangeRateLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencyFlag: {
    fontSize: 20,
    marginRight: 8,
  },
  currencyCode: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textPrimary,
  },
  exchangeRateValue: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  lastUpdatedText: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  bottomSpacing: {
    height: 32,
  },
});

export default LanguageAndCurrencySettings;
