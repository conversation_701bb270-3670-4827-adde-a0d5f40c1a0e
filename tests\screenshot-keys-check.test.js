/**
 * Screenshot-Specific Translation Keys Check
 * 
 * Verifies that all the specific keys visible in the user's screenshots
 * are now properly translated in the Swahili file
 */

const fs = require('fs');
const path = require('path');

console.log('📱 Screenshot-Specific Translation Keys Check\n');

// Load Swahili translation file
const swPath = path.join(__dirname, '../locales/sw.js');
const swContent = fs.readFileSync(swPath, 'utf8');

// Extract all translation keys and values
const extractTranslations = (content) => {
  const translations = {};
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    const match = line.match(/^\s*([a-zA-Z][a-zA-Z0-9]*)\s*:\s*'([^']*)'.*$/);
    if (match) {
      const [, key, value] = match;
      translations[key] = value;
    }
  }
  
  return translations;
};

const swTranslations = extractTranslations(swContent);

console.log('🔍 SCREENSHOT 1 - Edit Profile Screen Keys:');
const editProfileScreenKeys = [
  'editProfile',
  'save', 
  'fullName',
  'emailAddress',
  'phoneNumber',
  'dateOfBirth',
  'country',
  'preferredLanguage'
];

let screenshot1Issues = 0;
editProfileScreenKeys.forEach(key => {
  const value = swTranslations[key];
  if (value) {
    console.log(`   ✅ ${key}: "${value}"`);
  } else {
    console.log(`   ❌ ${key}: MISSING`);
    screenshot1Issues++;
  }
});

console.log('\n🔍 SCREENSHOT 2 - Account Verification Screen Keys:');
const verificationScreenKeys = [
  'accountVerification',
  'basicAccount',
  'limitedFeaturesAndTransactionLimits',
  'currentLimits',
  'dailyLimit',
  'monthlyLimit',
  'verificationSteps',
  'basicInformation',
  'completeYourProfileWithNamePhoneAndEmail',
  'phoneVerification',
  'verifyYourPhoneNumberWithOTP',
  'emailVerification',
  'verifyYourEmailAddress',
  'identityVerification',
  'uploadYourNationalIdOrPassport',
  'completed'
];

let screenshot2Issues = 0;
verificationScreenKeys.forEach(key => {
  const value = swTranslations[key];
  if (value) {
    console.log(`   ✅ ${key}: "${value}"`);
  } else {
    console.log(`   ❌ ${key}: MISSING`);
    screenshot2Issues++;
  }
});

console.log('\n🔍 SCREENSHOT 3 - Privacy & Data Screen Keys:');
const privacyScreenKeys = [
  'privacyAndData',
  'controlHowYourDataIsUsedAndShared',
  'dataConsent',
  'chooseWhatDataYoureComfortableSharingWithUs',
  'essentialServices',
  'required',
  'requiredForCoreAppFunctionalityAndSecurity',
  'analyticsAndPerformance',
  'helpUsImproveTheAppBySharingUsageData',
  'marketingCommunications',
  'receivePersonalizedOffersAndFinancialTips',
  'dataSharing',
  'shareAnonymizedDataWithTrustedPartners',
  'locationServices',
  'useLocationDataForEnhancedSecurityAndServices',
  'communicationPreferences'
];

let screenshot3Issues = 0;
privacyScreenKeys.forEach(key => {
  const value = swTranslations[key];
  if (value) {
    console.log(`   ✅ ${key}: "${value}"`);
  } else {
    console.log(`   ❌ ${key}: MISSING`);
    screenshot3Issues++;
  }
});

// Check for any remaining camelCase values
console.log('\n🔍 CAMELCASE VALUES CHECK:');
const camelCasePattern = /^[a-z]+[A-Z][a-zA-Z]*$/;
const camelCaseValues = [];

Object.entries(swTranslations).forEach(([key, value]) => {
  if (camelCasePattern.test(value)) {
    camelCaseValues.push({ key, value });
  }
});

if (camelCaseValues.length > 0) {
  console.log(`   Found ${camelCaseValues.length} camelCase values:`);
  camelCaseValues.forEach(({ key, value }) => {
    console.log(`   ❌ ${key}: "${value}" (will show as raw key)`);
  });
} else {
  console.log('   ✅ No camelCase values found');
}

// Summary
const totalIssues = screenshot1Issues + screenshot2Issues + screenshot3Issues + camelCaseValues.length;

console.log('\n📋 SCREENSHOT FIXES SUMMARY:');
console.log('============================');
console.log(`Screenshot 1 (Edit Profile) Issues: ${screenshot1Issues}`);
console.log(`Screenshot 2 (Verification) Issues: ${screenshot2Issues}`);
console.log(`Screenshot 3 (Privacy & Data) Issues: ${screenshot3Issues}`);
console.log(`CamelCase Values: ${camelCaseValues.length}`);
console.log(`Total Issues: ${totalIssues}`);

if (totalIssues === 0) {
  console.log('\n🎉 SCREENSHOT ISSUES: ✅ FIXED!');
  console.log('   All keys from the screenshots are now properly translated');
  console.log('   Users should see proper Swahili text instead of raw keys');
} else if (totalIssues <= 3) {
  console.log('\n✅ SCREENSHOT ISSUES: 🔄 MOSTLY FIXED');
  console.log('   Minor issues remain');
} else {
  console.log('\n⚠️  SCREENSHOT ISSUES: 🔄 NEEDS MORE WORK');
  console.log('   Several issues still need to be addressed');
}

console.log('\n🎯 USER EXPERIENCE PREDICTION:');
console.log('==============================');
if (screenshot1Issues === 0) {
  console.log('✅ Edit Profile Screen: Will show "Hariri Wasifu" and proper Swahili labels');
} else {
  console.log(`❌ Edit Profile Screen: ${screenshot1Issues} keys still showing as raw text`);
}

if (screenshot2Issues === 0) {
  console.log('✅ Account Verification Screen: Will show "Uthibitishaji wa Akaunti" and proper Swahili');
} else {
  console.log(`❌ Account Verification Screen: ${screenshot2Issues} keys still showing as raw text`);
}

if (screenshot3Issues === 0) {
  console.log('✅ Privacy & Data Screen: Will show "Faragha na Taarifa" and proper Swahili');
} else {
  console.log(`❌ Privacy & Data Screen: ${screenshot3Issues} keys still showing as raw text`);
}

console.log('\n🚀 TESTING INSTRUCTIONS:');
console.log('========================');
console.log('1. Close the JiraniPay app completely');
console.log('2. Clear app cache/data if possible');
console.log('3. Restart the app');
console.log('4. Switch to Swahili language');
console.log('5. Navigate to the screens shown in screenshots');
console.log('6. Verify that you see proper Swahili text instead of camelCase keys');

if (totalIssues === 0) {
  console.log('\n✨ Expected Result: All text should now appear in proper Swahili! ✨');
}
