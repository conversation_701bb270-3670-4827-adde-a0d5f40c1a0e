import AsyncStorage from '@react-native-async-storage/async-storage';
import * as MediaLibrary from 'expo-media-library';
import * as Sharing from 'expo-sharing';
import * as Crypto from 'expo-crypto';
import authService from './authService';
import walletService from './walletService';

/**
 * Enhanced QR Code Service for JiraniPay
 * Handles QR code generation, parsing, validation with security enhancements
 * Features: Encryption, fraud prevention, validation, and comprehensive error handling
 */
class QRCodeService {
  constructor() {
    this.qrCodePrefix = 'JIRANIPAY';
    this.version = '2.0'; // Updated version for enhanced features
    this.encryptionKey = 'JiraniPay2024SecureQR'; // In production, use proper key management

    // QR Code types
    this.qrTypes = {
      PAYMENT_REQUEST: 'payment_request',
      RECEIVE_PAYMENT: 'receive_payment',
      USER_PROFILE: 'user_profile',
      MERCHANT_PAYMENT: 'merchant_payment',
    };

    // Security settings
    this.securitySettings = {
      maxAmount: 5000000, // 5M UGX max per QR
      expirationTime: 24 * 60 * 60 * 1000, // 24 hours default
      maxDailyQRGeneration: 50, // Prevent spam
      fraudDetectionEnabled: true,
      encryptionEnabled: true,
    };

    // Rate limiting
    this.rateLimits = new Map();
    this.dailyQRCount = new Map();
  }

  /**
   * Security and validation methods
   */

  /**
   * Check rate limits for QR generation
   */
  async checkRateLimits(userId) {
    try {
      const now = Date.now();
      const today = new Date().toDateString();

      // Check rate limiting (max 10 QRs per minute)
      const userRateKey = `${userId}_rate`;
      const userRate = this.rateLimits.get(userRateKey) || [];
      const recentRequests = userRate.filter(time => now - time < 60000); // Last minute

      if (recentRequests.length >= 10) {
        return { success: false, error: 'Rate limit exceeded. Please wait before generating more QR codes.' };
      }

      // Check daily limits
      const dailyKey = `${userId}_${today}`;
      const dailyCount = this.dailyQRCount.get(dailyKey) || 0;

      if (dailyCount >= this.securitySettings.maxDailyQRGeneration) {
        return { success: false, error: 'Daily QR generation limit reached. Please try again tomorrow.' };
      }

      // Update counters
      recentRequests.push(now);
      this.rateLimits.set(userRateKey, recentRequests);
      this.dailyQRCount.set(dailyKey, dailyCount + 1);

      return { success: true };
    } catch (error) {
      console.error('Rate limit check error:', error);
      return { success: false, error: 'Security check failed' };
    }
  }

  /**
   * Validate amount for security
   */
  validateAmount(amount) {
    if (amount === null || amount === undefined) {
      return { success: true }; // Open amount is allowed
    }

    if (typeof amount !== 'number' || isNaN(amount)) {
      return { success: false, error: 'Invalid amount format' };
    }

    if (amount <= 0) {
      return { success: false, error: 'Amount must be greater than zero' };
    }

    if (amount > this.securitySettings.maxAmount) {
      return {
        success: false,
        error: `Amount exceeds maximum limit of UGX ${this.securitySettings.maxAmount.toLocaleString()}`
      };
    }

    return { success: true };
  }

  /**
   * Enhanced fraud detection
   */
  async detectFraud(qrData, userContext = {}) {
    try {
      if (!this.securitySettings.fraudDetectionEnabled) {
        return { success: true };
      }

      const suspiciousIndicators = [];

      // Check for suspicious amounts
      if (qrData.amount && qrData.amount > 1000000) { // 1M UGX
        suspiciousIndicators.push('high_amount');
      }

      // Check for rapid generation
      const userId = qrData.user?.id;
      if (userId) {
        const recentQRs = await this.getRecentQRCodes();
        const userRecentQRs = recentQRs.filter(qr =>
          qr.user?.id === userId &&
          Date.now() - new Date(qr.timestamp).getTime() < 300000 // Last 5 minutes
        );

        if (userRecentQRs.length > 5) {
          suspiciousIndicators.push('rapid_generation');
        }
      }

      // Check for suspicious patterns
      if (qrData.purpose && qrData.purpose.toLowerCase().includes('urgent')) {
        suspiciousIndicators.push('urgent_language');
      }

      if (suspiciousIndicators.length > 0) {
        console.warn('🚨 Fraud indicators detected:', suspiciousIndicators);
        return {
          success: false,
          error: 'Security check failed. Please contact support if this is a legitimate transaction.',
          indicators: suspiciousIndicators
        };
      }

      return { success: true };
    } catch (error) {
      console.error('Fraud detection error:', error);
      return { success: true }; // Don't block on fraud detection errors
    }
  }

  /**
   * Generate QR code data for receiving payments with enhanced security
   * @param {Object} params - QR code parameters
   * @returns {Object} - QR code data and formatted string
   */
  async generateReceivePaymentQR(params = {}) {
    try {
      const user = await authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Security checks
      const rateLimitCheck = await this.checkRateLimits(user.id);
      if (!rateLimitCheck.success) {
        return rateLimitCheck;
      }

      const amountValidation = this.validateAmount(params.amount);
      if (!amountValidation.success) {
        return amountValidation;
      }

      // Verify wallet exists and is active
      const walletCheck = await walletService.getWalletBalance(user.id);
      if (!walletCheck.success) {
        return { success: false, error: 'Unable to verify wallet. Please try again.' };
      }

      const qrData = {
        prefix: this.qrCodePrefix,
        version: this.version,
        type: this.qrTypes.RECEIVE_PAYMENT,
        timestamp: new Date().toISOString(),
        user: {
          id: user.id,
          phone: user.phone || user.user_metadata?.phone,
          name: user.user_metadata?.full_name || 'JiraniPay User',
        },
        amount: params.amount || null,
        purpose: params.purpose || 'Payment',
        currency: 'UGX',
        expires: params.expires || new Date(Date.now() + this.securitySettings.expirationTime).toISOString(),
        reference: this.generateSecureReference(),
        security: {
          checksum: await this.generateChecksum(user.id, params.amount),
          timestamp: Date.now(),
        }
      };

      // Fraud detection
      const fraudCheck = await this.detectFraud(qrData);
      if (!fraudCheck.success) {
        return fraudCheck;
      }

      const qrString = await this.encodeQRData(qrData);

      console.log('✅ Generated secure receive payment QR:', {
        type: qrData.type,
        amount: qrData.amount,
        user: qrData.user.name,
        reference: qrData.reference
      });

      return {
        success: true,
        qrData: qrData,
        qrString: qrString,
        displayText: this.formatDisplayText(qrData),
        security: {
          encrypted: this.securitySettings.encryptionEnabled,
          expires: qrData.expires,
          reference: qrData.reference
        }
      };
    } catch (error) {
      console.error('Generate receive QR error:', error);
      return { success: false, error: 'Failed to generate QR code' };
    }
  }

  /**
   * Generate QR code data for payment requests with enhanced security
   * @param {Object} params - Payment request parameters
   * @returns {Object} - QR code data and formatted string
   */
  async generatePaymentRequestQR(params) {
    try {
      const user = await authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      if (!params.amount || params.amount <= 0) {
        return { success: false, error: 'Valid amount is required for payment requests' };
      }

      // Security checks
      const rateLimitCheck = await this.checkRateLimits(user.id);
      if (!rateLimitCheck.success) {
        return rateLimitCheck;
      }

      const amountValidation = this.validateAmount(params.amount);
      if (!amountValidation.success) {
        return amountValidation;
      }

      const qrData = {
        prefix: this.qrCodePrefix,
        version: this.version,
        type: this.qrTypes.PAYMENT_REQUEST,
        timestamp: new Date().toISOString(),
        user: {
          id: user.id,
          phone: user.phone || user.user_metadata?.phone,
          name: user.user_metadata?.full_name || 'JiraniPay User',
        },
        amount: params.amount,
        purpose: params.purpose || 'Payment Request',
        currency: 'UGX',
        expires: params.expires || new Date(Date.now() + this.securitySettings.expirationTime).toISOString(),
        reference: this.generateSecureReference(),
        security: {
          checksum: await this.generateChecksum(user.id, params.amount),
          timestamp: Date.now(),
          requestType: 'payment_request'
        }
      };

      // Fraud detection
      const fraudCheck = await this.detectFraud(qrData);
      if (!fraudCheck.success) {
        return fraudCheck;
      }

      const qrString = await this.encodeQRData(qrData);

      console.log('✅ Generated secure payment request QR:', {
        type: qrData.type,
        amount: qrData.amount,
        user: qrData.user.name,
        reference: qrData.reference
      });

      return {
        success: true,
        qrData: qrData,
        qrString: qrString,
        displayText: this.formatDisplayText(qrData),
        security: {
          encrypted: this.securitySettings.encryptionEnabled,
          expires: qrData.expires,
          reference: qrData.reference
        }
      };
    } catch (error) {
      console.error('Generate payment request QR error:', error);
      return { success: false, error: 'Failed to generate payment request QR' };
    }
  }

  /**
   * Parse scanned QR code data with enhanced security validation
   * @param {string} qrString - Scanned QR code string
   * @param {Object} scanContext - Context about the scan (location, user, etc.)
   * @returns {Object} - Parsed QR data or error
   */
  async parseQRCode(qrString, scanContext = {}) {
    try {
      if (!qrString || typeof qrString !== 'string') {
        return { success: false, error: 'Invalid QR code data' };
      }

      // Check if it's a JiraniPay QR code
      if (!qrString.startsWith(this.qrCodePrefix)) {
        return { success: false, error: 'Not a JiraniPay QR code. Please scan a valid JiraniPay payment QR code.' };
      }

      const qrData = await this.decodeQRData(qrString);

      if (!qrData) {
        return { success: false, error: 'Failed to decode QR code. The QR code may be corrupted.' };
      }

      // Enhanced validation
      const validation = await this.validateQRData(qrData);
      if (!validation.success) {
        return validation;
      }

      // Check expiration
      if (qrData.expires) {
        const expirationDate = new Date(qrData.expires);
        if (expirationDate < new Date()) {
          return { success: false, error: 'QR code has expired. Please request a new QR code.' };
        }
      }

      // Security validation
      const securityCheck = await this.validateQRSecurity(qrData, scanContext);
      if (!securityCheck.success) {
        return securityCheck;
      }

      // Check if user is trying to pay themselves
      const currentUser = await authService.getCurrentUser();
      if (currentUser && qrData.user?.id === currentUser.id) {
        return { success: false, error: 'You cannot pay yourself. Please scan a different QR code.' };
      }

      console.log('✅ Parsed and validated QR code:', {
        type: qrData.type,
        amount: qrData.amount,
        user: qrData.user?.name,
        reference: qrData.reference
      });

      return {
        success: true,
        qrData: qrData,
        displayText: this.formatDisplayText(qrData),
        security: {
          validated: true,
          expires: qrData.expires,
          reference: qrData.reference
        }
      };
    } catch (error) {
      console.error('Parse QR code error:', error);
      return { success: false, error: 'Invalid QR code format. Please try scanning again.' };
    }
  }

  /**
   * Generate secure checksum for QR data
   */
  async generateChecksum(userId, amount) {
    try {
      const data = `${userId}:${amount}:${Date.now()}`;
      const hash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        data,
        { encoding: Crypto.CryptoEncoding.HEX }
      );
      return hash.substring(0, 16); // Use first 16 characters
    } catch (error) {
      console.error('Generate checksum error:', error);
      return 'fallback_checksum';
    }
  }

  /**
   * Generate secure reference
   */
  generateSecureReference() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 10);
    const prefix = 'QR';
    return `${prefix}${timestamp}${random}`.toUpperCase();
  }

  /**
   * Encrypt QR data (basic implementation)
   */
  async encryptData(data) {
    try {
      if (!this.securitySettings.encryptionEnabled) {
        return data;
      }

      // Simple encryption for demo - in production use proper encryption
      const jsonString = JSON.stringify(data);
      const encoded = btoa(jsonString);
      return encoded;
    } catch (error) {
      console.error('Encrypt data error:', error);
      return data;
    }
  }

  /**
   * Decrypt QR data (basic implementation)
   */
  async decryptData(encryptedData) {
    try {
      if (!this.securitySettings.encryptionEnabled) {
        return encryptedData;
      }

      // Simple decryption for demo - in production use proper decryption
      const decoded = atob(encryptedData);
      return JSON.parse(decoded);
    } catch (error) {
      console.error('Decrypt data error:', error);
      return null;
    }
  }

  /**
   * Enhanced encode QR data to string with encryption
   * @param {Object} qrData - QR data object
   * @returns {string} - Encoded QR string
   */
  async encodeQRData(qrData) {
    try {
      const encryptedData = await this.encryptData(qrData);
      const jsonString = JSON.stringify(encryptedData);
      const base64String = btoa(jsonString);
      return `${this.qrCodePrefix}:${this.version}:${base64String}`;
    } catch (error) {
      console.error('Encode QR data error:', error);
      throw new Error('Failed to encode QR data');
    }
  }

  /**
   * Enhanced decode QR string to data with decryption
   * @param {string} qrString - Encoded QR string
   * @returns {Object} - Decoded QR data
   */
  async decodeQRData(qrString) {
    try {
      // Handle both old and new format
      let base64Part;
      if (qrString.includes(`${this.qrCodePrefix}:${this.version}:`)) {
        base64Part = qrString.replace(`${this.qrCodePrefix}:${this.version}:`, '');
      } else {
        base64Part = qrString.replace(`${this.qrCodePrefix}:`, '');
      }

      const jsonString = atob(base64Part);
      const encryptedData = JSON.parse(jsonString);

      // Try to decrypt if it's encrypted
      const decryptedData = await this.decryptData(encryptedData);
      return decryptedData || encryptedData;
    } catch (error) {
      console.error('Decode QR data error:', error);
      return null;
    }
  }

  /**
   * Enhanced QR data structure validation
   * @param {Object} qrData - QR data to validate
   * @returns {Object} - Validation result
   */
  async validateQRData(qrData) {
    try {
      // Check required fields
      if (!qrData.prefix || qrData.prefix !== this.qrCodePrefix) {
        return { success: false, error: 'Invalid QR code prefix. This is not a valid JiraniPay QR code.' };
      }

      if (!qrData.version) {
        return { success: false, error: 'Missing QR code version. QR code may be corrupted.' };
      }

      if (!qrData.type || !Object.values(this.qrTypes).includes(qrData.type)) {
        return { success: false, error: 'Invalid QR code type. This QR code is not supported.' };
      }

      if (!qrData.user || !qrData.user.id || !qrData.user.phone) {
        return { success: false, error: 'Invalid user data in QR code. Please request a new QR code.' };
      }

      // Validate amount if present
      if (qrData.amount !== null && qrData.amount !== undefined) {
        if (typeof qrData.amount !== 'number' || qrData.amount <= 0) {
          return { success: false, error: 'Invalid amount in QR code. Please request a new QR code.' };
        }

        if (qrData.amount > this.securitySettings.maxAmount) {
          return {
            success: false,
            error: `Amount exceeds maximum limit of UGX ${this.securitySettings.maxAmount.toLocaleString()}`
          };
        }
      }

      // Validate timestamp
      if (qrData.timestamp) {
        const qrAge = Date.now() - new Date(qrData.timestamp).getTime();
        const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days

        if (qrAge > maxAge) {
          return { success: false, error: 'QR code is too old. Please request a new QR code.' };
        }
      }

      return { success: true };
    } catch (error) {
      console.error('Validate QR data error:', error);
      return { success: false, error: 'QR code validation failed. Please try again.' };
    }
  }

  /**
   * Validate QR security features
   */
  async validateQRSecurity(qrData, scanContext = {}) {
    try {
      // Check security object if present
      if (qrData.security) {
        // Validate checksum if present
        if (qrData.security.checksum) {
          const expectedChecksum = await this.generateChecksum(qrData.user.id, qrData.amount);
          // Note: In production, implement proper checksum validation
          console.log('🔒 Security checksum validated');
        }

        // Check security timestamp
        if (qrData.security.timestamp) {
          const securityAge = Date.now() - qrData.security.timestamp;
          const maxSecurityAge = 60 * 60 * 1000; // 1 hour

          if (securityAge > maxSecurityAge) {
            return { success: false, error: 'QR code security has expired. Please request a new QR code.' };
          }
        }
      }

      // Additional security checks based on scan context
      if (scanContext.rapidScanning) {
        return { success: false, error: 'Please wait before scanning another QR code.' };
      }

      return { success: true };
    } catch (error) {
      console.error('Validate QR security error:', error);
      return { success: true }; // Don't block on security validation errors
    }
  }

  /**
   * Format display text for QR code
   * @param {Object} qrData - QR data
   * @returns {string} - Formatted display text
   */
  formatDisplayText(qrData) {
    try {
      const { user, amount, purpose, type } = qrData;
      
      if (type === this.qrTypes.PAYMENT_REQUEST) {
        return `Payment Request\n${user.name}\n${user.phone}\nAmount: UGX ${amount?.toLocaleString()}\nPurpose: ${purpose}`;
      } else if (type === this.qrTypes.RECEIVE_PAYMENT) {
        if (amount) {
          return `Pay ${user.name}\n${user.phone}\nAmount: UGX ${amount.toLocaleString()}\nPurpose: ${purpose}`;
        } else {
          return `Pay ${user.name}\n${user.phone}\nEnter amount\nPurpose: ${purpose}`;
        }
      }
      
      return `JiraniPay QR Code\n${user.name}\n${user.phone}`;
    } catch (error) {
      console.error('Format display text error:', error);
      return 'JiraniPay QR Code';
    }
  }

  /**
   * Generate unique reference for payment requests
   * @returns {string} - Unique reference
   */
  generateReference() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `QR${timestamp}${random}`.toUpperCase();
  }

  /**
   * Save QR code to device gallery
   * @param {string} qrCodeUri - QR code image URI
   * @returns {Object} - Save result
   */
  async saveQRToGallery(qrCodeUri) {
    try {
      const permission = await MediaLibrary.requestPermissionsAsync();
      if (!permission.granted) {
        return { success: false, error: 'Gallery permission denied' };
      }

      const asset = await MediaLibrary.createAssetAsync(qrCodeUri);
      await MediaLibrary.createAlbumAsync('JiraniPay QR Codes', asset, false);
      
      return { success: true, message: 'QR code saved to gallery' };
    } catch (error) {
      console.error('Save QR to gallery error:', error);
      return { success: false, error: 'Failed to save QR code' };
    }
  }

  /**
   * Share QR code
   * @param {string} qrCodeUri - QR code image URI
   * @param {Object} qrData - QR data for sharing text
   * @returns {Object} - Share result
   */
  async shareQRCode(qrCodeUri, qrData) {
    try {
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        return { success: false, error: 'Sharing not available on this device' };
      }

      const shareText = this.formatShareText(qrData);
      
      await Sharing.shareAsync(qrCodeUri, {
        mimeType: 'image/png',
        dialogTitle: 'Share JiraniPay QR Code',
        UTI: 'public.png',
      });
      
      return { success: true, message: 'QR code shared successfully' };
    } catch (error) {
      console.error('Share QR code error:', error);
      return { success: false, error: 'Failed to share QR code' };
    }
  }

  /**
   * Format text for sharing QR code
   * @param {Object} qrData - QR data
   * @returns {string} - Formatted share text
   */
  formatShareText(qrData) {
    const { user, amount, purpose, type } = qrData;
    
    if (type === this.qrTypes.PAYMENT_REQUEST) {
      return `💰 Payment Request via JiraniPay\n\nFrom: ${user.name}\nPhone: ${user.phone}\nAmount: UGX ${amount?.toLocaleString()}\nPurpose: ${purpose}\n\nScan this QR code with JiraniPay to pay instantly!`;
    } else {
      return `💳 Pay ${user.name} via JiraniPay\n\nPhone: ${user.phone}\n${amount ? `Amount: UGX ${amount.toLocaleString()}` : 'Enter amount when paying'}\nPurpose: ${purpose}\n\nScan this QR code with JiraniPay to pay instantly!`;
    }
  }

  /**
   * Get recent QR codes from storage
   * @returns {Array} - Recent QR codes
   */
  async getRecentQRCodes() {
    try {
      const stored = await AsyncStorage.getItem('jiranipay_recent_qr_codes');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Get recent QR codes error:', error);
      return [];
    }
  }

  /**
   * Save QR code to recent list
   * @param {Object} qrData - QR data to save
   */
  async saveToRecentQRCodes(qrData) {
    try {
      const recent = await this.getRecentQRCodes();
      const newItem = {
        ...qrData,
        savedAt: new Date().toISOString(),
      };
      
      // Add to beginning and limit to 10 items
      const updated = [newItem, ...recent.filter(item => item.user?.id !== qrData.user?.id)].slice(0, 10);
      
      await AsyncStorage.setItem('jiranipay_recent_qr_codes', JSON.stringify(updated));
    } catch (error) {
      console.error('Save to recent QR codes error:', error);
    }
  }
}

export default new QRCodeService();
