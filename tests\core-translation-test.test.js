/**
 * Core Translation System Test
 * 
 * Tests the fundamental translation system functionality
 * to verify if the basic issues are resolved
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 CORE TRANSLATION SYSTEM TEST\n');

// Test 1: File Import Test
console.log('1️⃣ TRANSLATION FILE IMPORT TEST:');
try {
  delete require.cache[require.resolve('../locales/en.js')];
  delete require.cache[require.resolve('../locales/sw.js')];
  
  const enTranslations = require('../locales/en.js').default;
  const swTranslations = require('../locales/sw.js').default;
  
  console.log('   ✅ English file imported successfully');
  console.log('   ✅ Swahili file imported successfully');
  console.log(`   📊 English sections: ${Object.keys(enTranslations).length}`);
  console.log(`   📊 Swahili sections: ${Object.keys(swTranslations).length}`);
  
  // Test 2: Critical Keys Test (from screenshots)
  console.log('\n2️⃣ CRITICAL KEYS TEST (Screenshot Keys):');
  
  const criticalKeys = [
    'editProfile',
    'save',
    'fullName',
    'emailAddress',
    'phoneNumber',
    'accountVerification',
    'privacyAndData',
    'dataConsent',
    'essentialServices'
  ];
  
  let englishMissing = 0;
  let swahiliMissing = 0;
  
  criticalKeys.forEach(key => {
    const enHasKey = enTranslations[key] !== undefined;
    const swHasKey = swTranslations[key] !== undefined;
    
    if (enHasKey && swHasKey) {
      console.log(`   ✅ ${key}: EN="${enTranslations[key]}" | SW="${swTranslations[key]}"`);
    } else {
      if (!enHasKey) {
        console.log(`   ❌ ${key}: MISSING IN ENGLISH`);
        englishMissing++;
      }
      if (!swHasKey) {
        console.log(`   ❌ ${key}: MISSING IN SWAHILI`);
        swahiliMissing++;
      }
    }
  });
  
  // Test 3: Translation Function Simulation
  console.log('\n3️⃣ TRANSLATION FUNCTION SIMULATION:');
  
  const simulateTranslation = (translations, key) => {
    // Simulate how the app's t() function works
    const keys = key.split('.');
    let translation = translations;
    
    for (const k of keys) {
      translation = translation?.[k];
      if (!translation) break;
    }
    
    return translation || key; // Return key if translation not found
  };
  
  const testKeys = [
    'editProfile',
    'common.save',
    'profile.editProfile',
    'accountVerification',
    'privacyAndData'
  ];
  
  testKeys.forEach(key => {
    const enResult = simulateTranslation(enTranslations, key);
    const swResult = simulateTranslation(swTranslations, key);
    
    const enWorking = enResult !== key;
    const swWorking = swResult !== key;
    
    console.log(`   ${enWorking && swWorking ? '✅' : '❌'} ${key}:`);
    console.log(`      EN: "${enResult}" ${enWorking ? '✅' : '❌'}`);
    console.log(`      SW: "${swResult}" ${swWorking ? '✅' : '❌'}`);
  });
  
  // Test 4: i18n Configuration Test
  console.log('\n4️⃣ I18N CONFIGURATION TEST:');
  
  try {
    delete require.cache[require.resolve('../utils/i18n.js')];
    const i18nModule = require('../utils/i18n.js');
    
    console.log('   ✅ i18n module imported successfully');
    console.log(`   📊 Supported languages: ${Object.keys(i18nModule.SUPPORTED_LANGUAGES || {}).length}`);
    console.log(`   📊 Available translations: ${Object.keys(i18nModule.TRANSLATIONS || {}).length}`);
    
    // Test if our translations are properly loaded
    const translations = i18nModule.TRANSLATIONS;
    if (translations && translations.en && translations.sw) {
      console.log('   ✅ English translations loaded in i18n');
      console.log('   ✅ Swahili translations loaded in i18n');
      
      // Test critical keys in i18n
      const enFromI18n = translations.en.editProfile;
      const swFromI18n = translations.sw.editProfile;
      
      if (enFromI18n && swFromI18n) {
        console.log(`   ✅ Critical key test: EN="${enFromI18n}" | SW="${swFromI18n}"`);
      } else {
        console.log('   ❌ Critical keys missing in i18n translations');
      }
    } else {
      console.log('   ❌ Translations not properly loaded in i18n');
    }
    
  } catch (error) {
    console.log('   ❌ i18n module import failed:', error.message);
  }
  
  // Summary
  console.log('\n📊 TEST SUMMARY:');
  console.log('================');
  console.log(`Critical keys missing in English: ${englishMissing}`);
  console.log(`Critical keys missing in Swahili: ${swahiliMissing}`);
  
  const totalCriticalIssues = englishMissing + swahiliMissing;
  
  if (totalCriticalIssues === 0) {
    console.log('\n🎉 CORE TRANSLATION SYSTEM: ✅ WORKING');
    console.log('   All critical keys are present in both languages');
    console.log('   Translation system should work for basic functionality');
  } else {
    console.log('\n⚠️  CORE TRANSLATION SYSTEM: 🔄 PARTIAL');
    console.log(`   ${totalCriticalIssues} critical issues remain`);
  }
  
  console.log('\n🎯 RECOMMENDATIONS:');
  console.log('===================');
  
  if (totalCriticalIssues === 0) {
    console.log('✅ Core translation system is ready');
    console.log('✅ Test the app with language switching');
    console.log('✅ If issues persist, check app code usage of translation keys');
  } else {
    console.log('❌ Fix remaining critical key mismatches first');
    console.log('❌ Ensure both files have identical critical keys');
  }
  
} catch (error) {
  console.log('❌ Critical error in translation system test:', error.message);
  console.log('   Translation files may have syntax errors or structural issues');
}
