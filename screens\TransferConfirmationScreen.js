import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  Image,
  Modal,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import * as LocalAuthentication from 'expo-local-authentication';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import sendMoneyService from '../services/sendMoneyService';
import authService from '../services/authService';

/**
 * TransferConfirmationScreen - Final confirmation and processing
 * Features PIN verification, biometric auth, and transfer execution
 */
const TransferConfirmationScreen = ({ navigation, route }) => {
  // Use theme and language contexts
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const { recipient, amount, purpose, feeCalculation, validation } = route.params;
  const [loading, setLoading] = useState(false);
  const [showPinModal, setShowPinModal] = useState(false);
  const [pin, setPin] = useState('');
  const [user, setUser] = useState(null);
  const [biometricAvailable, setBiometricAvailable] = useState(false);

  useEffect(() => {
    loadInitialData();
    checkBiometricAvailability();
  }, []);

  const loadInitialData = async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      if (currentUser.success) {
        setUser(currentUser.user);
      }
    } catch (error) {
      console.error('Load initial data error:', error);
    }
  };

  const checkBiometricAvailability = async () => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      // Make biometric available for all transfers, not just high-value ones
      setBiometricAvailable(hasHardware && isEnrolled);
    } catch (error) {
      console.error('Biometric check error:', error);
    }
  };

  const handleBiometricAuth = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Use LocalAuthentication directly for better control
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to confirm transfer',
        subtitle: `Confirm transfer of UGX ${amount.toLocaleString()} to ${recipient.name}`,
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use PIN',
        disableDeviceFallback: false,
      });

      if (result.success) {
        processTransfer(true);
      } else {
        Alert.alert(t('transfer.authenticationFailed'), t('transfer.biometricAuthFailedMessage'));
      }
    } catch (error) {
      console.error('Biometric auth error:', error);
      Alert.alert(t('common.errorTitle'), t('transfer.authenticationFailedTryAgain'));
    }
  };

  const handlePinAuth = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowPinModal(true);
  };

  const verifyPin = async () => {
    if (pin.length !== 4) {
      Alert.alert(t('transfer.invalidPIN'), t('transfer.pleaseEnter4DigitPIN'));
      return;
    }

    try {
      // Development mode - accept any 4-digit PIN
      if (process.env.NODE_ENV === 'development' || __DEV__) {
        console.log('🔧 Development Mode: PIN verification bypassed');
        setShowPinModal(false);
        setPin('');
        processTransfer(false);
        return;
      }

      // Production implementation would verify actual PIN
      Alert.alert(t('transfer.pinVerification'), t('transfer.pinVerificationUnavailable'));
    } catch (error) {
      console.error('PIN verification error:', error);
      Alert.alert(t('common.errorTitle'), t('transfer.pinVerificationFailed'));
    }
  };

  const processTransfer = async (biometricAuth = false) => {
    try {
      setLoading(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

      const transferData = {
        userId: user?.id,
        recipient: recipient,
        amount: amount,
        purpose: purpose,
        pin: pin,
        biometricAuth: biometricAuth,
      };

      const result = await sendMoneyService.processTransfer(transferData);

      if (result.success) {
        // Navigate to success screen
        navigation.replace('TransferSuccess', {
          transaction: result.transaction,
          reference: result.reference,
          message: result.message,
        });
      } else {
        Alert.alert(t('transfer.transferFailed'), result.error || t('transfer.transferCouldNotBeCompleted'));
      }
    } catch (error) {
      console.error('Transfer processing error:', error);
      Alert.alert(t('common.errorTitle'), t('transfer.transferFailedTryAgain'));
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value) => {
    return `UGX ${parseFloat(value).toLocaleString()}`;
  };

  const renderRecipientSummary = () => (
    <View style={styles.summaryCard}>
      <Text style={styles.cardTitle}>{t('transfer.transferSummary')}</Text>
      
      <View style={styles.recipientInfo}>
        {recipient.image ? (
          <Image source={{ uri: recipient.image.uri }} style={styles.recipientAvatar} />
        ) : (
          <View style={[styles.recipientAvatar, styles.recipientAvatarPlaceholder]}>
            <Text style={styles.recipientAvatarText}>
              {recipient.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
        
        <View style={styles.recipientDetails}>
          <Text style={styles.recipientName}>{recipient.name}</Text>
          <Text style={styles.recipientPhone}>{recipient.phoneNumber}</Text>
          {recipient.provider && (
            <View style={[
              styles.providerBadge,
              { backgroundColor: recipient.provider.color + '20' }
            ]}>
              <Text style={[
                styles.providerText,
                { color: recipient.provider.color }
              ]}>
                {recipient.provider.name}
              </Text>
            </View>
          )}
        </View>
        
        <View style={styles.verifiedBadge}>
          <Ionicons name="checkmark-circle" size={24} color={theme.colors.success} />
        </View>
      </View>
    </View>
  );

  const renderTransferDetails = () => (
    <View style={styles.detailsCard}>
      <Text style={styles.cardTitle}>{t('transfer.transferDetails')}</Text>
      
      <View style={styles.detailsContent}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>{t('transfer.amount')}</Text>
          <Text style={styles.detailValue}>{formatCurrency(amount)}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>{t('transfer.transferFee')}</Text>
          <Text style={[
            styles.detailValue,
            feeCalculation.freeTransfer && styles.freeText
          ]}>
            {feeCalculation.freeTransfer ? 'FREE' : formatCurrency(feeCalculation.fee)}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>{t('transfer.purpose')}</Text>
          <Text style={styles.detailValue}>{purpose}</Text>
        </View>
        
        <View style={[styles.detailRow, styles.totalRow]}>
          <Text style={styles.totalLabel}>{t('transfer.totalAmount')}</Text>
          <Text style={styles.totalValue}>{formatCurrency(feeCalculation.total)}</Text>
        </View>
      </View>

      {feeCalculation.freeTransfer && (
        <View style={styles.freeTransferBadge}>
          <Ionicons name="gift" size={16} color={theme.colors.success} />
          <Text style={styles.freeTransferText}>{t('transfer.freeTransfer')}</Text>
        </View>
      )}
    </View>
  );

  const renderAuthOptions = () => (
    <View style={styles.authCard}>
      <Text style={styles.cardTitle}>{t('transfer.confirmTransfer')}</Text>
      <Text style={styles.authSubtitle}>
        {t('transfer.chooseAuthenticationMethod')}
      </Text>

      <View style={styles.authButtons}>
        {biometricAvailable && (
          <TouchableOpacity
            style={[styles.authButton, styles.primaryAuthButton]}
            onPress={handleBiometricAuth}
            disabled={loading}
            activeOpacity={0.8}
          >
            <View style={[styles.authButtonIcon, styles.primaryAuthIcon]}>
              <Ionicons name="finger-print" size={24} color={theme.colors.white} />
            </View>
            <View style={styles.authButtonContent}>
              <Text style={[styles.authButtonTitle, styles.primaryAuthTitle]}>
                Biometric Authentication
              </Text>
              <Text style={[styles.authButtonSubtitle, styles.primaryAuthSubtitle]}>
                Quick & secure with fingerprint or face
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.white} />
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={styles.authButton}
          onPress={handlePinAuth}
          disabled={loading}
          activeOpacity={0.8}
        >
          <View style={styles.authButtonIcon}>
            <Ionicons name="keypad" size={24} color={theme.colors.primary} />
          </View>
          <View style={styles.authButtonContent}>
            <Text style={styles.authButtonTitle}>{t('transfer.pinAuthentication')}</Text>
            <Text style={styles.authButtonSubtitle}>{t('transfer.enterYour4DigitPIN')}</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderPinModal = () => (
    <Modal
      visible={showPinModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowPinModal(false)}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity
            onPress={() => {
              setShowPinModal(false);
              setPin('');
            }}
            style={styles.modalCloseButton}
          >
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.modalTitle}>{t('transfer.enterPIN')}</Text>
          <View style={styles.modalSpacer} />
        </View>

        <View style={styles.modalContent}>
          <Text style={styles.pinInstructions}>
            Enter your 4-digit PIN to confirm this transfer
          </Text>
          
          <View style={styles.pinInputContainer}>
            <TextInput
              style={styles.pinInput}
              value={pin}
              onChangeText={setPin}
              placeholder={t('transfer.enterPINPlaceholder')}
              placeholderTextColor={theme.colors.placeholder}
              keyboardType="numeric"
              maxLength={4}
              secureTextEntry
              autoFocus
            />
          </View>

          <TouchableOpacity
            style={[
              styles.verifyButton,
              pin.length !== 4 && styles.verifyButtonDisabled
            ]}
            onPress={verifyPin}
            disabled={pin.length !== 4}
            activeOpacity={0.8}
          >
            <Text style={styles.verifyButtonText}>{t('transfer.verifyPIN')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{t('transfer.confirmTransfer')}</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderRecipientSummary()}
        {renderTransferDetails()}
        {renderAuthOptions()}
      </ScrollView>

      {renderPinModal()}
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 20,
  },
  summaryCard: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.primary + '20',
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  recipientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  recipientAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  recipientAvatarPlaceholder: {
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  recipientAvatarText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  recipientDetails: {
    flex: 1,
  },
  recipientName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  recipientPhone: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 6,
  },
  providerBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  providerText: {
    fontSize: 12,
    fontWeight: '600',
  },
  verifiedBadge: {
    padding: 4,
  },
  detailsCard: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
  },
  detailsContent: {
    gap: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  freeText: {
    color: theme.colors.success,
  },
  totalRow: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  freeTransferBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.success + '10',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginTop: 12,
    gap: 6,
  },
  freeTransferText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.success,
  },
  authCard: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
  },
  authSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 20,
    lineHeight: 20,
  },
  authButtons: {
    gap: 12,
  },
  authButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
    gap: 12,
  },
  authButtonIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary + '10',
    justifyContent: 'center',
    alignItems: 'center',
  },
  authButtonContent: {
    flex: 1,
  },
  authButtonTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  authButtonSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  primaryAuthButton: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  primaryAuthIcon: {
    backgroundColor: theme.colors.primary + 'DD',
  },
  primaryAuthTitle: {
    color: theme.colors.white,
  },
  primaryAuthSubtitle: {
    color: theme.colors.white + 'CC',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalCloseButton: {
    padding: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  modalSpacer: {
    width: 40,
  },
  modalContent: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  pinInstructions: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  pinInputContainer: {
    marginBottom: 32,
  },
  pinInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 20,
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    letterSpacing: 8,
    borderWidth: 2,
    borderColor: theme.colors.primary,
  },
  verifyButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  verifyButtonDisabled: {
    backgroundColor: theme.colors.disabled,
  },
  verifyButtonText: {
    fontSize: 17,
    fontWeight: 'bold',
    color: theme.colors.white,
  },
});

export default TransferConfirmationScreen;
