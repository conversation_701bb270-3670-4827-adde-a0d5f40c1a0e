/**
 * ProfileScreen i18n Verification Test
 * 
 * Tests to verify that ProfileScreen.js is properly internationalized
 * and all user-facing strings use translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Verifying ProfileScreen.js i18n Implementation\n');

// Read the ProfileScreen.js file
const profileScreenPath = path.join(__dirname, '../screens/ProfileScreen.js');
const profileScreenContent = fs.readFileSync(profileScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = profileScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = profileScreenContent.includes('const { currentLanguage, changeLanguage, t }');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded user-facing strings (should not exist)
console.log('\n✅ Test 3: No hardcoded user-facing strings');
const potentialHardcodedStrings = [
  'Profile',
  'Edit Profile',
  'Account Verification',
  'Security Settings',
  'Privacy and Data',
  'Dark Mode',
  'Notifications',
  'Language',
  'Currency',
  'FAQ',
  'Contact Us',
  'Logout',
  'Wallet Balance',
  'Transactions',
  'Account Level'
];

let hardcodedFound = [];
potentialHardcodedStrings.forEach(str => {
  // Check if the string appears as hardcoded (not in translation key)
  const lines = profileScreenContent.split('\n');
  let foundAsHardcoded = false;
  lines.forEach(line => {
    if ((line.includes(`'${str}'`) || line.includes(`"${str}"`)) && 
        !line.includes('t(') && 
        !line.trim().startsWith('//') && 
        !line.trim().startsWith('*') &&
        !line.includes('console.') &&
        !line.includes('navigation.navigate')) {
      foundAsHardcoded = true;
    }
  });
  if (foundAsHardcoded) {
    hardcodedFound.push(str);
  }
});

if (hardcodedFound.length === 0) {
  console.log('   No hardcoded user-facing strings found: ✅ PASS');
} else {
  console.log('   Hardcoded user-facing strings found: ❌ FAIL');
  hardcodedFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 4: Check for proper profile translation key usage
console.log('\n✅ Test 4: Profile translation keys usage');
const profileTranslationKeys = [
  't(\'profile.editProfile\')',
  't(\'profile.accountVerification\')',
  't(\'profile.securitySettings\')',
  't(\'profile.privacyAndData\')',
  't(\'profile.darkMode\')',
  't(\'profile.notifications\')',
  't(\'profile.currency\')',
  't(\'profile.faq\')',
  't(\'profile.logout\')',
  't(\'profile.walletBalance\')',
  't(\'profile.transactions\')',
  't(\'profile.accountLevel\')'
];

let profileKeysFound = 0;
profileTranslationKeys.forEach(key => {
  if (profileScreenContent.includes(key)) {
    profileKeysFound++;
  }
});

console.log(`   Profile translation keys found: ${profileKeysFound}/${profileTranslationKeys.length} ${profileKeysFound === profileTranslationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 5: Check that required profile keys exist in en.js
console.log('\n✅ Test 5: Profile keys in en.js');
const requiredProfileKeys = [
  'editProfile:',
  'accountVerification:',
  'securitySettings:',
  'privacyAndData:',
  'darkMode:',
  'notifications:',
  'currency:',
  'faq:',
  'logout:',
  'walletBalance:',
  'transactions:',
  'accountLevel:'
];

let profileKeysInTranslations = 0;
requiredProfileKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    profileKeysInTranslations++;
  }
});

console.log(`   Required profile keys in en.js: ${profileKeysInTranslations}/${requiredProfileKeys.length} ${profileKeysInTranslations === requiredProfileKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check for Alert.alert with proper translation keys
console.log('\n✅ Test 6: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = profileScreenContent.match(alertPattern);
const totalAlerts = (profileScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check for language and currency selector integration
console.log('\n✅ Test 7: Language and currency selector integration');
const hasLanguageSelector = profileScreenContent.includes('LanguageSelector') && 
                            profileScreenContent.includes('onLanguageChange={handleLanguageChange}');
const hasCurrencySelector = profileScreenContent.includes('CurrencySelector') && 
                            profileScreenContent.includes('onCurrencyChange={handleCurrencyChange}');
console.log(`   Language and currency selectors integrated: ${hasLanguageSelector && hasCurrencySelector ? '✅ PASS' : '❌ FAIL'}`);

// Test 8: Check for responsive text usage
console.log('\n✅ Test 8: Responsive text usage');
const hasResponsiveText = profileScreenContent.includes('ResponsiveText') && 
                          profileScreenContent.includes('t(\'profile.appPreferences\')');
console.log(`   ResponsiveText component used: ${hasResponsiveText ? '✅ PASS' : '❌ FAIL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 8;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (hardcodedFound.length === 0) passedTests++;
if (profileKeysFound === profileTranslationKeys.length) passedTests++;
if (profileKeysInTranslations === requiredProfileKeys.length) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (hasLanguageSelector && hasCurrencySelector) passedTests++;
if (hasResponsiveText) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 ProfileScreen.js i18n implementation: ✅ VERIFIED COMPLETE');
  console.log('   All user-facing strings use proper translation keys and the screen is fully internationalized!');
} else {
  console.log('\n⚠️  ProfileScreen.js i18n implementation: 🔄 NEEDS ATTENTION');
  console.log('   Some issues need to be addressed.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test profile screen with different languages');
console.log('2. Verify language and currency selectors work correctly');
console.log('3. Test all profile management features');
console.log('4. Phase 1 (Week 1-2) implementation complete!');
