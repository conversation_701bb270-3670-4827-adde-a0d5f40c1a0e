/**
 * BillPaymentScreen i18n Implementation Test
 * 
 * Tests to verify that BillPaymentScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing BillPaymentScreen.js i18n Implementation\n');

// Read the BillPaymentScreen.js file
const billPaymentScreenPath = path.join(__dirname, '../screens/BillPaymentScreen.js');
const billPaymentScreenContent = fs.readFileSync(billPaymentScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = billPaymentScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = billPaymentScreenContent.includes('const { t }') || billPaymentScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded Alert.alert strings replacement
console.log('\n✅ Test 3: Hardcoded Alert.alert strings replacement');
const hardcodedAlertStrings = [
  'Error',
  'Failed to load bill payment options. Please try again.'
];

let hardcodedAlertsFound = [];
hardcodedAlertStrings.forEach(str => {
  // Check if the string appears in Alert.alert calls (not in translation keys)
  const alertPattern = new RegExp(`Alert\\.alert\\([^)]*['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (alertPattern.test(billPaymentScreenContent)) {
    hardcodedAlertsFound.push(str);
  }
});

if (hardcodedAlertsFound.length === 0) {
  console.log('   All hardcoded Alert.alert strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Alert.alert strings still found: ❌ FAIL');
  hardcodedAlertsFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 4: Check for hardcoded placeholder text
console.log('\n✅ Test 4: Hardcoded placeholder text');
const hardcodedPlaceholders = [
  'Enter Paybill name'
];

let hardcodedPlaceholdersFound = [];
hardcodedPlaceholders.forEach(str => {
  // Check if the string appears as hardcoded placeholder (not in translation key)
  const placeholderPattern = new RegExp(`placeholder=['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (placeholderPattern.test(billPaymentScreenContent)) {
    hardcodedPlaceholdersFound.push(str);
  }
});

if (hardcodedPlaceholdersFound.length === 0) {
  console.log('   All hardcoded placeholder text replaced: ✅ PASS');
} else {
  console.log('   Hardcoded placeholder text still found: ❌ FAIL');
  hardcodedPlaceholdersFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 5: Check for hardcoded category titles
console.log('\n✅ Test 5: Hardcoded category titles');
const hardcodedCategoryTitles = [
  'School Fees',
  'Education payments'
];

let hardcodedCategoriesFound = [];
hardcodedCategoryTitles.forEach(str => {
  // Check if the string appears as hardcoded title (not in translation key)
  const titlePattern = new RegExp(`title:\\s*['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  const subtitlePattern = new RegExp(`subtitle:\\s*['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (titlePattern.test(billPaymentScreenContent) || subtitlePattern.test(billPaymentScreenContent)) {
    hardcodedCategoriesFound.push(str);
  }
});

if (hardcodedCategoriesFound.length === 0) {
  console.log('   All hardcoded category titles replaced: ✅ PASS');
} else {
  console.log('   Hardcoded category titles still found: ❌ FAIL');
  hardcodedCategoriesFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 6: Check for proper translation key usage
console.log('\n✅ Test 6: Translation key usage');
const translationKeys = [
  't(\'bills.failedToLoadBillPaymentOptions\')',
  't(\'bills.enterPaybillName\')',
  't(\'bills.schoolFees\')',
  't(\'bills.educationPayments\')',
  't(\'bills.payBill\')',
  't(\'bills.categories\')',
  't(\'bills.viewAll\')',
  't(\'bills.showLess\')'
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (billPaymentScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check that required translation keys exist in en.js
console.log('\n✅ Test 7: Translation keys in en.js');
const requiredKeys = [
  'failedToLoadBillPaymentOptions:',
  'enterPaybillName:',
  'schoolFees:',
  'educationPayments:',
  'payBill:',
  'categories:',
  'viewAll:',
  'showLess:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 8: Check for Alert.alert with proper translation keys
console.log('\n✅ Test 8: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = billPaymentScreenContent.match(alertPattern);
const totalAlerts = (billPaymentScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 9: Check for placeholder text internationalization
console.log('\n✅ Test 9: Placeholder text internationalization');
const placeholderPattern = /placeholder=\{t\(/g;
const placeholderMatches = billPaymentScreenContent.match(placeholderPattern);
const totalPlaceholders = (billPaymentScreenContent.match(/placeholder=/g) || []).length;
const translatedPlaceholders = placeholderMatches ? placeholderMatches.length : 0;

console.log(`   Translated placeholders: ${translatedPlaceholders}/${totalPlaceholders} ${translatedPlaceholders === totalPlaceholders ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 10: Check for bills-specific translation usage
console.log('\n✅ Test 10: Bills-specific translations');
const billsTranslations = [
  't(\'bills.electricity\')',
  't(\'bills.airtime\')',
  't(\'bills.financial\')',
  't(\'bills.postpaid\')',
  't(\'bills.solar\')',
  't(\'bills.tv\')',
  't(\'bills.governmentServices\')'
];

let billsKeysFound = 0;
billsTranslations.forEach(key => {
  if (billPaymentScreenContent.includes(key)) {
    billsKeysFound++;
  }
});

console.log(`   Bills translation keys found: ${billsKeysFound}/${billsTranslations.length} ${billsKeysFound === billsTranslations.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 10;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (hardcodedAlertsFound.length === 0) passedTests++;
if (hardcodedPlaceholdersFound.length === 0) passedTests++;
if (hardcodedCategoriesFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (translatedPlaceholders === totalPlaceholders) passedTests++;
if (billsKeysFound === billsTranslations.length) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 BillPaymentScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
} else {
  console.log('\n⚠️  BillPaymentScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test bill payment flow with different languages');
console.log('2. Verify category selection and provider listings');
console.log('3. Test search functionality and empty states');
console.log('4. Proceed to Transfer and Payment Confirmation Flows');
