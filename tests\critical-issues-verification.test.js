/**
 * Critical Issues Verification Test
 * 
 * Tests the fixes for:
 * 1. Translation system failure (LanguageProvider missing)
 * 2. Device registration duplicate key constraint violation
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 CRITICAL ISSUES VERIFICATION TEST\n');

// Test 1: Translation System Fix Verification
console.log('1️⃣ TRANSLATION SYSTEM FIX VERIFICATION:');
console.log('==========================================');

try {
  // Check if App.js has LanguageProvider
  const appJsPath = path.join(__dirname, '../App.js');
  const appJsContent = fs.readFileSync(appJsPath, 'utf8');
  
  const hasLanguageProviderImport = appJsContent.includes("import { LanguageProvider }");
  const hasLanguageProviderUsage = appJsContent.includes("<LanguageProvider>");
  const hasCurrencyProviderImport = appJsContent.includes("import { CurrencyProvider }");
  const hasCurrencyProviderUsage = appJsContent.includes("<CurrencyProvider>");
  
  console.log(`   ✅ App.js LanguageProvider import: ${hasLanguageProviderImport ? '✅ FOUND' : '❌ MISSING'}`);
  console.log(`   ✅ App.js LanguageProvider usage: ${hasLanguageProviderUsage ? '✅ FOUND' : '❌ MISSING'}`);
  console.log(`   ✅ App.js CurrencyProvider import: ${hasCurrencyProviderImport ? '✅ FOUND' : '❌ MISSING'}`);
  console.log(`   ✅ App.js CurrencyProvider usage: ${hasCurrencyProviderUsage ? '✅ FOUND' : '❌ MISSING'}`);
  
  // Test translation files can be imported
  delete require.cache[require.resolve('../locales/en.js')];
  delete require.cache[require.resolve('../locales/sw.js')];
  
  const enTranslations = require('../locales/en.js').default;
  const swTranslations = require('../locales/sw.js').default;
  
  console.log(`   ✅ English translations loaded: ${Object.keys(enTranslations).length} sections`);
  console.log(`   ✅ Swahili translations loaded: ${Object.keys(swTranslations).length} sections`);
  
  // Test critical translation keys
  const criticalKeys = ['editProfile', 'fullName', 'emailAddress', 'accountVerification', 'privacyAndData'];
  let translationIssues = 0;
  
  console.log('\n   🔑 Critical Translation Keys Test:');
  criticalKeys.forEach(key => {
    const enHasKey = enTranslations[key] !== undefined;
    const swHasKey = swTranslations[key] !== undefined;
    
    if (enHasKey && swHasKey) {
      console.log(`      ✅ ${key}: EN="${enTranslations[key]}" | SW="${swTranslations[key]}"`);
    } else {
      console.log(`      ❌ ${key}: EN=${enHasKey ? '✅' : '❌'} | SW=${swHasKey ? '✅' : '❌'}`);
      translationIssues++;
    }
  });
  
  const translationSystemFixed = hasLanguageProviderImport && hasLanguageProviderUsage && 
                                 hasCurrencyProviderImport && hasCurrencyProviderUsage && 
                                 translationIssues === 0;
  
  console.log(`\n   📊 Translation System Status: ${translationSystemFixed ? '✅ FIXED' : '❌ NEEDS WORK'}`);
  
} catch (error) {
  console.log(`   ❌ Translation system test failed: ${error.message}`);
}

// Test 2: Device Registration Fix Verification
console.log('\n2️⃣ DEVICE REGISTRATION FIX VERIFICATION:');
console.log('=========================================');

try {
  // Check if deviceManagementService.js has the fixes
  const deviceServicePath = path.join(__dirname, '../services/deviceManagementService.js');
  const deviceServiceContent = fs.readFileSync(deviceServicePath, 'utf8');
  
  const hasMaybeSingleFix = deviceServiceContent.includes('.maybeSingle()');
  const hasDuplicateKeyHandling = deviceServiceContent.includes('23505') && 
                                  deviceServiceContent.includes('user_device_user_id_device_id_key');
  const hasRaceConditionHandling = deviceServiceContent.includes('race condition');
  
  console.log(`   ✅ Uses .maybeSingle() instead of .single(): ${hasMaybeSingleFix ? '✅ FIXED' : '❌ MISSING'}`);
  console.log(`   ✅ Handles duplicate key constraint (23505): ${hasDuplicateKeyHandling ? '✅ FIXED' : '❌ MISSING'}`);
  console.log(`   ✅ Handles race condition scenarios: ${hasRaceConditionHandling ? '✅ FIXED' : '❌ MISSING'}`);
  
  // Check JiraniPay version too
  const jiranipayDeviceServicePath = path.join(__dirname, '../JiraniPay/services/deviceManagementService.js');
  if (fs.existsSync(jiranipayDeviceServicePath)) {
    const jiranipayDeviceServiceContent = fs.readFileSync(jiranipayDeviceServicePath, 'utf8');
    
    const jiranipayHasMaybeSingleFix = jiranipayDeviceServiceContent.includes('.maybeSingle()');
    const jiranipayHasDuplicateKeyHandling = jiranipayDeviceServiceContent.includes('23505') && 
                                            jiranipayDeviceServiceContent.includes('user_device_user_id_device_id_key');
    
    console.log(`   ✅ JiraniPay version also fixed: ${jiranipayHasMaybeSingleFix && jiranipayHasDuplicateKeyHandling ? '✅ FIXED' : '❌ MISSING'}`);
  }
  
  const deviceRegistrationFixed = hasMaybeSingleFix && hasDuplicateKeyHandling && hasRaceConditionHandling;
  
  console.log(`\n   📊 Device Registration Status: ${deviceRegistrationFixed ? '✅ FIXED' : '❌ NEEDS WORK'}`);
  
} catch (error) {
  console.log(`   ❌ Device registration test failed: ${error.message}`);
}

// Test 3: App Structure Verification
console.log('\n3️⃣ APP STRUCTURE VERIFICATION:');
console.log('===============================');

try {
  // Check if we're using the correct App.js
  const rootAppExists = fs.existsSync(path.join(__dirname, '../App.js'));
  const jiranipayAppExists = fs.existsSync(path.join(__dirname, '../JiraniPay/App.js'));
  const indexJsContent = fs.readFileSync(path.join(__dirname, '../index.js'), 'utf8');
  
  console.log(`   ✅ Root App.js exists: ${rootAppExists ? '✅ YES' : '❌ NO'}`);
  console.log(`   ✅ JiraniPay/App.js exists: ${jiranipayAppExists ? '✅ YES' : '❌ NO'}`);
  console.log(`   ✅ index.js imports from: ${indexJsContent.includes('./App') ? '✅ ./App (correct)' : '❌ Wrong path'}`);
  
  if (rootAppExists && jiranipayAppExists) {
    console.log('   ⚠️  WARNING: Two App.js files exist - ensure the correct one is being used');
  }
  
} catch (error) {
  console.log(`   ❌ App structure test failed: ${error.message}`);
}

// Summary and Next Steps
console.log('\n📋 SUMMARY AND NEXT STEPS:');
console.log('===========================');

console.log('\n🎯 EXPECTED RESULTS AFTER FIXES:');
console.log('1. Translation System:');
console.log('   - Edit Profile should show "Edit Profile" (EN) or "Hariri Wasifu" (SW)');
console.log('   - Form fields should show "Full Name" (EN) or "Jina Kamili" (SW)');
console.log('   - Account Verification should show "Account Verification" (EN) or "Uthibitishaji wa Akaunti" (SW)');
console.log('   - Privacy & Data should show "Privacy & Data" (EN) or "Faragha na Taarifa" (SW)');

console.log('\n2. Device Registration:');
console.log('   - No more "duplicate key value violates unique constraints" errors');
console.log('   - Smooth authentication flow without database errors');
console.log('   - Proper handling of multiple login attempts from same device');

console.log('\n🚀 TESTING INSTRUCTIONS:');
console.log('1. Clear Metro cache: npx expo start --clear');
console.log('2. Clear Expo Go app data on device');
console.log('3. Restart the app');
console.log('4. Test authentication (should not show device registration errors)');
console.log('5. Test language switching (should show proper translations)');
console.log('6. Navigate to Edit Profile, Account Verification, and Privacy & Data screens');

console.log('\n✨ If both fixes are working:');
console.log('   - Translations will appear properly in both English and Swahili');
console.log('   - Authentication will work without database errors');
console.log('   - App will be fully functional for testing and production use');

console.log('\n🔧 If issues persist:');
console.log('   - Check console logs for specific error messages');
console.log('   - Verify database schema is properly set up');
console.log('   - Ensure app is running in production mode for device registration');
console.log('   - Check that LanguageProvider is properly initialized');
