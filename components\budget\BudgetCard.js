/**
 * Budget Card Component
 * Displays budget overview with progress and performance metrics
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

// Constants
import { Colors } from '../../constants/Colors';
import { useLanguage } from '../../contexts/LanguageContext';

const BudgetCard = ({ budget, onPress, theme, formatCurrency }) => {
  const { t } = useLanguage();
  const performance = budget.performance || {};
  const categories = Array.isArray(budget.budget_categories) ? budget.budget_categories : [];
  
  // Calculate progress
  const utilizationRate = performance.utilizationRate || 0;
  const progressPercentage = Math.min(utilizationRate * 100, 100);
  
  // Determine status color
  const getStatusColor = () => {
    if (utilizationRate > 1) return Colors.status.error;
    if (utilizationRate > 0.8) return Colors.status.warning;
    return Colors.status.success;
  };

  // Get status text
  const getStatusText = () => {
    if (utilizationRate > 1) return t('budget.overBudget');
    if (utilizationRate > 0.8) return t('budget.onTrack');
    return t('budget.underBudget');
  };

  // Format budget period
  const formatBudgetPeriod = () => {
    const startDate = new Date(budget.start_date);
    const endDate = budget.end_date ? new Date(budget.end_date) : null;
    
    if (endDate) {
      return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
    }
    
    return t('budget.since', { date: startDate.toLocaleDateString() });
  };

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: theme.colors.surface }]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={[styles.budgetName, { color: theme.colors.text }]}>
            {budget.name}
          </Text>
          <Text style={[styles.budgetPeriod, { color: theme.colors.textSecondary }]}>
            {formatBudgetPeriod()}
          </Text>
        </View>
        <View style={styles.headerRight}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor() + '20' }]}>
            <Text style={[styles.statusText, { color: getStatusColor() }]}>
              {getStatusText()}
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </View>
      </View>

      {/* Budget Amount */}
      <View style={styles.amountContainer}>
        <View style={styles.amountRow}>
          <Text style={[styles.amountLabel, { color: theme.colors.textSecondary }]}>
            Spent
          </Text>
          <Text style={[styles.amountValue, { color: theme.colors.text }]}>
            {formatCurrency(performance.totalSpent || 0, 'UGX')}
          </Text>
        </View>
        <View style={styles.amountRow}>
          <Text style={[styles.amountLabel, { color: theme.colors.textSecondary }]}>
            Budget
          </Text>
          <Text style={[styles.amountValue, { color: theme.colors.text }]}>
            {formatCurrency(budget.total_amount || 0, 'UGX')}
          </Text>
        </View>
      </View>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={[styles.progressTrack, { backgroundColor: theme.colors.border }]}>
          <LinearGradient
            colors={[getStatusColor(), getStatusColor() + '80']}
            style={[
              styles.progressFill,
              { width: `${Math.min(progressPercentage, 100)}%` }
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          />
        </View>
        <Text style={[styles.progressText, { color: theme.colors.textSecondary }]}>
          {progressPercentage.toFixed(1)}% used
        </Text>
      </View>

      {/* Categories Summary */}
      <View style={styles.categoriesContainer}>
        <Text style={[styles.categoriesTitle, { color: theme.colors.text }]}>
          Categories: {categories.length}
        </Text>
        <View style={styles.categoriesStats}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: Colors.status.error }]}>
              {performance.categoriesOverBudget || 0}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
              Over
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: Colors.status.success }]}>
              {performance.categoriesOnTrack || 0}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
              On Track
            </Text>
          </View>
        </View>
      </View>

      {/* Description */}
      {budget.description && (
        <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
          {budget.description}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  budgetName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  budgetPeriod: {
    fontSize: 14,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  amountContainer: {
    marginBottom: 16,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  amountLabel: {
    fontSize: 14,
  },
  amountValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressTrack: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    textAlign: 'center',
  },
  categoriesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoriesTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  categoriesStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  statLabel: {
    fontSize: 12,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    fontStyle: 'italic',
  },
});

export default BudgetCard;
