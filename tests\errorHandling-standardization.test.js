/**
 * Error Handling Message Standardization Test
 * 
 * Tests to verify that all Alert.alert calls across the application use proper translation keys
 * and follow consistent error message patterns
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

console.log('🧪 Testing Error Handling Message Standardization\n');

// Get all JavaScript files in screens directory
const screenFiles = glob.sync('screens/*.js', { cwd: path.join(__dirname, '..') }).map(file => path.join(__dirname, '..', file));
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

let totalTests = 0;
let passedTests = 0;
let allAlertCalls = [];
let hardcodedAlerts = [];
let translatedAlerts = [];

console.log(`📁 Analyzing ${screenFiles.length} screen files...\n`);

// Test 1: Analyze all Alert.alert calls
console.log('✅ Test 1: Alert.alert call analysis');
screenFiles.forEach(filePath => {
  const fileName = path.basename(filePath);
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Find all Alert.alert calls
  const alertPattern = /Alert\.alert\([^)]+\)/g;
  const alerts = content.match(alertPattern) || [];
  
  alerts.forEach(alert => {
    allAlertCalls.push({ file: fileName, alert });
    
    // Check if alert uses translation keys
    if (alert.includes('t(')) {
      translatedAlerts.push({ file: fileName, alert });
    } else {
      hardcodedAlerts.push({ file: fileName, alert });
    }
  });
  
  const alertCount = alerts.length;
  const translatedCount = alerts.filter(alert => alert.includes('t(')).length;
  const translationRate = alertCount > 0 ? (translatedCount / alertCount * 100).toFixed(1) : 100;
  
  console.log(`   ${fileName}: ${translatedCount}/${alertCount} alerts translated (${translationRate}%)`);
});

const overallTranslationRate = allAlertCalls.length > 0 ? (translatedAlerts.length / allAlertCalls.length * 100).toFixed(1) : 100;
console.log(`\n   📊 Overall: ${translatedAlerts.length}/${allAlertCalls.length} alerts translated (${overallTranslationRate}%)`);

totalTests++;
if (hardcodedAlerts.length === 0) passedTests++;

// Test 2: Check for consistent error message patterns
console.log('\n✅ Test 2: Error message pattern consistency');
const expectedErrorPatterns = [
  't(\'common.errorTitle\')',
  't(\'error\')',
  't(\'common.error\')',
  't(\'errors.'
];

const expectedSuccessPatterns = [
  't(\'success\')',
  't(\'common.success\')',
  't(\'success.'
];

let errorPatternUsage = 0;
let successPatternUsage = 0;

translatedAlerts.forEach(({ alert }) => {
  expectedErrorPatterns.forEach(pattern => {
    if (alert.includes(pattern)) errorPatternUsage++;
  });
  
  expectedSuccessPatterns.forEach(pattern => {
    if (alert.includes(pattern)) successPatternUsage++;
  });
});

console.log(`   Error pattern usage: ${errorPatternUsage} instances`);
console.log(`   Success pattern usage: ${successPatternUsage} instances`);

const hasConsistentPatterns = errorPatternUsage > 0 && successPatternUsage > 0;
console.log(`   Pattern consistency: ${hasConsistentPatterns ? '✅ PASS' : '❌ FAIL'}`);

totalTests++;
if (hasConsistentPatterns) passedTests++;

// Test 3: Check for proper error categorization
console.log('\n✅ Test 3: Error categorization');
const errorCategories = [
  'auth.',
  'wallet.',
  'bills.',
  'transfer.',
  'verification.',
  'security.',
  'twoFactor.',
  'sendMoney.',
  'dashboard.',
  'profile.'
];

let categorizedErrors = 0;
translatedAlerts.forEach(({ alert }) => {
  errorCategories.forEach(category => {
    if (alert.includes(`'${category}`) || alert.includes(`"${category}`)) {
      categorizedErrors++;
    }
  });
});

const categorizationRate = translatedAlerts.length > 0 ? (categorizedErrors / translatedAlerts.length * 100).toFixed(1) : 100;
console.log(`   Categorized errors: ${categorizedErrors}/${translatedAlerts.length} (${categorizationRate}%)`);

const hasGoodCategorization = categorizationRate >= 80;
console.log(`   Categorization quality: ${hasGoodCategorization ? '✅ PASS' : '❌ FAIL'}`);

totalTests++;
if (hasGoodCategorization) passedTests++;

// Test 4: Check for required error message keys in en.js
console.log('\n✅ Test 4: Required error message keys');
const requiredErrorKeys = [
  'errorTitle:',
  'error:',
  'success:',
  'networkError:',
  'validationError:',
  'systemError:',
  'authenticationError:',
  'permissionError:',
  'timeoutError:'
];

let foundErrorKeys = 0;
requiredErrorKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    foundErrorKeys++;
  }
});

const errorKeyCompleteness = (foundErrorKeys / requiredErrorKeys.length * 100).toFixed(1);
console.log(`   Required error keys: ${foundErrorKeys}/${requiredErrorKeys.length} (${errorKeyCompleteness}%)`);

const hasRequiredKeys = foundErrorKeys >= requiredErrorKeys.length * 0.8;
console.log(`   Error key completeness: ${hasRequiredKeys ? '✅ PASS' : '❌ FAIL'}`);

totalTests++;
if (hasRequiredKeys) passedTests++;

// Test 5: Check for string interpolation in error messages
console.log('\n✅ Test 5: String interpolation in error messages');
const interpolationPattern = /t\([^)]+,\s*\{[^}]+\}/g;
let interpolatedAlerts = 0;

translatedAlerts.forEach(({ alert }) => {
  if (interpolationPattern.test(alert)) {
    interpolatedAlerts++;
  }
});

const interpolationUsage = translatedAlerts.length > 0 ? (interpolatedAlerts / translatedAlerts.length * 100).toFixed(1) : 0;
console.log(`   Alerts with interpolation: ${interpolatedAlerts}/${translatedAlerts.length} (${interpolationUsage}%)`);

const hasGoodInterpolation = interpolatedAlerts > 0;
console.log(`   String interpolation usage: ${hasGoodInterpolation ? '✅ PASS' : '❌ FAIL'}`);

totalTests++;
if (hasGoodInterpolation) passedTests++;

// Test 6: Check for proper button text in alerts
console.log('\n✅ Test 6: Alert button text internationalization');
const buttonPatterns = [
  't(\'common.ok\')',
  't(\'common.cancel\')',
  't(\'common.retry\')',
  't(\'common.yes\')',
  't(\'common.no\')'
];

let translatedButtons = 0;
let totalButtons = 0;

allAlertCalls.forEach(({ alert }) => {
  // Count button text occurrences
  const buttonMatches = alert.match(/text:\s*['"][^'"]+['"]/g) || [];
  totalButtons += buttonMatches.length;
  
  buttonPatterns.forEach(pattern => {
    if (alert.includes(pattern)) {
      translatedButtons++;
    }
  });
});

const buttonTranslationRate = totalButtons > 0 ? (translatedButtons / totalButtons * 100).toFixed(1) : 100;
console.log(`   Translated button text: ${translatedButtons}/${totalButtons} (${buttonTranslationRate}%)`);

const hasGoodButtonTranslation = buttonTranslationRate >= 70;
console.log(`   Button text quality: ${hasGoodButtonTranslation ? '✅ PASS' : '❌ FAIL'}`);

totalTests++;
if (hasGoodButtonTranslation) passedTests++;

// Display hardcoded alerts that need fixing
if (hardcodedAlerts.length > 0) {
  console.log('\n⚠️  Hardcoded Alert.alert calls found:');
  hardcodedAlerts.forEach(({ file, alert }, index) => {
    console.log(`   ${index + 1}. ${file}: ${alert.substring(0, 80)}...`);
  });
}

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);
console.log(`Alert translation rate: ${overallTranslationRate}%`);
console.log(`Error categorization rate: ${categorizationRate}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 Error Handling Message Standardization: ✅ COMPLETE');
  console.log('   All Alert.alert calls use proper translation keys!');
  console.log('   Error message patterns are consistent across the application!');
} else {
  console.log('\n⚠️  Error Handling Message Standardization: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Recommendations:');
console.log('1. Replace remaining hardcoded Alert.alert calls with translation keys');
console.log('2. Ensure consistent error categorization (auth.*, wallet.*, bills.*, etc.)');
console.log('3. Add string interpolation for dynamic error messages');
console.log('4. Standardize button text in all alert dialogs');
console.log('5. Phase 2 Core Features implementation is ready for completion!');
