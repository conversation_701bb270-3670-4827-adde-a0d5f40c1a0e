/**
 * InvestmentAnalyticsScreen i18n Implementation Test
 * 
 * Tests to verify that InvestmentAnalyticsScreen.js has been properly internationalized
 * and all hardcoded strings have been replaced with translation keys
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing InvestmentAnalyticsScreen.js i18n Implementation\n');

// Read the InvestmentAnalyticsScreen.js file
const investmentAnalyticsScreenPath = path.join(__dirname, '../screens/InvestmentAnalyticsScreen.js');
const investmentAnalyticsScreenContent = fs.readFileSync(investmentAnalyticsScreenPath, 'utf8');

// Read the en.js translation file
const enTranslationsPath = path.join(__dirname, '../locales/en.js');
const enTranslationsContent = fs.readFileSync(enTranslationsPath, 'utf8');

// Test 1: Check that useLanguage hook is imported
console.log('✅ Test 1: useLanguage hook import');
const hasUseLanguageImport = investmentAnalyticsScreenContent.includes("import { useLanguage } from '../contexts/LanguageContext'");
console.log(`   useLanguage imported: ${hasUseLanguageImport ? '✅ PASS' : '❌ FAIL'}`);

// Test 2: Check that t function is destructured
console.log('\n✅ Test 2: t function destructuring');
const hasTFunction = investmentAnalyticsScreenContent.includes('const { t }') || investmentAnalyticsScreenContent.includes('const { t,');
console.log(`   t function destructured: ${hasTFunction ? '✅ PASS' : '❌ FAIL'}`);

// Test 3: Check for hardcoded Alert.alert strings replacement
console.log('\n✅ Test 3: Alert.alert internationalization');
const alertPattern = /Alert\.alert\([^)]*t\(/g;
const alertMatches = investmentAnalyticsScreenContent.match(alertPattern);
const totalAlerts = (investmentAnalyticsScreenContent.match(/Alert\.alert\(/g) || []).length;
const translatedAlerts = alertMatches ? alertMatches.length : 0;

console.log(`   Translated Alert.alert calls: ${translatedAlerts}/${totalAlerts} ${translatedAlerts === totalAlerts ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 4: Check for hardcoded Text component strings
console.log('\n✅ Test 4: Hardcoded Text component strings');
const hardcodedTextStrings = [
  'Investment Analytics',
  'Performance Metrics',
  'Total Return',
  'Annualized Return',
  'Volatility',
  'Sharpe Ratio',
  'Max Drawdown',
  'Win Rate',
  'Risk Analysis',
  'Best Day',
  'Worst Day',
  'Total Trades',
  'Avg Holding',
  'Portfolio Beta',
  'Sector Allocation',
  'Asset Allocation',
  'Advanced Analytics',
  'Loading analytics...'
];

let hardcodedTextFound = [];
hardcodedTextStrings.forEach(str => {
  const textPattern = new RegExp(`<Text[^>]*>\\s*${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*</Text>`, 'g');
  const directTextPattern = new RegExp(`['"\`]${str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
  if (textPattern.test(investmentAnalyticsScreenContent) || 
      (directTextPattern.test(investmentAnalyticsScreenContent) && !investmentAnalyticsScreenContent.includes(`t('investmentAnalytics.`))) {
    hardcodedTextFound.push(str);
  }
});

if (hardcodedTextFound.length === 0) {
  console.log('   All hardcoded Text strings replaced: ✅ PASS');
} else {
  console.log('   Hardcoded Text strings still found: ❌ FAIL');
  hardcodedTextFound.forEach(str => console.log(`     - "${str}"`));
}

// Test 5: Check for proper translation key usage
console.log('\n✅ Test 5: Translation key usage');
const translationKeys = [
  't(\'investmentAnalytics.title\')',
  't(\'investmentAnalytics.performanceMetrics\')',
  't(\'investmentAnalytics.totalReturn\')',
  't(\'investmentAnalytics.annualizedReturn\')',
  't(\'investmentAnalytics.volatility\')',
  't(\'investmentAnalytics.sharpeRatio\')',
  't(\'investmentAnalytics.maxDrawdown\')',
  't(\'investmentAnalytics.winRate\')',
  't(\'investmentAnalytics.riskAnalysis\')',
  't(\'investmentAnalytics.bestDay\')',
  't(\'investmentAnalytics.worstDay\')',
  't(\'investmentAnalytics.totalTrades\')',
  't(\'investmentAnalytics.avgHolding\')',
  't(\'investmentAnalytics.portfolioBeta\')',
  't(\'investmentAnalytics.sectorAllocation\')',
  't(\'investmentAnalytics.assetAllocation\')',
  't(\'investmentAnalytics.advancedAnalytics\')',
  't(\'investmentAnalytics.loadingAnalytics\')'
];

let keysFound = 0;
translationKeys.forEach(key => {
  if (investmentAnalyticsScreenContent.includes(key)) {
    keysFound++;
  }
});

console.log(`   Translation keys found: ${keysFound}/${translationKeys.length} ${keysFound === translationKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 6: Check for export functionality internationalization
console.log('\n✅ Test 6: Export functionality internationalization');
const exportKeys = [
  't(\'investmentAnalytics.exportAnalytics\')',
  't(\'investmentAnalytics.chooseExportFormat\')',
  't(\'investmentAnalytics.pdfReport\')',
  't(\'investmentAnalytics.excelData\')',
  't(\'investmentAnalytics.exportSuccessful\')',
  't(\'investmentAnalytics.exportFailed\')'
];

let exportKeysFound = 0;
exportKeys.forEach(key => {
  if (investmentAnalyticsScreenContent.includes(key)) {
    exportKeysFound++;
  }
});

console.log(`   Export translation keys: ${exportKeysFound}/${exportKeys.length} ${exportKeysFound === exportKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 7: Check that required translation keys exist in en.js
console.log('\n✅ Test 7: Translation keys in en.js');
const requiredKeys = [
  'title:',
  'performanceMetrics:',
  'totalReturn:',
  'annualizedReturn:',
  'volatility:',
  'sharpeRatio:',
  'maxDrawdown:',
  'winRate:',
  'riskAnalysis:',
  'bestDay:',
  'worstDay:',
  'totalTrades:',
  'avgHolding:',
  'portfolioBeta:',
  'sectorAllocation:',
  'assetAllocation:',
  'advancedAnalytics:',
  'exportAnalytics:',
  'chooseExportFormat:',
  'pdfReport:',
  'excelData:',
  'exportSuccessful:',
  'exportFailed:',
  'loadingAnalytics:',
  'failedToLoadAnalytics:'
];

let keysInTranslations = 0;
requiredKeys.forEach(key => {
  if (enTranslationsContent.includes(key)) {
    keysInTranslations++;
  }
});

console.log(`   Required keys in en.js: ${keysInTranslations}/${requiredKeys.length} ${keysInTranslations === requiredKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Test 8: Check investmentAnalytics section exists in en.js
console.log('\n✅ Test 8: InvestmentAnalytics section in en.js');
const investmentAnalyticsSection = enTranslationsContent.includes('investmentAnalytics: {');
console.log(`   InvestmentAnalytics section exists: ${investmentAnalyticsSection ? '✅ PASS' : '❌ FAIL'}`);

// Test 9: Check for portfolio analytics and market data internationalization
console.log('\n✅ Test 9: Portfolio analytics and market data');
const portfolioKeys = [
  't(\'investmentAnalytics.sectorAllocation\')',
  't(\'investmentAnalytics.assetAllocation\')',
  't(\'investmentAnalytics.riskAnalysis\')',
  't(\'investmentAnalytics.advancedAnalytics\')'
];

let portfolioKeysFound = 0;
portfolioKeys.forEach(key => {
  if (investmentAnalyticsScreenContent.includes(key)) {
    portfolioKeysFound++;
  }
});

console.log(`   Portfolio analytics keys: ${portfolioKeysFound}/${portfolioKeys.length} ${portfolioKeysFound === portfolioKeys.length ? '✅ PASS' : '❌ PARTIAL'}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('==========');
const totalTests = 9;
let passedTests = 0;

if (hasUseLanguageImport) passedTests++;
if (hasTFunction) passedTests++;
if (translatedAlerts === totalAlerts) passedTests++;
if (hardcodedTextFound.length === 0) passedTests++;
if (keysFound === translationKeys.length) passedTests++;
if (exportKeysFound === exportKeys.length) passedTests++;
if (keysInTranslations === requiredKeys.length) passedTests++;
if (investmentAnalyticsSection) passedTests++;
if (portfolioKeysFound === portfolioKeys.length) passedTests++;

console.log(`Tests passed: ${passedTests}/${totalTests}`);
console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 InvestmentAnalyticsScreen.js i18n implementation: ✅ COMPLETE');
  console.log('   All hardcoded strings have been successfully replaced with translation keys!');
  console.log('   Portfolio analytics, market data, and investment insights are internationalized!');
} else {
  console.log('\n⚠️  InvestmentAnalyticsScreen.js i18n implementation: 🔄 IN PROGRESS');
  console.log('   Some issues need to be addressed before completion.');
}

console.log('\n🔍 Next Steps:');
console.log('1. Test investment analytics with different languages');
console.log('2. Verify portfolio data displays correctly');
console.log('3. Test export functionality in multiple languages');
console.log('4. Proceed to BudgetInsightsScreen.js implementation');
